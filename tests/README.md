# Тесты MagicUniq

Этот каталог содержит комплексные тесты для всех модулей проекта MagicUniq.

## Структура тестов

```
tests/
├── __init__.py              # Инициализация тестового пакета
├── conftest.py              # Общие fixtures и настройки pytest
├── fixtures/                # Тестовые данные и fixtures
├── unit/                    # Unit тесты
│   ├── test_config_*.py     # Тесты модулей конфигурации
│   ├── test_core_*.py       # Тесты основных модулей
│   ├── test_utils_*.py      # Тесты утилит
│   ├── test_services_*.py   # Тесты сервисов
│   └── test_interfaces_*.py # Тесты интерфейсов
├── integration/             # Интеграционные тесты
│   └── test_module_integration.py
└── README.md               # Этот файл
```

## Покрытие модулей

### ✅ Config модули
- `test_config_settings.py` - Тесты для `magic_uniq.config.settings`
- `test_config_validation.py` - Тесты для `magic_uniq.config.validation`
- `test_config_video_settings.py` - Тесты для `magic_uniq.config.video_settings`

### ✅ Utils модули
- `test_utils_file_utils.py` - Тесты для `magic_uniq.utils.file_utils`
- `test_utils_image_utils.py` - Тесты для `magic_uniq.utils.image_utils`
- `test_utils_system_utils.py` - Тесты для `magic_uniq.utils.system_utils`

### ✅ Services модули
- `test_services_content_generator.py` - Тесты для `magic_uniq.services.content_generator`
- `test_services_image_server.py` - Тесты для `magic_uniq.services.image_server`

### ✅ Core модули
- `test_core_classes.py` - Тесты для `magic_uniq.core.classes`
- `test_core_video_processor.py` - Тесты для `magic_uniq.core.video_processor`

### ✅ Integration тесты
- `test_module_integration.py` - Тесты взаимодействия между модулями

## Запуск тестов

### Быстрый старт

```bash
# Установка зависимостей для тестирования
pip install pytest pytest-cov pytest-asyncio

# Запуск всех тестов
python run_tests.py

# Или через pytest напрямую
pytest tests/
```

### Различные режимы запуска

```bash
# Unit тесты
python run_tests.py --unit

# Интеграционные тесты
python run_tests.py --integration

# Тесты с покрытием кода
python run_tests.py --coverage

# Тесты конкретного модуля
python run_tests.py --module config
python run_tests.py --module utils
python run_tests.py --module services

# Подробный вывод
python run_tests.py --verbose

# Проверка стиля кода
python run_tests.py --lint

# Проверка зависимостей
python run_tests.py --check-deps
```

### Запуск через pytest с маркерами

```bash
# Только unit тесты
pytest -m unit

# Только интеграционные тесты
pytest -m integration

# Тесты конкретного компонента
pytest -m config
pytest -m utils
pytest -m services
pytest -m core

# Медленные тесты
pytest -m slow

# Тесты требующие сеть
pytest -m network
```

## Fixtures и утилиты

### Основные fixtures (conftest.py)

- `temp_dir` - Временная директория для тестов
- `test_data_dir` - Директория с тестовой структурой данных
- `sample_config` - Пример конфигурации
- `config_file` - Временный файл конфигурации
- `mock_image` - Mock объект PIL Image
- `sample_image_file` - Тестовый файл изображения
- `sample_video_file` - Тестовый файл видео
- `mock_settings` - Mock объект Settings
- `mock_requests` - Mock для HTTP запросов
- `mock_system_uuid` - Mock для system UUID
- `test_helper` - Вспомогательные функции для тестов

### Вспомогательный класс TestHelper

```python
# Создание тестовых данных изображения
image_data = TestHelper.create_test_image_data()

# Создание тестовых параметров видео
video_params = TestHelper.create_test_video_params()
```

## Особенности тестирования

### Мокирование зависимостей

Тесты используют мокирование для избежания зависимостей от внешних библиотек:

```python
# Мокирование PIL для тестов изображений
with patch.dict('sys.modules', {
    'PIL': Mock(),
    'PIL.Image': Mock(),
    'PIL.ImageOps': Mock()
}):
    from magic_uniq.utils.image_utils import ImageUtils
```

### Асинхронные тесты

Для тестирования асинхронного кода используется `pytest-asyncio`:

```python
@pytest.mark.asyncio
async def test_async_function():
    result = await some_async_function()
    assert result is not None
```

### Временные файлы и директории

Все тесты используют временные директории для избежания конфликтов:

```python
def test_file_operations(temp_dir):
    test_file = temp_dir / "test.txt"
    test_file.write_text("content")
    assert test_file.exists()
    # Файл автоматически удалится после теста
```

## Покрытие кода

Для генерации отчета о покрытии:

```bash
# HTML отчет
python run_tests.py --coverage

# Отчет будет доступен в htmlcov/index.html
```

Цель покрытия: **80%+** для всех модулей.

## Добавление новых тестов

### Создание unit теста

1. Создайте файл `tests/unit/test_module_name.py`
2. Импортируйте необходимые модули и fixtures
3. Создайте класс `TestModuleName`
4. Добавьте методы тестирования с префиксом `test_`

```python
"""
Unit тесты для модуля magic_uniq.module_name.
"""

import pytest
from magic_uniq.module_name import SomeClass


class TestSomeClass:
    """Тесты для класса SomeClass."""
    
    def test_init(self):
        """Тест инициализации."""
        obj = SomeClass()
        assert obj is not None
    
    def test_some_method(self, temp_dir):
        """Тест метода."""
        obj = SomeClass()
        result = obj.some_method()
        assert result == expected_value
```

### Добавление маркеров

Добавьте соответствующие маркеры в `pytest.ini`:

```ini
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    network: Tests that require network access
    new_module: Tests for new module
```

## Отладка тестов

### Запуск одного теста

```bash
# Конкретный тест
pytest tests/unit/test_config_settings.py::TestSettings::test_init_default -v

# Тесты класса
pytest tests/unit/test_config_settings.py::TestSettings -v

# Тесты файла
pytest tests/unit/test_config_settings.py -v
```

### Отладка с pdb

```bash
# Остановка на первой ошибке
pytest --pdb

# Остановка на первой ошибке с выводом
pytest --pdb -s
```

### Вывод print statements

```bash
# Показать print вывод
pytest -s

# Показать print вывод только для упавших тестов
pytest --tb=short
```

## Continuous Integration

Тесты настроены для запуска в CI/CD пайплайнах. Пример конфигурации:

```yaml
# .github/workflows/tests.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.10
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-asyncio
      - name: Run tests
        run: python run_tests.py --coverage
```

## Лучшие практики

1. **Изоляция тестов** - каждый тест должен быть независимым
2. **Описательные имена** - имена тестов должны описывать что тестируется
3. **Один assert на тест** - по возможности тестируйте одну вещь за раз
4. **Используйте fixtures** - для повторяющейся настройки
5. **Мокируйте внешние зависимости** - для стабильности тестов
6. **Тестируйте edge cases** - граничные случаи и ошибки
7. **Документируйте сложные тесты** - добавляйте комментарии

## Troubleshooting

### Проблемы с импортами

Если возникают проблемы с импортами модулей:

```bash
# Убедитесь что PYTHONPATH настроен
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"

# Или используйте editable install
pip install -e .
```

### Проблемы с зависимостями

```bash
# Проверьте зависимости
python run_tests.py --check-deps

# Установите недостающие
pip install pytest pytest-cov pytest-asyncio
```

### Медленные тесты

```bash
# Покажите самые медленные тесты
pytest --durations=10
```
