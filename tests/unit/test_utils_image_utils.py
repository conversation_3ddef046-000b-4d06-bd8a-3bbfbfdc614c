"""
Unit тесты для модуля magic_uniq.utils.image_utils.
"""

import pytest
import numpy as np
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Мокаем PIL импорты для тестирования без зависимостей
with patch.dict('sys.modules', {
    'PIL': <PERSON><PERSON>(),
    'PIL.Image': <PERSON><PERSON>(),
    'PIL.ImageOps': <PERSON><PERSON>()
}):
    from magic_uniq.utils.image_utils import ImageUtils


class TestImageUtils:
    """Тесты для класса ImageUtils."""
    
    def test_load_image_success(self, mock_image):
        """Тест успешной загрузки изображения."""
        with patch('magic_uniq.utils.image_utils.PIL_AVAILABLE', True):
            with patch('magic_uniq.utils.image_utils.Image.open', return_value=mock_image):
                result = ImageUtils.load_image("test.jpg")
                
                assert result == mock_image
    
    def test_load_image_pil_not_available(self):
        """Тест загрузки изображения когда PIL недоступен."""
        with patch('magic_uniq.utils.image_utils.PIL_AVAILABLE', False):
            with pytest.raises(ImportError, match="PIL \\(Pillow\\) не установлен"):
                ImageUtils.load_image("test.jpg")
    
    def test_load_image_file_error(self):
        """Тест обработки ошибки при загрузке файла."""
        with patch('magic_uniq.utils.image_utils.PIL_AVAILABLE', True):
            with patch('magic_uniq.utils.image_utils.Image.open', side_effect=Exception("File error")):
                result = ImageUtils.load_image("invalid.jpg")
                
                assert result is None
    
    def test_save_image_success_jpeg(self, mock_image, temp_dir):
        """Тест успешного сохранения JPEG изображения."""
        file_path = temp_dir / "test.jpg"
        mock_image.mode = 'RGB'
        
        result = ImageUtils.save_image(mock_image, file_path, quality=90)
        
        assert result is True
        mock_image.save.assert_called_once()
        
        # Проверяем параметры сохранения
        call_args = mock_image.save.call_args
        assert call_args[0][0] == file_path
        assert call_args[1]['format'] == 'JPEG'
        assert call_args[1]['quality'] == 90
        assert call_args[1]['optimize'] is True
    
    def test_save_image_success_png(self, mock_image, temp_dir):
        """Тест успешного сохранения PNG изображения."""
        file_path = temp_dir / "test.png"
        
        result = ImageUtils.save_image(mock_image, file_path)
        
        assert result is True
        call_args = mock_image.save.call_args
        assert call_args[1]['format'] == 'PNG'
    
    def test_save_image_rgba_to_jpeg(self, temp_dir):
        """Тест конвертации RGBA в RGB при сохранении в JPEG."""
        mock_image = Mock()
        mock_image.mode = 'RGBA'
        mock_converted = Mock()
        mock_image.convert.return_value = mock_converted
        
        file_path = temp_dir / "test.jpg"
        
        result = ImageUtils.save_image(mock_image, file_path)
        
        assert result is True
        mock_image.convert.assert_called_once_with('RGB')
        mock_converted.save.assert_called_once()
    
    def test_save_image_create_directory(self, mock_image, temp_dir):
        """Тест создания директории при сохранении."""
        file_path = temp_dir / "subdir" / "test.jpg"
        
        result = ImageUtils.save_image(mock_image, file_path)
        
        assert result is True
        assert file_path.parent.exists()
    
    def test_save_image_error(self, mock_image, temp_dir):
        """Тест обработки ошибки при сохранении."""
        file_path = temp_dir / "test.jpg"
        mock_image.save.side_effect = Exception("Save error")
        
        result = ImageUtils.save_image(mock_image, file_path)
        
        assert result is False
    
    def test_resize_image_maintain_aspect(self, mock_image):
        """Тест изменения размера с сохранением пропорций."""
        with patch('magic_uniq.utils.image_utils.ImageOps') as mock_ops:
            mock_resized = Mock()
            mock_ops.fit.return_value = mock_resized
            
            result = ImageUtils.resize_image(mock_image, (800, 600), maintain_aspect=True)
            
            assert result == mock_resized
            mock_ops.fit.assert_called_once()
    
    def test_resize_image_no_aspect(self, mock_image):
        """Тест изменения размера без сохранения пропорций."""
        mock_resized = Mock()
        mock_image.resize.return_value = mock_resized
        
        result = ImageUtils.resize_image(mock_image, (800, 600), maintain_aspect=False)
        
        assert result == mock_resized
        mock_image.resize.assert_called_once_with((800, 600), mock_image.resize.call_args[0][1])
    
    def test_get_image_info(self, mock_image):
        """Тест получения информации об изображении."""
        mock_image.size = (1920, 1080)
        mock_image.width = 1920
        mock_image.height = 1080
        mock_image.mode = 'RGB'
        mock_image.format = 'JPEG'
        
        info = ImageUtils.get_image_info(mock_image)
        
        expected_info = {
            'size': (1920, 1080),
            'width': 1920,
            'height': 1080,
            'mode': 'RGB',
            'format': 'JPEG',
            'has_transparency': False
        }
        
        assert info == expected_info
    
    def test_get_image_info_with_transparency(self, mock_image):
        """Тест получения информации об изображении с прозрачностью."""
        mock_image.mode = 'RGBA'
        
        info = ImageUtils.get_image_info(mock_image)
        
        assert info['has_transparency'] is True
    
    def test_convert_to_rgb_already_rgb(self, mock_image):
        """Тест конвертации изображения, которое уже в RGB."""
        mock_image.mode = 'RGB'
        
        result = ImageUtils.convert_to_rgb(mock_image)
        
        assert result == mock_image
        mock_image.convert.assert_not_called()
    
    def test_convert_to_rgb_from_rgba(self):
        """Тест конвертации из RGBA в RGB."""
        mock_image = Mock()
        mock_image.mode = 'RGBA'
        mock_image.size = (100, 100)
        
        # Мокаем split для получения альфа-канала
        mock_alpha = Mock()
        mock_image.split.return_value = [Mock(), Mock(), Mock(), mock_alpha]
        
        with patch('magic_uniq.utils.image_utils.Image') as mock_pil:
            mock_background = Mock()
            mock_pil.new.return_value = mock_background
            
            result = ImageUtils.convert_to_rgb(mock_image)
            
            assert result == mock_background
            mock_pil.new.assert_called_once_with('RGB', (100, 100), (255, 255, 255))
            mock_background.paste.assert_called_once_with(mock_image, mask=mock_alpha)
    
    def test_convert_to_rgb_other_mode(self, mock_image):
        """Тест конвертации из другого режима в RGB."""
        mock_image.mode = 'L'  # Grayscale
        mock_converted = Mock()
        mock_image.convert.return_value = mock_converted
        
        result = ImageUtils.convert_to_rgb(mock_image)
        
        assert result == mock_converted
        mock_image.convert.assert_called_once_with('RGB')
    
    def test_image_to_numpy(self, mock_image):
        """Тест конвертации PIL изображения в numpy array."""
        with patch('magic_uniq.utils.image_utils.np') as mock_np:
            mock_array = Mock()
            mock_np.array.return_value = mock_array
            
            result = ImageUtils.image_to_numpy(mock_image)
            
            assert result == mock_array
            mock_np.array.assert_called_once_with(mock_image)
    
    def test_numpy_to_image_uint8(self):
        """Тест конвертации numpy array в PIL изображение (uint8)."""
        mock_array = Mock()
        mock_array.dtype = np.uint8
        
        with patch('magic_uniq.utils.image_utils.Image') as mock_pil:
            mock_image = Mock()
            mock_pil.fromarray.return_value = mock_image
            
            result = ImageUtils.numpy_to_image(mock_array)
            
            assert result == mock_image
            mock_pil.fromarray.assert_called_once_with(mock_array)
    
    def test_numpy_to_image_float(self):
        """Тест конвертации numpy array в PIL изображение (float)."""
        mock_array = Mock()
        mock_array.dtype = np.float64
        
        with patch('magic_uniq.utils.image_utils.np') as mock_np:
            mock_clipped = Mock()
            mock_uint8 = Mock()
            mock_np.clip.return_value = mock_clipped
            mock_clipped.astype.return_value = mock_uint8
            
            with patch('magic_uniq.utils.image_utils.Image') as mock_pil:
                mock_image = Mock()
                mock_pil.fromarray.return_value = mock_image
                
                result = ImageUtils.numpy_to_image(mock_array)
                
                assert result == mock_image
                mock_np.clip.assert_called_once_with(mock_array, 0, 255)
                mock_clipped.astype.assert_called_once_with(mock_np.uint8)
    
    def test_create_thumbnail(self, mock_image):
        """Тест создания миниатюры."""
        mock_copy = Mock()
        mock_image.copy.return_value = mock_copy
        
        result = ImageUtils.create_thumbnail(mock_image, (150, 150))
        
        assert result == mock_copy
        mock_image.copy.assert_called_once()
        mock_copy.thumbnail.assert_called_once_with((150, 150), mock_copy.thumbnail.call_args[0][1])
    
    def test_format_detection(self, mock_image, temp_dir):
        """Тест определения формата по расширению файла."""
        test_cases = [
            ("test.jpg", "JPEG"),
            ("test.jpeg", "JPEG"),
            ("test.png", "PNG"),
            ("test.bmp", "BMP"),
            ("test.tiff", "TIFF"),
            ("test.webp", "WEBP"),
            ("test.unknown", "JPEG")  # Fallback to JPEG
        ]
        
        for filename, expected_format in test_cases:
            file_path = temp_dir / filename
            
            ImageUtils.save_image(mock_image, file_path)
            
            call_args = mock_image.save.call_args
            assert call_args[1]['format'] == expected_format
            
            # Сбрасываем mock для следующего теста
            mock_image.save.reset_mock()
    
    def test_quality_parameter_only_for_jpeg(self, mock_image, temp_dir):
        """Тест что параметр quality применяется только для JPEG."""
        # Тест для JPEG
        jpeg_path = temp_dir / "test.jpg"
        ImageUtils.save_image(mock_image, jpeg_path, quality=80)
        
        call_args = mock_image.save.call_args
        assert 'quality' in call_args[1]
        assert call_args[1]['quality'] == 80
        
        # Сбрасываем mock
        mock_image.save.reset_mock()
        
        # Тест для PNG (quality не должно быть)
        png_path = temp_dir / "test.png"
        ImageUtils.save_image(mock_image, png_path, quality=80)
        
        call_args = mock_image.save.call_args
        assert 'quality' not in call_args[1]
