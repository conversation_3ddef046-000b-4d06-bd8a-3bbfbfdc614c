"""
Unit тесты для модуля magic_uniq.core.classes.
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Мокаем зависимости для избежания ошибок импорта
with patch.dict('sys.modules', {
    'cv2': <PERSON><PERSON>(),
    'numpy': <PERSON><PERSON>(),
    'PIL': <PERSON><PERSON>(),
    'PIL.Image': <PERSON><PERSON>(),
}):
    from magic_uniq.core.classes import VideoProcessor


class TestVideoProcessor:
    """Тесты для класса VideoProcessor."""
    
    def test_init(self, temp_dir):
        """Тест инициализации VideoProcessor."""
        mock_settings = Mock()
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            processor = VideoProcessor(mock_settings)
            
            assert processor.settings_manager == mock_settings
            assert processor.token is None
            assert processor.image_url is None
            
            # Проверяем что директории созданы
            expected_dirs = [
                temp_dir / "data" / "backgrounds",
                temp_dir / "data" / "input", 
                temp_dir / "data" / "output"
            ]
            
            for dir_path in expected_dirs:
                assert dir_path.exists()
    
    def test_initialize(self, temp_dir):
        """Тест инициализации процессора."""
        mock_settings = Mock()
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.classes.get_system_uuid', return_value='test-uuid-123'):
                processor = VideoProcessor(mock_settings)
                
                result = processor.initialize()
                
                assert result == processor
                assert processor.token == 'test-uuid-123'
                assert processor.image_url == 'https://server2.magicuniq.space/get_image?token=test-uuid-123'
    
    def test_initialize_token_already_set(self, temp_dir):
        """Тест инициализации когда токен уже установлен."""
        mock_settings = Mock()
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            processor = VideoProcessor(mock_settings)
            processor.token = 'existing-token'
            processor.image_url = 'existing-url'
            
            result = processor.initialize()
            
            assert result == processor
            assert processor.token == 'existing-token'
            assert processor.image_url == 'existing-url'
    
    def test_directory_creation(self, temp_dir):
        """Тест создания необходимых директорий."""
        mock_settings = Mock()
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            processor = VideoProcessor(mock_settings)
            
            # Проверяем что все необходимые директории созданы
            assert processor.backgrounds_dir.exists()
            assert processor.input_dir.exists()
            assert processor.output_dir.exists()
            
            # Проверяем правильные пути
            assert processor.backgrounds_dir == temp_dir / "data" / "backgrounds"
            assert processor.input_dir == temp_dir / "data" / "input"
            assert processor.output_dir == temp_dir / "data" / "output"
    
    def test_data_directory_structure(self, temp_dir):
        """Тест структуры директории data."""
        mock_settings = Mock()
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            processor = VideoProcessor(mock_settings)
            
            data_dir = temp_dir / "data"
            assert data_dir.exists()
            assert data_dir.is_dir()
            
            # Проверяем что базовая директория data создается
            assert processor.backgrounds_dir.parent == data_dir
            assert processor.input_dir.parent == data_dir
            assert processor.output_dir.parent == data_dir
    
    def test_settings_manager_assignment(self, temp_dir):
        """Тест присвоения менеджера настроек."""
        mock_settings = Mock()
        mock_settings.some_method = Mock(return_value="test_value")
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            processor = VideoProcessor(mock_settings)
            
            assert processor.settings_manager == mock_settings
            
            # Проверяем что можем вызывать методы менеджера настроек
            result = processor.settings_manager.some_method()
            assert result == "test_value"
    
    def test_token_generation_on_initialize(self, temp_dir):
        """Тест генерации токена при инициализации."""
        mock_settings = Mock()
        test_uuid = 'generated-uuid-456'
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.classes.get_system_uuid', return_value=test_uuid):
                processor = VideoProcessor(mock_settings)
                
                # Токен не должен быть установлен до инициализации
                assert processor.token is None
                
                processor.initialize()
                
                # После инициализации токен должен быть установлен
                assert processor.token == test_uuid
    
    def test_image_url_generation(self, temp_dir):
        """Тест генерации URL изображения."""
        mock_settings = Mock()
        test_uuid = 'url-test-uuid'
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.classes.get_system_uuid', return_value=test_uuid):
                processor = VideoProcessor(mock_settings)
                processor.initialize()
                
                expected_url = f'https://server2.magicuniq.space/get_image?token={test_uuid}'
                assert processor.image_url == expected_url
    
    def test_multiple_initialization_calls(self, temp_dir):
        """Тест множественных вызовов инициализации."""
        mock_settings = Mock()
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.classes.get_system_uuid', return_value='stable-uuid') as mock_uuid:
                processor = VideoProcessor(mock_settings)
                
                # Первая инициализация
                result1 = processor.initialize()
                token1 = processor.token
                url1 = processor.image_url
                
                # Вторая инициализация
                result2 = processor.initialize()
                token2 = processor.token
                url2 = processor.image_url
                
                # Токен и URL не должны измениться
                assert token1 == token2
                assert url1 == url2
                assert result1 == result2 == processor
                
                # get_system_uuid должен быть вызван только один раз
                mock_uuid.assert_called_once()
    
    def test_directory_permissions(self, temp_dir):
        """Тест прав доступа к созданным директориям."""
        mock_settings = Mock()
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            processor = VideoProcessor(mock_settings)
            
            # Проверяем что директории доступны для записи
            test_file = processor.backgrounds_dir / "test.txt"
            test_file.write_text("test content")
            assert test_file.exists()
            
            test_file2 = processor.input_dir / "test2.txt"
            test_file2.write_text("test content 2")
            assert test_file2.exists()
            
            test_file3 = processor.output_dir / "test3.txt"
            test_file3.write_text("test content 3")
            assert test_file3.exists()
    
    def test_processor_state_after_init(self, temp_dir):
        """Тест состояния процессора после инициализации."""
        mock_settings = Mock()
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            processor = VideoProcessor(mock_settings)
            
            # Проверяем начальное состояние
            assert hasattr(processor, 'settings_manager')
            assert hasattr(processor, 'backgrounds_dir')
            assert hasattr(processor, 'input_dir')
            assert hasattr(processor, 'output_dir')
            assert hasattr(processor, 'token')
            assert hasattr(processor, 'image_url')
            
            # Проверяем типы атрибутов
            assert isinstance(processor.backgrounds_dir, Path)
            assert isinstance(processor.input_dir, Path)
            assert isinstance(processor.output_dir, Path)
    
    def test_error_handling_in_directory_creation(self, temp_dir):
        """Тест обработки ошибок при создании директорий."""
        mock_settings = Mock()
        
        # Мокаем ошибку при создании директории
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('pathlib.Path.mkdir', side_effect=PermissionError("Access denied")):
                
                # Должно быть поднято исключение
                with pytest.raises(PermissionError):
                    processor = VideoProcessor(mock_settings)
    
    def test_uuid_import_availability(self, temp_dir):
        """Тест доступности импорта get_system_uuid."""
        mock_settings = Mock()
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            # Проверяем что функция импортируется корректно
            with patch('magic_uniq.core.classes.get_system_uuid', return_value='import-test-uuid'):
                processor = VideoProcessor(mock_settings)
                processor.initialize()
                
                assert processor.token == 'import-test-uuid'
