"""
Unit тесты для модуля magic_uniq.services.content_generator.
"""

import pytest
import json
import time
import threading
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from queue import Queue

from magic_uniq.services.content_generator import APIAccount, MultiAccountFusionBrainAPI, CombinedGenerator


class TestAPIAccount:
    """Тесты для класса APIAccount."""
    
    def test_init(self):
        """Тест инициализации аккаунта API."""
        api_key = "test_api_key"
        secret_key = "test_secret_key"
        
        account = APIAccount(api_key, secret_key)
        
        assert account.api_key == api_key
        assert account.secret_key == secret_key
        assert account.headers == {
            'X-Key': f'Key {api_key}',
            'X-Secret': f'Secret {secret_key}',
        }
        assert account.is_busy is False
        assert account.consecutive_errors == 0
        assert account.last_error_time == 0
        assert account.total_generated == 0


class TestMultiAccountFusionBrainAPI:
    """Тесты для класса MultiAccountFusionBrainAPI."""
    
    def test_init(self, temp_dir):
        """Тест инициализации API."""
        accounts = [
            {"api_key": "key1", "secret_key": "secret1"},
            {"api_key": "key2", "secret_key": "secret2"}
        ]
        prompts = ["prompt1", "prompt2"]
        
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=prompts,
            max_images=100
        )
        
        assert len(api.accounts) == 2
        assert api.SAVE_DIR == temp_dir
        assert api.MAX_IMAGES == 100
        assert api.is_running is True
        assert api.is_paused is False
        assert api.generation_count == 0
        assert isinstance(api.prompt_queue, Queue)
    
    def test_fill_prompt_queue(self, temp_dir):
        """Тест заполнения очереди промптов."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        prompts = ["prompt1", "prompt2", "prompt3"]
        
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=prompts,
            max_images=100
        )
        
        api.fill_prompt_queue()
        
        # Проверяем что очередь заполнена
        assert not api.prompt_queue.empty()
        
        # Проверяем содержимое очереди
        queue_items = []
        while not api.prompt_queue.empty():
            queue_items.append(api.prompt_queue.get())
        
        assert len(queue_items) > 0
        # Каждый элемент должен быть из списка промптов
        for item in queue_items:
            assert item in prompts
    
    def test_get_model_success(self, temp_dir, mock_requests):
        """Тест успешного получения модели."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        # Мокаем успешный ответ
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = [{"id": "model1", "name": "Test Model"}]
        mock_requests["get"].return_value = mock_response
        
        model = api.get_model(api.accounts[0])
        
        assert model == "model1"
    
    def test_get_model_http_error(self, temp_dir, mock_requests):
        """Тест обработки HTTP ошибки при получении модели."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        # Мокаем ошибку HTTP
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.text = "Not found"
        mock_requests["get"].return_value = mock_response
        
        with patch('builtins.print'):
            model = api.get_model(api.accounts[0])
            
            assert model is None
    
    def test_get_model_json_decode_error(self, temp_dir, mock_requests):
        """Тест обработки ошибки декодирования JSON."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        # Мокаем ответ с невалидным JSON
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
        mock_response.text = "invalid json"
        mock_requests["get"].return_value = mock_response
        
        with patch('builtins.print'):
            model = api.get_model(api.accounts[0])
            
            assert model is None
    
    def test_generate_success(self, temp_dir, mock_requests):
        """Тест успешной генерации изображения."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        # Мокаем успешный ответ
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"uuid": "test-uuid-123"}
        mock_requests["post"].return_value = mock_response
        
        uuid = api.generate("test prompt", "model1", api.accounts[0])
        
        assert uuid == "test-uuid-123"
    
    def test_generate_empty_prompt(self, temp_dir):
        """Тест генерации с пустым промптом."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        with patch('builtins.print'):
            uuid = api.generate("", "model1", api.accounts[0])
            
            assert uuid is None
    
    def test_generate_long_prompt(self, temp_dir, mock_requests):
        """Тест генерации с длинным промптом."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        # Создаем очень длинный промпт
        long_prompt = "a" * 600
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"uuid": "test-uuid"}
        mock_requests["post"].return_value = mock_response
        
        with patch('builtins.print'):
            uuid = api.generate(long_prompt, "model1", api.accounts[0])
            
            # Промпт должен быть обрезан, но генерация должна пройти
            assert uuid == "test-uuid"
    
    def test_generate_not_running(self, temp_dir):
        """Тест генерации когда API не запущен."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        api.is_running = False
        
        uuid = api.generate("test prompt", "model1", api.accounts[0])
        
        assert uuid is None
    
    def test_generate_paused(self, temp_dir):
        """Тест генерации когда API на паузе."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        api.is_paused = True
        
        uuid = api.generate("test prompt", "model1", api.accounts[0])
        
        assert uuid is None
    
    def test_check_generation_success(self, temp_dir, mock_requests):
        """Тест успешной проверки статуса генерации."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        # Мокаем успешный ответ
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "status": "DONE",
            "images": ["base64_image_data"]
        }
        mock_requests["get"].return_value = mock_response
        
        result = api.check_generation("test-uuid", api.accounts[0])
        
        assert result["status"] == "DONE"
        assert "images" in result
    
    def test_check_generation_in_progress(self, temp_dir, mock_requests):
        """Тест проверки статуса генерации в процессе."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        # Мокаем ответ "в процессе"
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "INITIAL"}
        mock_requests["get"].return_value = mock_response
        
        with patch('time.sleep'):  # Ускоряем тест
            result = api.check_generation("test-uuid", api.accounts[0])
            
            # Должен вернуться статус ошибки из-за таймаута
            assert result["status"] == "ERROR"
    
    def test_save_images(self, temp_dir):
        """Тест сохранения изображений."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        # Тестовые данные base64 (простая строка для теста)
        import base64
        test_image_data = b"fake image data"
        base64_data = base64.b64encode(test_image_data).decode()
        
        images_data = [base64_data]
        prompt = "test prompt"
        
        with patch('time.strftime', return_value="20231201_120000"):
            api.save_images(images_data, prompt)
            
            # Проверяем что файл был создан
            saved_files = list(temp_dir.glob("*.jpg"))
            assert len(saved_files) == 1
            
            # Проверяем содержимое файла
            assert saved_files[0].read_bytes() == test_image_data
            
            # Проверяем что счетчик увеличился
            assert api.generation_count == 1
    
    def test_save_images_max_limit_reached(self, temp_dir):
        """Тест сохранения когда достигнут лимит."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=1  # Устанавливаем лимит в 1
        )
        
        # Создаем файл чтобы достичь лимита
        (temp_dir / "existing.jpg").write_bytes(b"existing")
        
        import base64
        test_image_data = b"fake image data"
        base64_data = base64.b64encode(test_image_data).decode()
        
        with patch('builtins.print'):
            api.save_images([base64_data], "test prompt")
            
            # API должен быть приостановлен
            assert api.is_paused is True
    
    def test_count_images(self, temp_dir):
        """Тест подсчета изображений."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        # Создаем тестовые файлы
        (temp_dir / "image1.jpg").write_bytes(b"image1")
        (temp_dir / "image2.png").write_bytes(b"image2")
        (temp_dir / "document.txt").write_bytes(b"text")  # Не изображение
        
        count = api.count_images()
        
        assert count == 2  # Только jpg и png файлы
    
    def test_handle_error(self, temp_dir):
        """Тест обработки ошибок."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        api = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir=str(temp_dir),
            positive_prompts=["test"],
            max_images=100
        )
        
        account = api.accounts[0]
        initial_errors = account.consecutive_errors
        
        api.handle_error(account, "Test error")
        
        assert account.consecutive_errors == initial_errors + 1
        assert account.last_error_time > 0


class TestCombinedGenerator:
    """Тесты для класса CombinedGenerator."""
    
    def test_init(self):
        """Тест инициализации комбинированного генератора."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        bg_prompts = ["bg1", "bg2"]
        overlay_prompts = ["overlay1", "overlay2"]
        
        with patch('magic_uniq.services.content_generator.MultiAccountFusionBrainAPI'):
            generator = CombinedGenerator(accounts, bg_prompts, overlay_prompts, max_images=100)
            
            assert generator.is_running is True
            assert generator.background_generator is not None
            assert generator.overlay_generator is not None
    
    def test_handle_exit(self):
        """Тест обработки сигнала выхода."""
        accounts = [{"api_key": "key1", "secret_key": "secret1"}]
        
        with patch('magic_uniq.services.content_generator.MultiAccountFusionBrainAPI'):
            generator = CombinedGenerator(accounts, [], [], max_images=100)
            
            generator.handle_exit(None, None)
            
            assert generator.is_running is False
