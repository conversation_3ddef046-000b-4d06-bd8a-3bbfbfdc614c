"""
Unit тесты для модуля magic_uniq.core.video_processor.
"""

import pytest
import random
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Мокаем зависимости
with patch.dict('sys.modules', {
    'cv2': <PERSON><PERSON>(),
    'numpy': <PERSON><PERSON>(),
    'PIL': <PERSON><PERSON>(),
    'PIL.Image': <PERSON><PERSON>(),
    'librosa': <PERSON><PERSON>(),
    'soundfile': <PERSON><PERSON>(),
    'scipy': <PERSON><PERSON>(),
}):
    from magic_uniq.core.video_processor import (
        VideoEffectParams, 
        EncodingParams, 
        VideoUniquifierConfig,
        VideoUniquifier
    )


class TestVideoEffectParams:
    """Тесты для класса VideoEffectParams."""
    
    def test_init_default_values(self):
        """Тест инициализации с значениями по умолчанию."""
        params = VideoEffectParams()
        
        # Проверяем некоторые ключевые параметры
        assert hasattr(params, 'scale_enabled')
        assert hasattr(params, 'saturation_enabled')
        assert hasattr(params, 'transparency_enabled')
        assert hasattr(params, 'gamma_enabled')
        assert hasattr(params, 'brightness_enabled')
        
        # Проверяем типы значений по умолчанию
        assert isinstance(params.scale_enabled, bool)
        assert isinstance(params.saturation_enabled, bool)
        assert isinstance(params.transparency_enabled, bool)
    
    def test_random_values_generation(self):
        """Тест генерации случайных значений."""
        params = VideoEffectParams()
        
        # Проверяем что случайные значения находятся в ожидаемых диапазонах
        if hasattr(params, 'wave_amplitude'):
            assert 0.1 <= params.wave_amplitude <= 0.5
        
        if hasattr(params, 'wave_frequency'):
            assert 0.5 <= params.wave_frequency <= 2.0
        
        if hasattr(params, 'wave_speed'):
            assert 0.1 <= params.wave_speed <= 1.0
    
    def test_effect_parameters_types(self):
        """Тест типов параметров эффектов."""
        params = VideoEffectParams()
        
        # Проверяем булевые параметры
        bool_params = [
            'scale_enabled', 'saturation_enabled', 'transparency_enabled',
            'gamma_enabled', 'brightness_enabled', 'contrast_enabled'
        ]
        
        for param_name in bool_params:
            if hasattr(params, param_name):
                param_value = getattr(params, param_name)
                assert isinstance(param_value, bool), f"{param_name} должен быть bool"
    
    def test_numeric_parameters_ranges(self):
        """Тест диапазонов числовых параметров."""
        params = VideoEffectParams()
        
        # Проверяем некоторые числовые параметры
        numeric_checks = [
            ('emoji_count', int, 1, 50),
            ('emoji_size', int, 10, 100),
            ('mosaic_size', int, 1, 10),
        ]
        
        for param_name, param_type, min_val, max_val in numeric_checks:
            if hasattr(params, param_name):
                param_value = getattr(params, param_name)
                assert isinstance(param_value, param_type), f"{param_name} должен быть {param_type}"
                if isinstance(param_value, (int, float)):
                    assert min_val <= param_value <= max_val, f"{param_name} вне диапазона [{min_val}, {max_val}]"


class TestEncodingParams:
    """Тесты для класса EncodingParams."""
    
    def test_init_default_values(self):
        """Тест инициализации с значениями по умолчанию."""
        params = EncodingParams()
        
        # Проверяем основные параметры кодирования
        assert hasattr(params, 'codec')
        assert hasattr(params, 'bitrate')
        assert hasattr(params, 'fps')
        assert hasattr(params, 'quality')
        
        # Проверяем значения по умолчанию
        if hasattr(params, 'codec'):
            assert isinstance(params.codec, str)
        
        if hasattr(params, 'fps'):
            assert isinstance(params.fps, (int, float))
            assert params.fps > 0
    
    def test_encoding_parameters_validity(self):
        """Тест валидности параметров кодирования."""
        params = EncodingParams()
        
        # Проверяем что параметры имеют разумные значения
        if hasattr(params, 'bitrate') and params.bitrate is not None:
            assert params.bitrate > 0, "Битрейт должен быть положительным"
        
        if hasattr(params, 'quality') and params.quality is not None:
            assert 0 <= params.quality <= 100, "Качество должно быть от 0 до 100"


class TestVideoUniquifierConfig:
    """Тесты для класса VideoUniquifierConfig."""
    
    def test_init_default_config(self, temp_dir):
        """Тест инициализации с конфигурацией по умолчанию."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.load_config') as mock_load_config:
                mock_config = Mock()
                mock_config.getboolean.return_value = False
                mock_config.getfloat.return_value = 0.5
                mock_config.getint.return_value = 30
                mock_load_config.return_value = mock_config
                
                config = VideoUniquifierConfig("test_settings.ini")
                
                assert config.config == mock_config
                assert config.base_dir == temp_dir / "data"
                assert config.input_dir == temp_dir / "data" / "input"
                assert config.output_dir == temp_dir / "data" / "output"
                assert config.bg_dir == temp_dir / "data" / "backgrounds"
    
    def test_directory_creation(self, temp_dir):
        """Тест создания директорий."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.load_config') as mock_load_config:
                mock_config = Mock()
                mock_config.getboolean.return_value = False
                mock_config.getfloat.return_value = 0.5
                mock_load_config.return_value = mock_config
                
                config = VideoUniquifierConfig("test_settings.ini")
                
                # Проверяем что базовая директория создана
                assert config.base_dir.exists()
    
    def test_config_loading(self, temp_dir):
        """Тест загрузки конфигурации."""
        config_path = "custom_settings.ini"
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.load_config') as mock_load_config:
                mock_config = Mock()
                mock_load_config.return_value = mock_config
                
                config = VideoUniquifierConfig(config_path)
                
                mock_load_config.assert_called_once_with(config_path)
                assert config.config == mock_config
    
    def test_scale_configuration(self, temp_dir):
        """Тест конфигурации масштабирования."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.load_config') as mock_load_config:
                mock_config = Mock()
                mock_config.getboolean.return_value = True
                mock_config.getfloat.side_effect = [0.5, 1.5]  # min, max
                mock_load_config.return_value = mock_config
                
                config = VideoUniquifierConfig("test_settings.ini")
                
                # Проверяем вызовы методов конфигурации
                mock_config.getboolean.assert_called_with('Scale', 'enabled', fallback=False)
                assert mock_config.getfloat.call_count >= 2
    
    def test_iterations_configuration(self, temp_dir):
        """Тест конфигурации итераций."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.load_config') as mock_load_config:
                mock_config = Mock()
                mock_config.getboolean.return_value = False
                mock_config.getfloat.return_value = 0.5
                mock_load_config.return_value = mock_config
                
                config = VideoUniquifierConfig("test_settings.ini")
                
                # Проверяем значения по умолчанию
                assert config.iterations == 1
                assert config.infinite_loop is False


class TestVideoUniquifier:
    """Тесты для класса VideoUniquifier."""
    
    def test_init(self, temp_dir):
        """Тест инициализации VideoUniquifier."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.VideoUniquifierConfig') as mock_config_class:
                mock_config = Mock()
                mock_config.iterations = 1
                mock_config_class.return_value = mock_config
                
                uniquifier = VideoUniquifier("test_config.ini")
                
                assert uniquifier.config == mock_config
                mock_config_class.assert_called_once_with("test_config.ini")
    
    def test_process_video_file_not_found(self, temp_dir):
        """Тест обработки несуществующего файла."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.VideoUniquifierConfig') as mock_config_class:
                mock_config = Mock()
                mock_config.iterations = 1
                mock_config_class.return_value = mock_config
                
                uniquifier = VideoUniquifier("test_config.ini")
                
                with pytest.raises(FileNotFoundError):
                    uniquifier.process_video("non_existent_file.mp4")
    
    def test_process_video_with_existing_file(self, temp_dir, sample_video_file):
        """Тест обработки существующего файла."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.VideoUniquifierConfig') as mock_config_class:
                mock_config = Mock()
                mock_config.iterations = 1
                mock_config_class.return_value = mock_config
                
                # Мокаем менеджер уникализации
                with patch('magic_uniq.core.video_processor.UniquificationManager') as mock_manager_class:
                    mock_manager = Mock()
                    mock_manager.register_process.return_value = "process_id"
                    mock_manager_class.return_value = mock_manager
                    
                    uniquifier = VideoUniquifier("test_config.ini")
                    
                    # Мокаем os.getpid для избежания ошибок
                    with patch('os.getpid', return_value=12345):
                        try:
                            uniquifier.process_video(str(sample_video_file))
                        except Exception as e:
                            # Ожидаем ошибки из-за мокированных зависимостей
                            # Главное что FileNotFoundError не поднимается
                            assert not isinstance(e, FileNotFoundError)
    
    def test_uniquification_manager_creation(self, temp_dir):
        """Тест создания менеджера уникализации."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.VideoUniquifierConfig') as mock_config_class:
                mock_config = Mock()
                mock_config.iterations = 1
                mock_config_class.return_value = mock_config
                
                with patch('magic_uniq.core.video_processor.UniquificationManager') as mock_manager_class:
                    mock_manager = Mock()
                    mock_manager_class.return_value = mock_manager
                    
                    uniquifier = VideoUniquifier("test_config.ini")
                    
                    # Проверяем что менеджер не создан до обработки видео
                    assert not hasattr(uniquifier, 'uniquification_manager')
    
    def test_effect_params_integration(self, temp_dir):
        """Тест интеграции с параметрами эффектов."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.VideoUniquifierConfig') as mock_config_class:
                mock_config = Mock()
                mock_config.iterations = 1
                mock_config_class.return_value = mock_config
                
                uniquifier = VideoUniquifier("test_config.ini")
                
                # Создаем тестовые параметры эффектов
                effect_params = VideoEffectParams()
                encoding_params = EncodingParams()
                
                # Проверяем что параметры можно передать в метод
                # (даже если он не выполнится полностью из-за мокированных зависимостей)
                assert effect_params is not None
                assert encoding_params is not None
    
    def test_config_attribute_access(self, temp_dir):
        """Тест доступа к атрибутам конфигурации."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('magic_uniq.core.video_processor.VideoUniquifierConfig') as mock_config_class:
                mock_config = Mock()
                mock_config.iterations = 5
                mock_config.infinite_loop = True
                mock_config_class.return_value = mock_config
                
                uniquifier = VideoUniquifier("test_config.ini")
                
                # Проверяем доступ к атрибутам конфигурации
                assert uniquifier.config.iterations == 5
                assert uniquifier.config.infinite_loop is True
