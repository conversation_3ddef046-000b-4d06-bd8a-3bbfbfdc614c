"""
Unit тесты для модуля magic_uniq.config.settings.
"""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import patch, mock_open

from magic_uniq.config.settings import Settings


class TestSettings:
    """Тесты для класса Settings."""
    
    def test_init_default(self, temp_dir):
        """Тест инициализации с настройками по умолчанию."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            assert settings.base_dir == temp_dir / "data"
            assert settings.base_dir.exists()
            assert settings.get("directories.input") == "input_images"
            assert settings.get("image_processing.quality") == 95
    
    def test_init_with_config_path(self, config_file):
        """Тест инициализации с указанным путем к конфигу."""
        settings = Settings(config_path=str(config_file))
        
        assert settings.config_path == config_file
        assert settings.get("directories.input") == "input_images"
    
    def test_load_config_success(self, temp_dir, sample_config):
        """Тест успешной загрузки конфигурации."""
        config_path = temp_dir / "test_config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(sample_config, f)
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings(config_path=str(config_path))
            
            assert settings.get("directories.input") == "input_images"
            assert settings.get("image_processing.quality") == 95
    
    def test_load_config_file_not_exists(self, temp_dir):
        """Тест загрузки конфигурации когда файл не существует."""
        non_existent_path = temp_dir / "non_existent.json"
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings(config_path=str(non_existent_path))
            
            # Должны использоваться настройки по умолчанию
            assert settings.get("directories.input") == "input_images"
    
    def test_load_config_invalid_json(self, temp_dir):
        """Тест загрузки конфигурации с невалидным JSON."""
        config_path = temp_dir / "invalid_config.json"
        config_path.write_text("invalid json content")
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('builtins.print') as mock_print:
                settings = Settings(config_path=str(config_path))
                
                # Должна быть выведена ошибка
                mock_print.assert_called()
                # Должны использоваться настройки по умолчанию
                assert settings.get("directories.input") == "input_images"
    
    def test_get_existing_key(self, temp_dir):
        """Тест получения существующего ключа."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            assert settings.get("directories.input") == "input_images"
            assert settings.get("image_processing.quality") == 95
    
    def test_get_nested_key(self, temp_dir):
        """Тест получения вложенного ключа."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            assert settings.get("image_processing.max_size") == 4096
            assert settings.get("server.host") == "localhost"
    
    def test_get_non_existing_key(self, temp_dir):
        """Тест получения несуществующего ключа."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            assert settings.get("non.existing.key") is None
            assert settings.get("non.existing.key", "default") == "default"
    
    def test_set_new_key(self, temp_dir):
        """Тест установки нового ключа."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            settings.set("new.key", "new_value")
            assert settings.get("new.key") == "new_value"
    
    def test_set_existing_key(self, temp_dir):
        """Тест изменения существующего ключа."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            settings.set("image_processing.quality", 85)
            assert settings.get("image_processing.quality") == 85
    
    def test_set_nested_key(self, temp_dir):
        """Тест установки вложенного ключа."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            settings.set("new.nested.key", "value")
            assert settings.get("new.nested.key") == "value"
    
    def test_get_directory_relative_path(self, temp_dir):
        """Тест получения директории с относительным путем."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            dir_path = settings.get_directory("input")
            expected_path = temp_dir / "data" / "input_images"
            assert dir_path == expected_path
    
    def test_get_directory_absolute_path(self, temp_dir):
        """Тест получения директории с абсолютным путем."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            abs_path = "/absolute/path"
            settings.set("directories.test", abs_path)
            
            dir_path = settings.get_directory("test")
            assert dir_path == Path(abs_path)
    
    def test_get_directory_non_existing(self, temp_dir):
        """Тест получения несуществующей директории."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            dir_path = settings.get_directory("non_existing")
            expected_path = temp_dir / "data" / "non_existing"
            assert dir_path == expected_path
    
    def test_get_backgrounds_dir(self, temp_dir):
        """Тест получения директории с фонами."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            bg_dir = settings.get_backgrounds_dir()
            expected_path = temp_dir / "data" / "backgrounds"
            assert bg_dir == expected_path
    
    def test_get_overlays_dir(self, temp_dir):
        """Тест получения директории с оверлеями."""
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            overlay_dir = settings.get_overlays_dir()
            expected_path = temp_dir / "data" / "overlay_images"
            assert overlay_dir == expected_path
    
    def test_merge_config(self, temp_dir):
        """Тест слияния конфигураций."""
        custom_config = {
            "image_processing": {
                "quality": 80,  # Изменяем существующий ключ
                "new_param": "new_value"  # Добавляем новый ключ
            },
            "new_section": {
                "param": "value"
            }
        }
        
        config_path = temp_dir / "custom_config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(custom_config, f)
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings(config_path=str(config_path))
            
            # Проверяем что изменился существующий ключ
            assert settings.get("image_processing.quality") == 80
            # Проверяем что добавился новый ключ в существующую секцию
            assert settings.get("image_processing.new_param") == "new_value"
            # Проверяем что добавилась новая секция
            assert settings.get("new_section.param") == "value"
            # Проверяем что остальные настройки по умолчанию сохранились
            assert settings.get("image_processing.max_size") == 4096
    
    def test_io_error_handling(self, temp_dir):
        """Тест обработки ошибок ввода-вывода."""
        config_path = temp_dir / "config.json"
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            with patch('builtins.open', side_effect=IOError("Permission denied")):
                with patch('builtins.print') as mock_print:
                    settings = Settings(config_path=str(config_path))
                    
                    # Должна быть выведена ошибка
                    mock_print.assert_called()
                    # Должны использоваться настройки по умолчанию
                    assert settings.get("directories.input") == "input_images"
