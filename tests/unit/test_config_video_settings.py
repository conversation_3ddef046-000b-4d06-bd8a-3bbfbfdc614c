"""
Unit тесты для модуля magic_uniq.config.video_settings.
"""

import pytest
from magic_uniq.config.video_settings import SettingsConfiguration


class TestSettingsConfiguration:
    """Тесты для класса SettingsConfiguration."""
    
    def test_get_default_settings_structure(self):
        """Тест структуры настроек по умолчанию."""
        settings = SettingsConfiguration.get_default_settings()
        
        # Проверяем что возвращается словарь
        assert isinstance(settings, dict)
        
        # Проверяем наличие основных секций
        expected_sections = [
            'scale', 'saturation', 'transparency', 'gamma', 'brightness', 
            'contrast', 'whitebalance', 'vignette', 'movement', 'backgroundscale',
            'blur', 'noise', 'microdots', 'mosaic', 'puzzle', 'snow', 'lines',
            'border', 'wave-distortion', 'emoji'
        ]
        
        for section in expected_sections:
            assert section in settings, f"Секция {section} отсутствует в настройках"
    
    def test_basic_effects_settings(self):
        """Тест настроек базовых эффектов."""
        settings = SettingsConfiguration.get_default_settings()
        
        # Проверяем настройки масштаба
        scale_settings = settings['scale']
        assert scale_settings['enabled'] is False
        assert scale_settings['controlType'] == 'range'
        assert scale_settings['range'] == [0.0, 1.0]
        assert scale_settings['translation'] == 'Масштаб'
        assert scale_settings['icon'] == 'minimize-2'
        
        # Проверяем настройки насыщенности
        saturation_settings = settings['saturation']
        assert saturation_settings['enabled'] is False
        assert saturation_settings['range'] == [1.0, 2.0]
        assert saturation_settings['translation'] == 'Насыщенность'
        
        # Проверяем настройки прозрачности
        transparency_settings = settings['transparency']
        assert transparency_settings['range'] == [0.0, 1.0]
        assert transparency_settings['translation'] == 'Прозрачность'
    
    def test_color_effects_settings(self):
        """Тест настроек цветовых эффектов."""
        settings = SettingsConfiguration.get_default_settings()
        
        # Проверяем настройки гаммы
        gamma_settings = settings['gamma']
        assert gamma_settings['range'] == [1.0, 3.0]
        assert gamma_settings['translation'] == 'Гамма'
        
        # Проверяем настройки яркости
        brightness_settings = settings['brightness']
        assert brightness_settings['range'] == [0, 2.0]
        assert brightness_settings['translation'] == 'Яркость'
        
        # Проверяем настройки контраста
        contrast_settings = settings['contrast']
        assert contrast_settings['range'] == [0.5, 2.0]
        assert contrast_settings['translation'] == 'Контраст'
    
    def test_movement_settings(self):
        """Тест настроек движения."""
        settings = SettingsConfiguration.get_default_settings()
        
        movement_settings = settings['movement']
        assert movement_settings['enabled'] is False
        assert 'patterns' in movement_settings
        
        # Проверяем паттерны движения
        patterns = movement_settings['patterns']
        expected_patterns = ['linear', 'circular', 'figure8', 'random', 'spiral_out', 'zigzag', 'combination']
        
        for pattern in expected_patterns:
            assert pattern in patterns['options']
        
        # Проверяем переводы паттернов
        translations = patterns['translations']
        assert translations['linear'] == 'Линейное'
        assert translations['circular'] == 'Круговое'
        assert translations['random'] == 'Случайное'
    
    def test_wave_distortion_settings(self):
        """Тест настроек волновых искажений."""
        settings = SettingsConfiguration.get_default_settings()
        
        wave_settings = settings['wave-distortion']
        assert wave_settings['enabled'] is False
        assert wave_settings['translation'] == 'Волновые искажения'
        
        # Проверяем подпараметры
        assert 'amplitude' in wave_settings
        assert 'speed' in wave_settings
        assert 'complexity' in wave_settings
        
        # Проверяем диапазоны
        assert wave_settings['amplitude']['range'] == [0.0, 1.0]
        assert wave_settings['speed']['range'] == [0.0, 1.0]
        assert wave_settings['complexity']['range'] == [0.1, 2.5]
    
    def test_emoji_settings(self):
        """Тест настроек эмодзи."""
        settings = SettingsConfiguration.get_default_settings()
        
        emoji_settings = settings['emoji']
        assert emoji_settings['enabled'] is False
        assert emoji_settings['translation'] == 'Эмодзи'
        
        # Проверяем параметры эмодзи
        assert 'count' in emoji_settings
        assert 'size' in emoji_settings
        assert 'speed' in emoji_settings
        
        # Проверяем диапазоны
        assert emoji_settings['count']['range'] == [1, 20]
        assert emoji_settings['size']['range'] == [10, 100]
        assert emoji_settings['speed']['range'] == [0.1, 5.0]
    
    def test_effects_settings(self):
        """Тест настроек эффектов."""
        settings = SettingsConfiguration.get_default_settings()
        
        # Проверяем настройки размытия
        blur_settings = settings['blur']
        assert blur_settings['enabled'] is False
        assert blur_settings['translation'] == 'Размытие'
        assert blur_settings['range'] == [0.0, 10.0]
        
        # Проверяем настройки шума
        noise_settings = settings['noise']
        assert noise_settings['translation'] == 'Шум'
        assert noise_settings['range'] == [0.0, 1.0]
        
        # Проверяем настройки мозаики
        mosaic_settings = settings['mosaic']
        assert mosaic_settings['translation'] == 'Мозаика'
        assert 'size' in mosaic_settings
        assert 'opacity' in mosaic_settings
    
    def test_lines_settings(self):
        """Тест настроек линий."""
        settings = SettingsConfiguration.get_default_settings()
        
        lines_settings = settings['lines']
        assert lines_settings['enabled'] is False
        assert lines_settings['translation'] == 'Линии'
        
        # Проверяем параметры линий
        assert 'count' in lines_settings
        assert 'thickness' in lines_settings
        assert 'opacity' in lines_settings
        assert 'color' in lines_settings
        
        # Проверяем диапазоны
        assert lines_settings['count']['range'] == [1, 50]
        assert lines_settings['thickness']['range'] == [1, 20]
        assert lines_settings['opacity']['range'] == [0.1, 1.0]
    
    def test_border_settings(self):
        """Тест настроек границ."""
        settings = SettingsConfiguration.get_default_settings()
        
        border_settings = settings['border']
        assert border_settings['enabled'] is False
        assert border_settings['translation'] == 'Граница'
        
        # Проверяем параметры границ
        assert 'width' in border_settings
        assert 'color' in border_settings
        assert 'style' in border_settings
        
        # Проверяем стили границ
        styles = border_settings['style']['options']
        expected_styles = ['solid', 'dashed', 'dotted', 'double']
        for style in expected_styles:
            assert style in styles
    
    def test_get_categories_structure(self):
        """Тест структуры категорий."""
        categories = SettingsConfiguration.get_categories()
        
        # Проверяем что возвращается список
        assert isinstance(categories, list)
        assert len(categories) > 0
        
        # Проверяем структуру каждой категории
        for category in categories:
            assert 'id' in category
            assert 'label' in category
            assert 'icon' in category
            assert 'items' in category
            assert isinstance(category['items'], list)
    
    def test_get_categories_content(self):
        """Тест содержимого категорий."""
        categories = SettingsConfiguration.get_categories()
        
        # Находим категорию базовых эффектов
        basic_category = next((cat for cat in categories if cat['id'] == 'basic'), None)
        assert basic_category is not None
        assert basic_category['label'] == 'Базовые эффекты'
        assert basic_category['icon'] == 'sliders'
        
        expected_basic_items = ['scale', 'saturation', 'transparency', 'gamma', 'brightness', 'contrast']
        for item in expected_basic_items:
            assert item in basic_category['items']
        
        # Находим категорию движения
        motion_category = next((cat for cat in categories if cat['id'] == 'motion'), None)
        assert motion_category is not None
        assert motion_category['label'] == 'Движение'
        assert 'movement' in motion_category['items']
        assert 'backgroundscale' in motion_category['items']
    
    def test_settings_consistency(self):
        """Тест согласованности настроек и категорий."""
        settings = SettingsConfiguration.get_default_settings()
        categories = SettingsConfiguration.get_categories()
        
        # Собираем все элементы из категорий
        all_category_items = set()
        for category in categories:
            all_category_items.update(category['items'])
        
        # Проверяем что все элементы из категорий есть в настройках
        for item in all_category_items:
            assert item in settings, f"Элемент {item} из категорий отсутствует в настройках"
    
    def test_translation_completeness(self):
        """Тест полноты переводов."""
        settings = SettingsConfiguration.get_default_settings()
        
        # Проверяем что у всех настроек есть переводы
        for key, setting in settings.items():
            assert 'translation' in setting, f"У настройки {key} отсутствует перевод"
            assert isinstance(setting['translation'], str), f"Перевод для {key} не является строкой"
            assert len(setting['translation']) > 0, f"Пустой перевод для {key}"
    
    def test_range_validity(self):
        """Тест валидности диапазонов."""
        settings = SettingsConfiguration.get_default_settings()
        
        for key, setting in settings.items():
            if 'range' in setting:
                range_val = setting['range']
                assert isinstance(range_val, list), f"Диапазон для {key} не является списком"
                assert len(range_val) == 2, f"Диапазон для {key} должен содержать 2 элемента"
                assert range_val[0] <= range_val[1], f"Неверный диапазон для {key}: {range_val}"
            
            # Проверяем вложенные диапазоны
            for sub_key, sub_setting in setting.items():
                if isinstance(sub_setting, dict) and 'range' in sub_setting:
                    range_val = sub_setting['range']
                    assert isinstance(range_val, list), f"Диапазон для {key}.{sub_key} не является списком"
                    assert len(range_val) == 2, f"Диапазон для {key}.{sub_key} должен содержать 2 элемента"
                    assert range_val[0] <= range_val[1], f"Неверный диапазон для {key}.{sub_key}: {range_val}"
    
    def test_control_types(self):
        """Тест типов контролов."""
        settings = SettingsConfiguration.get_default_settings()
        
        valid_control_types = ['range', 'checkbox', 'select', 'color', 'text']
        
        for key, setting in settings.items():
            if 'controlType' in setting:
                control_type = setting['controlType']
                assert control_type in valid_control_types, f"Неверный тип контрола для {key}: {control_type}"
