"""
Unit тесты для модуля magic_uniq.services.image_server.
"""

import pytest
import asyncio
import os
import time
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from collections import defaultdict

from magic_uniq.services.image_server import ImageServer


class TestImageServer:
    """Тесты для класса ImageServer."""
    
    def test_init(self):
        """Тест инициализации сервера изображений."""
        server = ImageServer()
        
        assert server.background_generator is None
        assert server.overlay_generator is None
        assert server.is_running is True
    
    @pytest.mark.asyncio
    async def test_handle_get_image_success(self, temp_dir):
        """Тест успешного получения изображения."""
        server = ImageServer()
        
        # Создаем тестовые файлы
        backgrounds_dir = temp_dir / "backgrounds"
        backgrounds_dir.mkdir()
        test_image = backgrounds_dir / "test.jpg"
        test_image.write_bytes(b"fake image data")
        
        # Мокаем request
        mock_request = Mock()
        mock_request.query = {"token": "test-token"}
        
        # Мокаем aiofiles
        mock_file = AsyncMock()
        mock_file.read.return_value = b"fake image data"
        
        with patch('magic_uniq.services.image_server.os.listdir', return_value=["test.jpg"]):
            with patch('magic_uniq.services.image_server.os.path.join', return_value=str(test_image)):
                with patch('magic_uniq.services.image_server.aiofiles.open', return_value=mock_file):
                    with patch('magic_uniq.services.image_server.web.Response') as mock_response:
                        with patch('magic_uniq.services.image_server.file_lock', AsyncMock()):
                            
                            result = await server.handle_get_image(mock_request)
                            
                            mock_response.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_handle_get_image_no_token(self):
        """Тест получения изображения без токена."""
        server = ImageServer()
        
        # Мокаем request без токена
        mock_request = Mock()
        mock_request.query = {}
        
        with patch('magic_uniq.services.image_server.web.json_response') as mock_json_response:
            result = await server.handle_get_image(mock_request)
            
            mock_json_response.assert_called_once_with(
                {"status": "error", "message": "Токен не предоставлен"}, 
                status=400
            )
    
    @pytest.mark.asyncio
    async def test_handle_get_image_no_files(self, temp_dir):
        """Тест получения изображения когда нет доступных файлов."""
        server = ImageServer()
        
        mock_request = Mock()
        mock_request.query = {"token": "test-token"}
        
        # Мокаем пустую директорию
        with patch('magic_uniq.services.image_server.os.listdir', return_value=[]):
            with patch('magic_uniq.services.image_server.file_lock', AsyncMock()):
                with patch('magic_uniq.services.image_server.web.json_response') as mock_json_response:
                    
                    result = await server.handle_get_image(mock_request)
                    
                    mock_json_response.assert_called_once_with(
                        {"status": "error", "message": "Нет доступных изображений"}, 
                        status=400
                    )
    
    @pytest.mark.asyncio
    async def test_handle_get_image_all_used(self, temp_dir):
        """Тест получения изображения когда все файлы уже использованы."""
        server = ImageServer()
        
        mock_request = Mock()
        mock_request.query = {"token": "test-token"}
        
        # Мокаем ситуацию когда все файлы уже использованы
        with patch('magic_uniq.services.image_server.os.listdir', return_value=["test.jpg"]):
            with patch('magic_uniq.services.image_server.image_usage_tracking', {"test.jpg": {"test-token"}}):
                with patch('magic_uniq.services.image_server.image_last_used', {"test.jpg": time.time()}):
                    with patch('magic_uniq.services.image_server.file_lock', AsyncMock()):
                        with patch('magic_uniq.services.image_server.web.json_response') as mock_json_response:
                            
                            result = await server.handle_get_image(mock_request)
                            
                            mock_json_response.assert_called_once_with(
                                {"status": "error", "message": "Все доступные изображения уже были использованы"}, 
                                status=429
                            )
    
    @pytest.mark.asyncio
    async def test_handle_get_overlay_success(self, temp_dir):
        """Тест успешного получения оверлея."""
        server = ImageServer()
        
        # Создаем тестовые файлы
        overlay_dir = temp_dir / "overlay"
        overlay_dir.mkdir()
        test_overlay = overlay_dir / "test.png"
        test_overlay.write_bytes(b"fake overlay data")
        
        mock_request = Mock()
        mock_request.query = {"token": "test-token"}
        
        mock_file = AsyncMock()
        mock_file.read.return_value = b"fake overlay data"
        
        with patch('magic_uniq.services.image_server.os.listdir', return_value=["test.png"]):
            with patch('magic_uniq.services.image_server.os.path.join', return_value=str(test_overlay)):
                with patch('magic_uniq.services.image_server.aiofiles.open', return_value=mock_file):
                    with patch('magic_uniq.services.image_server.web.Response') as mock_response:
                        with patch('magic_uniq.services.image_server.file_lock', AsyncMock()):
                            
                            result = await server.handle_get_overlay(mock_request)
                            
                            mock_response.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_handle_get_overlay_no_token(self):
        """Тест получения оверлея без токена."""
        server = ImageServer()
        
        mock_request = Mock()
        mock_request.query = {}
        
        with patch('magic_uniq.services.image_server.web.json_response') as mock_json_response:
            result = await server.handle_get_overlay(mock_request)
            
            mock_json_response.assert_called_once_with(
                {"status": "error", "message": "Токен не предоставлен"}, 
                status=400
            )
    
    @pytest.mark.asyncio
    async def test_run_server_setup(self):
        """Тест настройки и запуска сервера."""
        server = ImageServer()
        
        # Мокаем компоненты aiohttp
        mock_app = Mock()
        mock_router = Mock()
        mock_app.router = mock_router
        
        mock_runner = AsyncMock()
        mock_site = AsyncMock()
        
        with patch('magic_uniq.services.image_server.web.Application', return_value=mock_app):
            with patch('magic_uniq.services.image_server.web.AppRunner', return_value=mock_runner):
                with patch('magic_uniq.services.image_server.web.TCPSite', return_value=mock_site):
                    with patch('asyncio.sleep', side_effect=asyncio.CancelledError()):  # Прерываем цикл
                        
                        try:
                            await server.run_server()
                        except asyncio.CancelledError:
                            pass  # Ожидаемое исключение для прерывания теста
                        
                        # Проверяем что маршруты были добавлены
                        assert mock_router.add_get.call_count == 2
                        
                        # Проверяем что runner был настроен
                        mock_runner.setup.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_run_server_error_handling(self):
        """Тест обработки ошибок при запуске сервера."""
        server = ImageServer()
        
        mock_app = Mock()
        mock_router = Mock()
        mock_app.router = mock_router
        
        mock_runner = AsyncMock()
        mock_site = AsyncMock()
        mock_site.start.side_effect = Exception("Server error")
        
        with patch('magic_uniq.services.image_server.web.Application', return_value=mock_app):
            with patch('magic_uniq.services.image_server.web.AppRunner', return_value=mock_runner):
                with patch('magic_uniq.services.image_server.web.TCPSite', return_value=mock_site):
                    with patch('builtins.print') as mock_print:
                        with patch('asyncio.sleep', side_effect=[None, asyncio.CancelledError()]):
                            
                            try:
                                await server.run_server()
                            except asyncio.CancelledError:
                                pass
                            
                            # Проверяем что ошибка была выведена
                            mock_print.assert_called()
    
    def test_file_usage_tracking_globals(self):
        """Тест глобальных переменных для отслеживания использования файлов."""
        # Импортируем глобальные переменные
        from magic_uniq.services.image_server import (
            image_usage_tracking, 
            image_last_used, 
            file_usage_counter,
            REUSE_TIMEOUT
        )
        
        # Проверяем типы и начальные значения
        assert isinstance(image_usage_tracking, defaultdict)
        assert isinstance(image_last_used, dict)
        assert isinstance(file_usage_counter, dict)
        assert isinstance(REUSE_TIMEOUT, int)
        assert REUSE_TIMEOUT > 0
    
    def test_server_constants(self):
        """Тест констант сервера."""
        from magic_uniq.services.image_server import (
            WEBSERVER_PORT,
            SERVER_IP,
            MAX_IMAGES
        )
        
        assert isinstance(WEBSERVER_PORT, int)
        assert WEBSERVER_PORT > 0
        assert isinstance(SERVER_IP, str)
        assert len(SERVER_IP) > 0
        assert isinstance(MAX_IMAGES, int)
        assert MAX_IMAGES > 0
    
    @pytest.mark.asyncio
    async def test_file_cleanup_logic(self, temp_dir):
        """Тест логики очистки файлов."""
        server = ImageServer()
        
        # Создаем тестовый файл
        backgrounds_dir = temp_dir / "backgrounds"
        backgrounds_dir.mkdir()
        test_image = backgrounds_dir / "test.jpg"
        test_image.write_bytes(b"fake image data")
        
        mock_request = Mock()
        mock_request.query = {"token": "test-token"}
        
        mock_file = AsyncMock()
        mock_file.read.return_value = b"fake image data"
        
        # Мокаем глобальные переменные
        mock_usage_counter = {"test.jpg": 1}
        mock_usage_tracking = defaultdict(set)
        mock_last_used = {}
        
        with patch('magic_uniq.services.image_server.os.listdir', return_value=["test.jpg"]):
            with patch('magic_uniq.services.image_server.os.path.join', return_value=str(test_image)):
                with patch('magic_uniq.services.image_server.aiofiles.open', return_value=mock_file):
                    with patch('magic_uniq.services.image_server.file_usage_counter', mock_usage_counter):
                        with patch('magic_uniq.services.image_server.image_usage_tracking', mock_usage_tracking):
                            with patch('magic_uniq.services.image_server.image_last_used', mock_last_used):
                                with patch('magic_uniq.services.image_server.file_lock', AsyncMock()):
                                    with patch('magic_uniq.services.image_server.os.remove') as mock_remove:
                                        
                                        # Симулируем завершение использования файла
                                        mock_usage_counter["test.jpg"] = 0
                                        
                                        # Вызываем обработчик (в реальности это происходит в finally блоке)
                                        # Здесь мы тестируем только логику
                                        if mock_usage_counter.get("test.jpg") == 0:
                                            mock_remove(str(test_image))
                                        
                                        mock_remove.assert_called_once_with(str(test_image))
    
    @pytest.mark.asyncio
    async def test_reuse_timeout_logic(self):
        """Тест логики таймаута повторного использования."""
        server = ImageServer()
        
        mock_request = Mock()
        mock_request.query = {"token": "test-token"}
        
        current_time = time.time()
        old_time = current_time - 400  # Старше чем REUSE_TIMEOUT (300 секунд)
        
        # Мокаем файл который был использован давно
        with patch('magic_uniq.services.image_server.os.listdir', return_value=["old_file.jpg"]):
            with patch('magic_uniq.services.image_server.image_usage_tracking', {"old_file.jpg": {"other-token"}}):
                with patch('magic_uniq.services.image_server.image_last_used', {"old_file.jpg": old_time}):
                    with patch('magic_uniq.services.image_server.time.time', return_value=current_time):
                        with patch('magic_uniq.services.image_server.file_lock', AsyncMock()):
                            with patch('magic_uniq.services.image_server.random.choice', return_value="old_file.jpg"):
                                with patch('magic_uniq.services.image_server.os.path.join', return_value="/path/old_file.jpg"):
                                    with patch('magic_uniq.services.image_server.aiofiles.open', AsyncMock()):
                                        with patch('magic_uniq.services.image_server.web.Response') as mock_response:
                                            
                                            result = await server.handle_get_image(mock_request)
                                            
                                            # Файл должен быть доступен для повторного использования
                                            mock_response.assert_called_once()
