"""
Unit тесты для модуля magic_uniq.config.validation.
"""

import pytest
from magic_uniq.config.validation import ConfigValidator


class TestConfigValidator:
    """Тесты для класса ConfigValidator."""
    
    def test_init(self):
        """Тест инициализации валидатора."""
        validator = ConfigValidator()
        
        assert validator.errors == []
        assert validator.warnings == []
    
    def test_validate_config_valid(self, sample_config):
        """Тест валидации корректной конфигурации."""
        validator = ConfigValidator()
        
        result = validator.validate_config(sample_config)
        
        assert result is True
        assert len(validator.errors) == 0
    
    def test_validate_config_empty(self):
        """Тест валидации пустой конфигурации."""
        validator = ConfigValidator()
        
        result = validator.validate_config({})
        
        assert result is True  # Пустая конфигурация валидна
        assert len(validator.errors) == 0
    
    def test_validate_directories_valid(self):
        """Тест валидации корректных директорий."""
        validator = ConfigValidator()
        directories = {
            "input": "input_images",
            "output": "output_images",
            "backgrounds": "backgrounds"
        }
        
        validator._validate_directories(directories)
        
        assert len(validator.errors) == 0
    
    def test_validate_directories_invalid_type(self):
        """Тест валидации директорий с неверным типом."""
        validator = ConfigValidator()
        directories = {
            "input": 123,  # Должна быть строка
            "output": None
        }
        
        validator._validate_directories(directories)
        
        assert len(validator.errors) == 2
        assert "input должен быть строкой" in validator.errors[0]
        assert "output должен быть строкой" in validator.errors[1]
    
    def test_validate_image_processing_valid(self):
        """Тест валидации корректных настроек обработки изображений."""
        validator = ConfigValidator()
        image_config = {
            "max_size": 4096,
            "quality": 95,
            "format": "JPEG"
        }
        
        validator._validate_image_processing(image_config)
        
        assert len(validator.errors) == 0
    
    def test_validate_image_processing_invalid_max_size(self):
        """Тест валидации неверного max_size."""
        validator = ConfigValidator()
        
        # Тест отрицательного значения
        validator._validate_image_processing({"max_size": -100})
        assert len(validator.errors) == 1
        assert "max_size должен быть положительным целым числом" in validator.errors[0]
        
        # Тест нецелого значения
        validator.errors.clear()
        validator._validate_image_processing({"max_size": 100.5})
        assert len(validator.errors) == 1
        
        # Тест нуля
        validator.errors.clear()
        validator._validate_image_processing({"max_size": 0})
        assert len(validator.errors) == 1
    
    def test_validate_image_processing_invalid_quality(self):
        """Тест валидации неверного quality."""
        validator = ConfigValidator()
        
        # Тест значения меньше 1
        validator._validate_image_processing({"quality": 0})
        assert len(validator.errors) == 1
        assert "quality должен быть целым числом от 1 до 100" in validator.errors[0]
        
        # Тест значения больше 100
        validator.errors.clear()
        validator._validate_image_processing({"quality": 101})
        assert len(validator.errors) == 1
        
        # Тест нецелого значения
        validator.errors.clear()
        validator._validate_image_processing({"quality": 95.5})
        assert len(validator.errors) == 1
    
    def test_validate_image_processing_invalid_format(self):
        """Тест валидации неверного формата."""
        validator = ConfigValidator()
        
        validator._validate_image_processing({"format": 123})
        
        assert len(validator.errors) == 1
        assert "format должен быть строкой" in validator.errors[0]
    
    def test_validate_video_processing_valid(self):
        """Тест валидации корректных настроек обработки видео."""
        validator = ConfigValidator()
        video_config = {
            "max_resolution": "1920x1080",
            "fps": 30,
            "codec": "h264"
        }
        
        validator._validate_video_processing(video_config)
        
        assert len(validator.errors) == 0
    
    def test_validate_video_processing_invalid_resolution(self):
        """Тест валидации неверного разрешения."""
        validator = ConfigValidator()
        
        # Тест неверного формата
        validator._validate_video_processing({"max_resolution": "invalid"})
        assert len(validator.errors) == 1
        assert "max_resolution должен быть в формате WIDTHxHEIGHT" in validator.errors[0]
        
        # Тест нестрокового типа
        validator.errors.clear()
        validator._validate_video_processing({"max_resolution": 1920})
        assert len(validator.errors) == 1
    
    def test_validate_video_processing_invalid_fps(self):
        """Тест валидации неверного FPS."""
        validator = ConfigValidator()
        
        # Тест отрицательного значения
        validator._validate_video_processing({"fps": -10})
        assert len(validator.errors) == 1
        assert "fps должен быть положительным числом" in validator.errors[0]
        
        # Тест нуля
        validator.errors.clear()
        validator._validate_video_processing({"fps": 0})
        assert len(validator.errors) == 1
        
        # Тест нечислового значения
        validator.errors.clear()
        validator._validate_video_processing({"fps": "30"})
        assert len(validator.errors) == 1
    
    def test_validate_effects_valid(self):
        """Тест валидации корректных настроек эффектов."""
        validator = ConfigValidator()
        effects_config = {
            "enabled": True,
            "intensity": 0.5
        }
        
        validator._validate_effects(effects_config)
        
        assert len(validator.errors) == 0
    
    def test_validate_effects_invalid_enabled(self):
        """Тест валидации неверного enabled."""
        validator = ConfigValidator()
        
        validator._validate_effects({"enabled": "true"})
        
        assert len(validator.errors) == 1
        assert "enabled должен быть булевым значением" in validator.errors[0]
    
    def test_validate_effects_invalid_intensity(self):
        """Тест валидации неверной интенсивности."""
        validator = ConfigValidator()
        
        # Тест отрицательного значения
        validator._validate_effects({"intensity": -0.5})
        assert len(validator.errors) == 1
        assert "intensity должен быть числом от 0.0 до 1.0" in validator.errors[0]
        
        # Тест значения больше 1
        validator.errors.clear()
        validator._validate_effects({"intensity": 1.5})
        assert len(validator.errors) == 1
        
        # Тест нечислового значения
        validator.errors.clear()
        validator._validate_effects({"intensity": "0.5"})
        assert len(validator.errors) == 1
    
    def test_validate_metadata_valid(self):
        """Тест валидации корректных настроек метаданных."""
        validator = ConfigValidator()
        metadata_config = {
            "remove_original": True,
            "add_custom": False
        }
        
        validator._validate_metadata(metadata_config)
        
        assert len(validator.errors) == 0
    
    def test_validate_metadata_invalid(self):
        """Тест валидации неверных настроек метаданных."""
        validator = ConfigValidator()
        
        validator._validate_metadata({
            "remove_original": "yes",
            "add_custom": 1
        })
        
        assert len(validator.errors) == 2
        assert "remove_original должен быть булевым значением" in validator.errors[0]
        assert "add_custom должен быть булевым значением" in validator.errors[1]
    
    def test_validate_server_valid(self):
        """Тест валидации корректных настроек сервера."""
        validator = ConfigValidator()
        server_config = {
            "host": "localhost",
            "port": 5000,
            "debug": False
        }
        
        validator._validate_server(server_config)
        
        assert len(validator.errors) == 0
    
    def test_validate_server_invalid_host(self):
        """Тест валидации неверного хоста."""
        validator = ConfigValidator()
        
        validator._validate_server({"host": 123})
        
        assert len(validator.errors) == 1
        assert "server.host должен быть строкой" in validator.errors[0]
    
    def test_validate_server_invalid_port(self):
        """Тест валидации неверного порта."""
        validator = ConfigValidator()
        
        # Тест отрицательного порта
        validator._validate_server({"port": -1})
        assert len(validator.errors) == 1
        assert "server.port должен быть целым числом от 1 до 65535" in validator.errors[0]
        
        # Тест слишком большого порта
        validator.errors.clear()
        validator._validate_server({"port": 70000})
        assert len(validator.errors) == 1
        
        # Тест нецелого значения
        validator.errors.clear()
        validator._validate_server({"port": 5000.5})
        assert len(validator.errors) == 1
    
    def test_validate_server_invalid_debug(self):
        """Тест валидации неверного debug."""
        validator = ConfigValidator()
        
        validator._validate_server({"debug": "false"})
        
        assert len(validator.errors) == 1
        assert "server.debug должен быть булевым значением" in validator.errors[0]
    
    def test_get_errors(self):
        """Тест получения списка ошибок."""
        validator = ConfigValidator()
        validator.errors = ["error1", "error2"]
        
        errors = validator.get_errors()
        
        assert errors == ["error1", "error2"]
        assert errors is not validator.errors  # Должна быть копия
    
    def test_get_warnings(self):
        """Тест получения списка предупреждений."""
        validator = ConfigValidator()
        validator.warnings = ["warning1", "warning2"]
        
        warnings = validator.get_warnings()
        
        assert warnings == ["warning1", "warning2"]
        assert warnings is not validator.warnings  # Должна быть копия
    
    def test_complex_validation(self):
        """Тест комплексной валидации с несколькими ошибками."""
        validator = ConfigValidator()
        invalid_config = {
            "directories": {
                "input": 123  # Неверный тип
            },
            "image_processing": {
                "quality": 150  # Неверное значение
            },
            "server": {
                "port": -1  # Неверный порт
            }
        }
        
        result = validator.validate_config(invalid_config)
        
        assert result is False
        assert len(validator.errors) == 3
