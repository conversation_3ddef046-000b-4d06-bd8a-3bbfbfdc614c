"""
Unit тесты для модуля magic_uniq.utils.system_utils.
"""

import pytest
import platform
import subprocess
from unittest.mock import patch, Mock, mock_open

from magic_uniq.utils.system_utils import get_system_uuid, get_system_info


class TestSystemUtils:
    """Тесты для системных утилит."""
    
    def test_get_system_uuid_windows(self):
        """Тест получения UUID в Windows."""
        mock_output = """


UUID
----
12345678-1234-5678-9012-123456789012


"""
        
        with patch('platform.system', return_value='Windows'):
            with patch('subprocess.check_output', return_value=mock_output.encode()):
                uuid = get_system_uuid()
                
                assert uuid == "12345678-1234-5678-9012-123456789012"
    
    def test_get_system_uuid_macos(self):
        """Тест получения UUID в macOS."""
        mock_output = """Hardware:

    Hardware Overview:

      Model Name: MacBook Pro
      Hardware UUID: ABCDEF12-3456-7890-ABCD-EF1234567890
      Processor Name: Apple M1
"""
        
        with patch('platform.system', return_value='Darwin'):
            with patch('subprocess.check_output', return_value=mock_output):
                uuid = get_system_uuid()
                
                assert uuid == "ABCDEF12-3456-7890-ABCD-EF1234567890"
    
    def test_get_system_uuid_linux_sys_file(self):
        """Тест получения UUID в Linux через /sys файл."""
        test_uuid = "fedcba98-7654-3210-fedc-ba9876543210"
        
        with patch('platform.system', return_value='Linux'):
            with patch('builtins.open', mock_open(read_data=test_uuid + '\n')):
                uuid = get_system_uuid()
                
                assert uuid == test_uuid
    
    def test_get_system_uuid_linux_dmidecode(self):
        """Тест получения UUID в Linux через dmidecode."""
        test_uuid = "11111111-**************-************"
        
        with patch('platform.system', return_value='Linux'):
            with patch('builtins.open', side_effect=FileNotFoundError()):
                with patch('subprocess.check_output', return_value=test_uuid):
                    uuid = get_system_uuid()
                    
                    assert uuid == test_uuid
    
    def test_get_system_uuid_linux_fallback_to_mac(self):
        """Тест fallback к MAC-адресу в Linux."""
        with patch('platform.system', return_value='Linux'):
            with patch('builtins.open', side_effect=FileNotFoundError()):
                with patch('subprocess.check_output', side_effect=subprocess.CalledProcessError(1, 'dmidecode')):
                    with patch('uuid.uuid1', return_value=Mock(spec=['__str__'])) as mock_uuid1:
                        mock_uuid1.return_value.__str__ = Mock(return_value="mac-based-uuid")
                        
                        uuid = get_system_uuid()
                        
                        assert uuid == "mac-based-uuid"
                        mock_uuid1.assert_called_once()
    
    def test_get_system_uuid_unknown_system(self):
        """Тест получения UUID для неизвестной системы."""
        with patch('platform.system', return_value='UnknownOS'):
            with patch('uuid.uuid1', return_value=Mock(spec=['__str__'])) as mock_uuid1:
                mock_uuid1.return_value.__str__ = Mock(return_value="fallback-uuid")
                
                uuid = get_system_uuid()
                
                assert uuid == "fallback-uuid"
                mock_uuid1.assert_called_once()
    
    def test_get_system_uuid_exception_fallback(self):
        """Тест fallback при исключении."""
        with patch('platform.system', side_effect=Exception("System error")):
            with patch('uuid.uuid4', return_value=Mock(spec=['__str__'])) as mock_uuid4:
                mock_uuid4.return_value.__str__ = Mock(return_value="random-uuid")
                with patch('builtins.print') as mock_print:
                    
                    uuid = get_system_uuid()
                    
                    assert uuid == "random-uuid"
                    mock_uuid4.assert_called_once()
                    mock_print.assert_called_once()
    
    def test_get_system_uuid_windows_subprocess_error(self):
        """Тест обработки ошибки subprocess в Windows."""
        with patch('platform.system', return_value='Windows'):
            with patch('subprocess.check_output', side_effect=subprocess.CalledProcessError(1, 'powershell')):
                with patch('uuid.uuid1', return_value=Mock(spec=['__str__'])) as mock_uuid1:
                    mock_uuid1.return_value.__str__ = Mock(return_value="fallback-uuid")
                    
                    uuid = get_system_uuid()
                    
                    assert uuid == "fallback-uuid"
    
    def test_get_system_uuid_macos_subprocess_error(self):
        """Тест обработки ошибки subprocess в macOS."""
        with patch('platform.system', return_value='Darwin'):
            with patch('subprocess.check_output', side_effect=subprocess.CalledProcessError(1, 'system_profiler')):
                with patch('uuid.uuid1', return_value=Mock(spec=['__str__'])) as mock_uuid1:
                    mock_uuid1.return_value.__str__ = Mock(return_value="fallback-uuid")
                    
                    uuid = get_system_uuid()
                    
                    assert uuid == "fallback-uuid"
    
    def test_get_system_uuid_windows_no_uuid_in_output(self):
        """Тест когда в выводе Windows нет UUID."""
        mock_output = "Some other output without UUID"
        
        with patch('platform.system', return_value='Windows'):
            with patch('subprocess.check_output', return_value=mock_output.encode()):
                with patch('uuid.uuid1', return_value=Mock(spec=['__str__'])) as mock_uuid1:
                    mock_uuid1.return_value.__str__ = Mock(return_value="fallback-uuid")
                    
                    uuid = get_system_uuid()
                    
                    assert uuid == "fallback-uuid"
    
    def test_get_system_uuid_macos_no_uuid_in_output(self):
        """Тест когда в выводе macOS нет Hardware UUID."""
        mock_output = "Hardware info without UUID field"
        
        with patch('platform.system', return_value='Darwin'):
            with patch('subprocess.check_output', return_value=mock_output):
                with patch('uuid.uuid1', return_value=Mock(spec=['__str__'])) as mock_uuid1:
                    mock_uuid1.return_value.__str__ = Mock(return_value="fallback-uuid")
                    
                    uuid = get_system_uuid()
                    
                    assert uuid == "fallback-uuid"
    
    def test_get_system_info_structure(self):
        """Тест структуры информации о системе."""
        with patch('platform.system', return_value='TestOS'):
            with patch('platform.release', return_value='1.0'):
                with patch('platform.version', return_value='1.0.0'):
                    with patch('platform.machine', return_value='x86_64'):
                        with patch('platform.processor', return_value='Intel'):
                            with patch('platform.python_version', return_value='3.9.0'):
                                with patch('magic_uniq.utils.system_utils.get_system_uuid', return_value='test-uuid'):
                                    
                                    info = get_system_info()
                                    
                                    expected_keys = [
                                        'platform', 'platform_release', 'platform_version',
                                        'architecture', 'processor', 'python_version', 'uuid'
                                    ]
                                    
                                    for key in expected_keys:
                                        assert key in info
                                    
                                    assert info['platform'] == 'TestOS'
                                    assert info['platform_release'] == '1.0'
                                    assert info['platform_version'] == '1.0.0'
                                    assert info['architecture'] == 'x86_64'
                                    assert info['processor'] == 'Intel'
                                    assert info['python_version'] == '3.9.0'
                                    assert info['uuid'] == 'test-uuid'
    
    def test_get_system_info_real_values(self):
        """Тест получения реальных значений системной информации."""
        info = get_system_info()
        
        # Проверяем что все ключи присутствуют
        expected_keys = [
            'platform', 'platform_release', 'platform_version',
            'architecture', 'processor', 'python_version', 'uuid'
        ]
        
        for key in expected_keys:
            assert key in info
            assert info[key] is not None
        
        # Проверяем что значения не пустые строки
        for key, value in info.items():
            if isinstance(value, str):
                assert len(value) > 0
    
    def test_get_system_uuid_case_insensitive_platform(self):
        """Тест что определение платформы работает независимо от регистра."""
        test_cases = [
            ('windows', 'Windows'),
            ('WINDOWS', 'Windows'),
            ('Windows', 'Windows'),
            ('darwin', 'Darwin'),
            ('DARWIN', 'Darwin'),
            ('Darwin', 'Darwin'),
            ('linux', 'Linux'),
            ('LINUX', 'Linux'),
            ('Linux', 'Linux')
        ]
        
        for platform_return, expected_behavior in test_cases:
            with patch('platform.system', return_value=platform_return):
                with patch('uuid.uuid1', return_value=Mock(spec=['__str__'])) as mock_uuid1:
                    mock_uuid1.return_value.__str__ = Mock(return_value="test-uuid")
                    
                    # Мокаем все возможные subprocess вызовы
                    with patch('subprocess.check_output', side_effect=Exception("No subprocess")):
                        with patch('builtins.open', side_effect=FileNotFoundError()):
                            
                            uuid = get_system_uuid()
                            
                            # Должен использоваться fallback
                            assert uuid == "test-uuid"
    
    def test_get_system_uuid_linux_permission_error(self):
        """Тест обработки ошибки доступа к /sys файлу в Linux."""
        with patch('platform.system', return_value='Linux'):
            with patch('builtins.open', side_effect=PermissionError("Access denied")):
                with patch('subprocess.check_output', side_effect=subprocess.CalledProcessError(1, 'dmidecode')):
                    with patch('uuid.uuid1', return_value=Mock(spec=['__str__'])) as mock_uuid1:
                        mock_uuid1.return_value.__str__ = Mock(return_value="fallback-uuid")
                        
                        uuid = get_system_uuid()
                        
                        assert uuid == "fallback-uuid"
    
    def test_get_system_uuid_windows_empty_lines(self):
        """Тест обработки пустых строк в выводе Windows."""
        mock_output = """


UUID
----



"""
        
        with patch('platform.system', return_value='Windows'):
            with patch('subprocess.check_output', return_value=mock_output.encode()):
                with patch('uuid.uuid1', return_value=Mock(spec=['__str__'])) as mock_uuid1:
                    mock_uuid1.return_value.__str__ = Mock(return_value="fallback-uuid")
                    
                    uuid = get_system_uuid()
                    
                    assert uuid == "fallback-uuid"
