"""
Unit тесты для модуля magic_uniq.utils.file_utils.
"""

import pytest
import os
import shutil
from pathlib import Path
from unittest.mock import patch, Mock

from magic_uniq.utils.file_utils import FileUtils


class TestFileUtils:
    """Тесты для класса FileUtils."""
    
    def test_ensure_directory_new_directory(self, temp_dir):
        """Тест создания новой директории."""
        new_dir = temp_dir / "new_directory"
        
        result = FileUtils.ensure_directory(new_dir)
        
        assert result == new_dir
        assert new_dir.exists()
        assert new_dir.is_dir()
    
    def test_ensure_directory_existing_directory(self, temp_dir):
        """Тест с существующей директорией."""
        existing_dir = temp_dir / "existing"
        existing_dir.mkdir()
        
        result = FileUtils.ensure_directory(existing_dir)
        
        assert result == existing_dir
        assert existing_dir.exists()
    
    def test_ensure_directory_nested_path(self, temp_dir):
        """Тест создания вложенных директорий."""
        nested_dir = temp_dir / "level1" / "level2" / "level3"
        
        result = FileUtils.ensure_directory(nested_dir)
        
        assert result == nested_dir
        assert nested_dir.exists()
        assert nested_dir.is_dir()
    
    def test_ensure_directory_string_path(self, temp_dir):
        """Тест с путем в виде строки."""
        new_dir_str = str(temp_dir / "string_dir")
        
        result = FileUtils.ensure_directory(new_dir_str)
        
        assert result == Path(new_dir_str)
        assert Path(new_dir_str).exists()
    
    def test_is_image_file_valid_images(self, temp_dir):
        """Тест определения файлов изображений."""
        # Создаем файлы с расширениями изображений
        image_files = [
            temp_dir / "test.jpg",
            temp_dir / "test.jpeg",
            temp_dir / "test.png",
            temp_dir / "test.gif",
            temp_dir / "test.bmp",
            temp_dir / "test.webp"
        ]
        
        for img_file in image_files:
            img_file.touch()
            assert FileUtils.is_image_file(img_file) is True
    
    def test_is_image_file_non_images(self, temp_dir):
        """Тест с файлами, не являющимися изображениями."""
        non_image_files = [
            temp_dir / "test.txt",
            temp_dir / "test.pdf",
            temp_dir / "test.mp4",
            temp_dir / "test.doc"
        ]
        
        for file in non_image_files:
            file.touch()
            assert FileUtils.is_image_file(file) is False
    
    def test_is_image_file_no_extension(self, temp_dir):
        """Тест с файлом без расширения."""
        no_ext_file = temp_dir / "no_extension"
        no_ext_file.touch()
        
        assert FileUtils.is_image_file(no_ext_file) is False
    
    def test_is_video_file_valid_videos(self, temp_dir):
        """Тест определения видео файлов."""
        video_files = [
            temp_dir / "test.mp4",
            temp_dir / "test.avi",
            temp_dir / "test.mov",
            temp_dir / "test.mkv",
            temp_dir / "test.webm"
        ]
        
        for video_file in video_files:
            video_file.touch()
            assert FileUtils.is_video_file(video_file) is True
    
    def test_is_video_file_non_videos(self, temp_dir):
        """Тест с файлами, не являющимися видео."""
        non_video_files = [
            temp_dir / "test.txt",
            temp_dir / "test.jpg",
            temp_dir / "test.pdf"
        ]
        
        for file in non_video_files:
            file.touch()
            assert FileUtils.is_video_file(file) is False
    
    def test_get_files_by_extension_existing_files(self, temp_dir):
        """Тест получения файлов по расширению."""
        # Создаем файлы с разными расширениями
        files = [
            temp_dir / "image1.jpg",
            temp_dir / "image2.JPG",
            temp_dir / "image3.png",
            temp_dir / "document.txt",
            temp_dir / "video.mp4"
        ]
        
        for file in files:
            file.touch()
        
        # Ищем jpg файлы
        jpg_files = FileUtils.get_files_by_extension(temp_dir, ['.jpg'])
        
        assert len(jpg_files) == 2
        assert temp_dir / "image1.jpg" in jpg_files
        assert temp_dir / "image2.JPG" in jpg_files
    
    def test_get_files_by_extension_multiple_extensions(self, temp_dir):
        """Тест получения файлов с несколькими расширениями."""
        files = [
            temp_dir / "image1.jpg",
            temp_dir / "image2.png",
            temp_dir / "image3.gif",
            temp_dir / "document.txt"
        ]
        
        for file in files:
            file.touch()
        
        image_files = FileUtils.get_files_by_extension(temp_dir, ['.jpg', '.png', '.gif'])
        
        assert len(image_files) == 3
        assert all(f.suffix.lower() in ['.jpg', '.png', '.gif'] for f in image_files)
    
    def test_get_files_by_extension_empty_directory(self, temp_dir):
        """Тест с пустой директорией."""
        empty_dir = temp_dir / "empty"
        empty_dir.mkdir()
        
        files = FileUtils.get_files_by_extension(empty_dir, ['.jpg'])
        
        assert files == []
    
    def test_get_files_by_extension_non_existing_directory(self, temp_dir):
        """Тест с несуществующей директорией."""
        non_existing = temp_dir / "non_existing"
        
        files = FileUtils.get_files_by_extension(non_existing, ['.jpg'])
        
        assert files == []
    
    def test_safe_filename_invalid_characters(self):
        """Тест очистки недопустимых символов в имени файла."""
        unsafe_name = 'file<>:"/\\|?*name.txt'
        
        safe_name = FileUtils.safe_filename(unsafe_name)
        
        assert safe_name == 'file_________name.txt'
    
    def test_safe_filename_long_name(self):
        """Тест обрезки слишком длинного имени файла."""
        long_name = 'a' * 300 + '.txt'
        
        safe_name = FileUtils.safe_filename(long_name)
        
        assert len(safe_name) <= 255
        assert safe_name.endswith('.txt')
    
    def test_safe_filename_normal_name(self):
        """Тест с нормальным именем файла."""
        normal_name = 'normal_file_name.jpg'
        
        safe_name = FileUtils.safe_filename(normal_name)
        
        assert safe_name == normal_name
    
    def test_copy_file_safe_success(self, temp_dir):
        """Тест успешного копирования файла."""
        src_file = temp_dir / "source.txt"
        dst_file = temp_dir / "destination.txt"
        
        src_file.write_text("test content")
        
        result = FileUtils.copy_file_safe(src_file, dst_file)
        
        assert result is True
        assert dst_file.exists()
        assert dst_file.read_text() == "test content"
    
    def test_copy_file_safe_create_directory(self, temp_dir):
        """Тест копирования с созданием директории."""
        src_file = temp_dir / "source.txt"
        dst_file = temp_dir / "subdir" / "destination.txt"
        
        src_file.write_text("test content")
        
        result = FileUtils.copy_file_safe(src_file, dst_file)
        
        assert result is True
        assert dst_file.exists()
        assert dst_file.parent.exists()
    
    def test_copy_file_safe_source_not_exists(self, temp_dir):
        """Тест копирования несуществующего файла."""
        src_file = temp_dir / "non_existing.txt"
        dst_file = temp_dir / "destination.txt"
        
        result = FileUtils.copy_file_safe(src_file, dst_file)
        
        assert result is False
        assert not dst_file.exists()
    
    def test_copy_file_safe_permission_error(self, temp_dir):
        """Тест обработки ошибки доступа при копировании."""
        src_file = temp_dir / "source.txt"
        dst_file = temp_dir / "destination.txt"
        
        src_file.write_text("test content")
        
        with patch('shutil.copy2', side_effect=PermissionError("Access denied")):
            result = FileUtils.copy_file_safe(src_file, dst_file)
            
            assert result is False
    
    def test_get_file_size_existing_file(self, temp_dir):
        """Тест получения размера существующего файла."""
        test_file = temp_dir / "test.txt"
        content = "test content"
        test_file.write_text(content)
        
        size = FileUtils.get_file_size(test_file)
        
        assert size == len(content.encode())
    
    def test_get_file_size_non_existing_file(self, temp_dir):
        """Тест получения размера несуществующего файла."""
        non_existing = temp_dir / "non_existing.txt"
        
        size = FileUtils.get_file_size(non_existing)
        
        assert size == -1
    
    def test_get_file_size_string_path(self, temp_dir):
        """Тест получения размера файла по строковому пути."""
        test_file = temp_dir / "test.txt"
        content = "test content"
        test_file.write_text(content)
        
        size = FileUtils.get_file_size(str(test_file))
        
        assert size == len(content.encode())
    
    def test_cleanup_temp_files_success(self, temp_dir):
        """Тест успешной очистки временных файлов."""
        # Создаем временные файлы
        temp_files = [
            temp_dir / "temp1.tmp",
            temp_dir / "temp2.tmp",
            temp_dir / "keep.txt"
        ]
        
        for file in temp_files:
            file.write_text("content")
        
        count = FileUtils.cleanup_temp_files(temp_dir, "*.tmp")
        
        assert count == 2
        assert not (temp_dir / "temp1.tmp").exists()
        assert not (temp_dir / "temp2.tmp").exists()
        assert (temp_dir / "keep.txt").exists()
    
    def test_cleanup_temp_files_all_pattern(self, temp_dir):
        """Тест очистки всех файлов."""
        files = [
            temp_dir / "file1.txt",
            temp_dir / "file2.jpg",
            temp_dir / "file3.tmp"
        ]
        
        for file in files:
            file.write_text("content")
        
        count = FileUtils.cleanup_temp_files(temp_dir, "*")
        
        assert count == 3
        assert not any(f.exists() for f in files)
    
    def test_cleanup_temp_files_non_existing_directory(self, temp_dir):
        """Тест очистки в несуществующей директории."""
        non_existing = temp_dir / "non_existing"
        
        count = FileUtils.cleanup_temp_files(non_existing, "*")
        
        assert count == 0
    
    def test_cleanup_temp_files_permission_error(self, temp_dir):
        """Тест обработки ошибки доступа при очистке."""
        test_file = temp_dir / "test.tmp"
        test_file.write_text("content")

        with patch.object(Path, 'unlink', side_effect=OSError("Permission denied")):
            count = FileUtils.cleanup_temp_files(temp_dir, "*.tmp")

            # Файл должен остаться, но ошибка должна быть обработана
            assert count == 0
            assert test_file.exists()

    def test_cleanup_temp_files_mixed_files_and_dirs(self, temp_dir):
        """Тест очистки смешанного содержимого (файлы и директории)."""
        # Создаем файлы и директории
        test_file = temp_dir / "test.tmp"
        test_dir = temp_dir / "test_dir.tmp"

        test_file.write_text("content")
        test_dir.mkdir()

        count = FileUtils.cleanup_temp_files(temp_dir, "*.tmp")

        # Должен быть удален только файл, не директория
        assert count == 1
        assert not test_file.exists()
        assert test_dir.exists()  # Директория должна остаться
