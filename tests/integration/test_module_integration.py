"""
Интеграционные тесты для модулей MagicUniq.
"""

import pytest
from pathlib import Path
from unittest.mock import patch, Mock


class TestModuleIntegration:
    """Тесты интеграции между модулями."""
    
    def test_settings_and_file_utils_integration(self, temp_dir):
        """Тест интеграции Settings и FileUtils."""
        from magic_uniq.config.settings import Settings
        from magic_uniq.utils.file_utils import FileUtils
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            # Получаем директорию через Settings
            bg_dir = settings.get_backgrounds_dir()
            
            # Используем FileUtils для работы с директорией
            FileUtils.ensure_directory(bg_dir)
            
            assert bg_dir.exists()
            assert bg_dir.is_dir()
    
    def test_settings_and_validation_integration(self, temp_dir, sample_config):
        """Тест интеграции Settings и ConfigValidator."""
        from magic_uniq.config.settings import Settings
        from magic_uniq.config.validation import ConfigValidator
        
        validator = ConfigValidator()
        
        # Валидируем конфигурацию
        is_valid = validator.validate_config(sample_config)
        assert is_valid is True
        
        # Используем валидную конфигурацию в Settings
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            # Проверяем что настройки загружены корректно
            assert settings.get("image_processing.quality") == 95
    
    def test_file_utils_and_image_utils_integration(self, temp_dir):
        """Тест интеграции FileUtils и ImageUtils."""
        from magic_uniq.utils.file_utils import FileUtils
        
        # Создаем тестовый файл изображения
        test_image = temp_dir / "test.jpg"
        test_image.write_bytes(b'\xff\xd8\xff\xe0\x00\x10JFIF')  # JPEG header
        
        # Проверяем через FileUtils
        assert FileUtils.is_image_file(test_image) is True
        
        # Получаем размер файла
        size = FileUtils.get_file_size(test_image)
        assert size > 0
    
    def test_settings_and_system_utils_integration(self, temp_dir):
        """Тест интеграции Settings и system_utils."""
        from magic_uniq.config.settings import Settings
        from magic_uniq.utils.system_utils import get_system_uuid
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            uuid = get_system_uuid()
            
            # Проверяем что UUID получен
            assert uuid is not None
            assert len(uuid) > 0
            
            # Можем сохранить UUID в настройках
            settings.set("system.uuid", uuid)
            assert settings.get("system.uuid") == uuid
    
    def test_video_settings_and_validation_integration(self):
        """Тест интеграции video_settings и validation."""
        from magic_uniq.config.video_settings import SettingsConfiguration
        from magic_uniq.config.validation import ConfigValidator
        
        # Получаем настройки по умолчанию
        default_settings = SettingsConfiguration.get_default_settings()
        
        # Создаем конфигурацию для валидации
        test_config = {
            "effects": {
                "enabled": True,
                "intensity": 0.5
            }
        }
        
        validator = ConfigValidator()
        is_valid = validator.validate_config(test_config)
        
        assert is_valid is True
        assert isinstance(default_settings, dict)
        assert len(default_settings) > 0
    
    def test_core_classes_and_settings_integration(self, temp_dir):
        """Тест интеграции core.classes и settings."""
        from magic_uniq.config.settings import Settings
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            # Мокаем VideoProcessor для избежания зависимостей
            with patch.dict('sys.modules', {'cv2': Mock(), 'numpy': Mock()}):
                from magic_uniq.core.classes import VideoProcessor
                
                processor = VideoProcessor(settings)
                
                assert processor.settings_manager == settings
                assert processor.backgrounds_dir.exists()
    
    def test_services_and_utils_integration(self, temp_dir):
        """Тест интеграции services и utils."""
        from magic_uniq.utils.file_utils import FileUtils
        from magic_uniq.utils.system_utils import get_system_uuid
        
        # Создаем директорию для сервиса
        service_dir = temp_dir / "service_data"
        FileUtils.ensure_directory(service_dir)
        
        # Получаем UUID для сервиса
        uuid = get_system_uuid()
        
        # Создаем файл с UUID
        uuid_file = service_dir / "service.uuid"
        uuid_file.write_text(uuid)
        
        assert uuid_file.exists()
        assert FileUtils.get_file_size(uuid_file) > 0
    
    def test_config_modules_integration(self, temp_dir, sample_config):
        """Тест интеграции всех модулей config."""
        from magic_uniq.config.settings import Settings
        from magic_uniq.config.validation import ConfigValidator
        from magic_uniq.config.video_settings import SettingsConfiguration
        
        # Валидируем конфигурацию
        validator = ConfigValidator()
        is_valid = validator.validate_config(sample_config)
        assert is_valid is True
        
        # Загружаем в Settings
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            # Получаем настройки видео
            video_settings = SettingsConfiguration.get_default_settings()
            
            # Проверяем интеграцию
            assert settings.get("image_processing.quality") == 95
            assert isinstance(video_settings, dict)
            assert len(validator.get_errors()) == 0
    
    def test_utils_modules_integration(self, temp_dir):
        """Тест интеграции всех модулей utils."""
        from magic_uniq.utils.file_utils import FileUtils
        from magic_uniq.utils.system_utils import get_system_uuid, get_system_info
        
        # Создаем тестовую структуру
        test_dir = temp_dir / "utils_test"
        FileUtils.ensure_directory(test_dir)
        
        # Создаем файлы разных типов
        image_file = test_dir / "test.jpg"
        image_file.write_bytes(b'\xff\xd8\xff\xe0\x00\x10JFIF')
        
        text_file = test_dir / "test.txt"
        text_file.write_text("test content")
        
        # Тестируем интеграцию
        assert FileUtils.is_image_file(image_file) is True
        assert FileUtils.is_image_file(text_file) is False
        
        uuid = get_system_uuid()
        system_info = get_system_info()
        
        assert uuid is not None
        assert system_info['uuid'] == uuid
        assert 'platform' in system_info
    
    def test_full_pipeline_mock(self, temp_dir):
        """Тест полного пайплайна с моками."""
        from magic_uniq.config.settings import Settings
        from magic_uniq.utils.file_utils import FileUtils
        from magic_uniq.utils.system_utils import get_system_uuid
        
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            # 1. Инициализируем настройки
            settings = Settings()
            
            # 2. Создаем необходимые директории
            input_dir = settings.get_directory("input")
            output_dir = settings.get_directory("output")
            
            FileUtils.ensure_directory(input_dir)
            FileUtils.ensure_directory(output_dir)
            
            # 3. Получаем системную информацию
            uuid = get_system_uuid()
            
            # 4. Создаем тестовый файл
            test_file = input_dir / "test_video.mp4"
            test_file.write_bytes(b'\x00\x00\x00\x20ftypmp41')
            
            # 5. Проверяем что все работает
            assert input_dir.exists()
            assert output_dir.exists()
            assert test_file.exists()
            assert FileUtils.is_video_file(test_file) is True
            assert uuid is not None
            
            # 6. Сохраняем результат
            result_file = output_dir / f"processed_{uuid[:8]}.mp4"
            FileUtils.copy_file_safe(test_file, result_file)
            
            assert result_file.exists()
    
    def test_error_propagation_between_modules(self, temp_dir):
        """Тест распространения ошибок между модулями."""
        from magic_uniq.config.settings import Settings
        from magic_uniq.config.validation import ConfigValidator
        
        # Тестируем с невалидной конфигурацией
        invalid_config = {
            "image_processing": {
                "quality": 150  # Невалидное значение
            }
        }
        
        validator = ConfigValidator()
        is_valid = validator.validate_config(invalid_config)
        
        assert is_valid is False
        assert len(validator.get_errors()) > 0
        
        # Settings должен работать с настройками по умолчанию
        with patch('pathlib.Path.cwd', return_value=temp_dir):
            settings = Settings()
            
            # Должно использоваться значение по умолчанию
            assert settings.get("image_processing.quality") == 95
