"""
Общие fixtures для тестов MagicUniq.
"""

import pytest
import tempfile
import shutil
import json
from pathlib import Path
from unittest.mock import Mock, patch
import sys
import os

# Добавляем src в путь
src_path = Path(__file__).parent.parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))


@pytest.fixture
def temp_dir():
    """Создает временную директорию для тестов."""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def test_data_dir():
    """Создает директорию с тестовыми данными."""
    temp_path = Path(tempfile.mkdtemp())
    
    # Создаем структуру директорий
    (temp_path / "input").mkdir()
    (temp_path / "output").mkdir()
    (temp_path / "backgrounds").mkdir()
    (temp_path / "overlays").mkdir()
    (temp_path / "emojis").mkdir()
    (temp_path / "temp").mkdir()
    (temp_path / "configs").mkdir()
    
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def sample_config():
    """Возвращает пример конфигурации."""
    return {
        "directories": {
            "input": "input_images",
            "output": "output_images",
            "backgrounds": "backgrounds",
            "overlays": "overlay_images",
            "emojis": "emojis",
            "temp": "temp"
        },
        "image_processing": {
            "max_size": 4096,
            "quality": 95,
            "format": "JPEG"
        },
        "video_processing": {
            "max_resolution": "1920x1080",
            "fps": 30,
            "codec": "h264"
        },
        "effects": {
            "enabled": True,
            "intensity": 0.5
        },
        "metadata": {
            "remove_original": True,
            "add_custom": True
        },
        "server": {
            "host": "localhost",
            "port": 5000,
            "debug": False
        }
    }


@pytest.fixture
def config_file(temp_dir, sample_config):
    """Создает временный файл конфигурации."""
    config_path = temp_dir / "config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, indent=2, ensure_ascii=False)
    return config_path


@pytest.fixture
def mock_image():
    """Создает mock объект PIL Image."""
    mock_img = Mock()
    mock_img.size = (1920, 1080)
    mock_img.width = 1920
    mock_img.height = 1080
    mock_img.mode = 'RGB'
    mock_img.format = 'JPEG'
    mock_img.save = Mock()
    mock_img.resize = Mock(return_value=mock_img)
    mock_img.convert = Mock(return_value=mock_img)
    mock_img.copy = Mock(return_value=mock_img)
    mock_img.thumbnail = Mock()
    return mock_img


@pytest.fixture
def sample_image_file(temp_dir):
    """Создает тестовый файл изображения."""
    # Создаем простой файл, имитирующий изображение
    image_path = temp_dir / "test_image.jpg"
    image_path.write_bytes(b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb')
    return image_path


@pytest.fixture
def sample_video_file(temp_dir):
    """Создает тестовый файл видео."""
    video_path = temp_dir / "test_video.mp4"
    # Создаем простой файл, имитирующий видео
    video_path.write_bytes(b'\x00\x00\x00\x20ftypmp41\x00\x00\x00\x00mp41isom')
    return video_path


@pytest.fixture
def mock_settings():
    """Создает mock объект Settings."""
    mock_settings = Mock()
    mock_settings.get = Mock(return_value=None)
    mock_settings.set = Mock()
    mock_settings.get_directory = Mock(return_value=Path("/tmp"))
    mock_settings.get_backgrounds_dir = Mock(return_value=Path("/tmp/backgrounds"))
    mock_settings.get_overlays_dir = Mock(return_value=Path("/tmp/overlays"))
    return mock_settings


@pytest.fixture
def mock_requests():
    """Создает mock для requests."""
    with patch('requests.get') as mock_get, \
         patch('requests.post') as mock_post:
        
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}
        mock_response.text = "success"
        
        mock_get.return_value = mock_response
        mock_post.return_value = mock_response
        
        yield {"get": mock_get, "post": mock_post, "response": mock_response}


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Настраивает тестовое окружение."""
    # Устанавливаем переменные окружения для тестов
    monkeypatch.setenv("TESTING", "1")
    monkeypatch.setenv("PYTHONPATH", str(src_path))


@pytest.fixture
def mock_file_system(temp_dir):
    """Создает mock файловой системы с тестовыми файлами."""
    # Создаем тестовые файлы
    test_files = {
        "image.jpg": b'\xff\xd8\xff\xe0\x00\x10JFIF',
        "image.png": b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR',
        "video.mp4": b'\x00\x00\x00\x20ftypmp41',
        "text.txt": b'test content',
        "config.json": json.dumps({"test": "value"}).encode()
    }
    
    for filename, content in test_files.items():
        (temp_dir / filename).write_bytes(content)
    
    return temp_dir


@pytest.fixture
def mock_system_uuid():
    """Создает mock для system UUID."""
    with patch('magic_uniq.utils.system_utils.get_system_uuid') as mock_uuid:
        mock_uuid.return_value = "test-uuid-12345"
        yield mock_uuid


class TestHelper:
    """Вспомогательный класс для тестов."""
    
    @staticmethod
    def create_test_image_data():
        """Создает тестовые данные изображения."""
        return {
            'width': 1920,
            'height': 1080,
            'mode': 'RGB',
            'format': 'JPEG',
            'size': (1920, 1080),
            'has_transparency': False
        }
    
    @staticmethod
    def create_test_video_params():
        """Создает тестовые параметры видео."""
        return {
            'width': 1920,
            'height': 1080,
            'fps': 30,
            'duration': 10.0,
            'codec': 'h264'
        }


@pytest.fixture
def test_helper():
    """Предоставляет доступ к вспомогательным функциям."""
    return TestHelper
