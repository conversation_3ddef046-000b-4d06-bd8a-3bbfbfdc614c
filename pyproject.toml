[tool.poetry]
name = "uniqualizer"
version = "0.1.0"
description = "Инструмент для уникализации изображений и видео с различными эффектами"
authors = ["igornet0"]
readme = "README.md"
packages = [{include = "src/magic_uniq"}]

[tool.poetry.dependencies]
python = "^3.10"
# Обработка изображений
Pillow = "^10.0.0"
opencv-python = "^4.8.0"
numpy = "^1.24.0"
piexif = "^1.1.3"

# GUI
PyQt6 = "^6.5.0"

# Веб-сервер
aiohttp = "^3.8.0"
aiofiles = "^23.0.0"
Flask = "^2.3.0"

# Аудио обработка
librosa = "^0.10.0"
soundfile = "^0.12.0"

# Научные вычисления
scipy = "^1.11.0"

# HTTP запросы
requests = "^2.31.0"

# Утилиты
tqdm = "^4.65.0"
psutil = "^5.9.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
black = "^23.0.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
uniqualizer = "uniqualizer:main"
uniqualizer-gui = "uniqualizer_gui:main"
generator = "generator:main"
image-server = "image_server:main"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
