#!/usr/bin/env python3
"""
Базовый тест для проверки работоспособности MagicUniq.

Этот тест проверяет основные компоненты без внешних зависимостей.
"""

import sys
import os
from pathlib import Path

# Добавляем src в путь для импорта
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Тест импортов основных модулей."""
    print("🔍 Тестирование импортов...")
    
    try:
        import magic_uniq
        print(f"✅ magic_uniq импортирован, версия: {magic_uniq.__version__}")
    except ImportError as e:
        print(f"❌ Ошибка импорта magic_uniq: {e}")
        return False
    
    try:
        from magic_uniq.config.settings import Settings
        print("✅ Settings импортирован")
    except ImportError as e:
        print(f"❌ Ошибка импорта Settings: {e}")
        return False
    
    try:
        from magic_uniq.utils.file_utils import FileUtils
        print("✅ FileUtils импортирован")
    except ImportError as e:
        print(f"❌ Ошибка импорта FileUtils: {e}")
        return False
    
    try:
        from magic_uniq.utils.system_utils import get_system_uuid
        print("✅ get_system_uuid импортирован")
    except ImportError as e:
        print(f"❌ Ошибка импорта get_system_uuid: {e}")
        return False
    
    return True

def test_settings():
    """Тест системы настроек."""
    print("\n⚙️  Тестирование системы настроек...")
    
    try:
        from magic_uniq.config.settings import Settings
        
        settings = Settings()
        print("✅ Settings создан")
        
        # Тест получения директорий
        input_dir = settings.get_input_dir()
        output_dir = settings.get_output_dir()
        print(f"✅ Входная директория: {input_dir}")
        print(f"✅ Выходная директория: {output_dir}")
        
        # Тест настроек
        host = settings.get("server.host", "localhost")
        port = settings.get("server.port", 5000)
        print(f"✅ Настройки сервера: {host}:{port}")
        
        return True
    except Exception as e:
        print(f"❌ Ошибка тестирования настроек: {e}")
        return False

def test_system_utils():
    """Тест системных утилит."""
    print("\n🖥️  Тестирование системных утилит...")
    
    try:
        from magic_uniq.utils.system_utils import get_system_uuid, get_system_info
        
        uuid = get_system_uuid()
        print(f"✅ UUID системы: {uuid[:8]}...")
        
        info = get_system_info()
        print(f"✅ Платформа: {info['platform']}")
        print(f"✅ Архитектура: {info['architecture']}")
        
        return True
    except Exception as e:
        print(f"❌ Ошибка тестирования системных утилит: {e}")
        return False

def test_file_utils():
    """Тест файловых утилит."""
    print("\n📁 Тестирование файловых утилит...")
    
    try:
        from magic_uniq.utils.file_utils import FileUtils
        
        # Создаем тестовый файл
        test_file = Path("test_file.txt")
        test_file.write_text("test content")
        
        # Тестируем утилиты
        size = FileUtils.get_file_size(test_file)
        print(f"✅ Размер файла: {size} байт")
        
        is_image = FileUtils.is_image_file(test_file)
        print(f"✅ Это изображение: {is_image}")
        
        # Удаляем тестовый файл
        test_file.unlink()
        
        return True
    except Exception as e:
        print(f"❌ Ошибка тестирования файловых утилит: {e}")
        return False

def main():
    """Главная функция тестирования."""
    print("🚀 Запуск базовых тестов MagicUniq\n")
    
    tests = [
        test_imports,
        test_settings,
        test_system_utils,
        test_file_utils,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("❌ Тест не пройден")
        except Exception as e:
            print(f"❌ Исключение в тесте: {e}")
    
    print(f"\n📊 Результаты: {passed}/{total} тестов пройдено")
    
    if passed == total:
        print("🎉 Все тесты пройдены успешно!")
        return 0
    else:
        print("⚠️  Некоторые тесты не пройдены")
        return 1

if __name__ == "__main__":
    sys.exit(main())
