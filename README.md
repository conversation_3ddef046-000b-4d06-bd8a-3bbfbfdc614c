# VideoCreater - Продвинутый инструмент уникализации видео и изображений

VideoCreater - это комплексная платформа для обработки и уникализации медиа-контента, предназначенная для создателей контента в социальных сетях. Она преобразует видео и изображения с помощью продвинутых эффектов, манипуляций с метаданными и систем наложения для создания уникальных вариаций при сохранении визуального качества.

## 🆕 Новая архитектура (v0.2.0)

Проект был полностью реорганизован для улучшения модульности и поддерживаемости:

- **🏗️ Модульная архитектура** с четким разделением ответственности
- **🚀 Единая точка входа** через CLI интерфейс (`main.py`)
- **⚙️ Централизованная конфигурация** с валидацией настроек
- **🧪 Комплексное тестирование** с заглушками для зависимостей
- **📚 Подробная документация** и примеры использования

### Быстрый старт
```bash
# Проверить систему
python test_full_system.py

# Показать справку
python main.py --help

# Запустить GUI
python main.py gui

# Обработать файл
python main.py process image.jpg
```

📖 **[Подробное руководство по новой структуре](docs/NEW_STRUCTURE.md)**
🚀 **[Быстрый старт](QUICK_START.md)**
📋 **[Отчет по оптимизации](OPTIMIZATION_REPORT.md)**

## 🚀 Возможности

### Основная обработка видео
- **Многослойный конвейер эффектов**: Масштабирование, насыщенность, прозрачность, гамма-коррекция, размытие, яркость, контрастность
- **Продвинутые паттерны движения**: Круговые, восьмерка, спиральные, зигзагообразные, диагональные и комбинированные движения
- **Система эффекта пазла**: Динамические паттерны пазла с настраиваемой видимостью и циклами анимации
- **Волновые искажения**: Реалистичные волновые искажения видео с настраиваемыми параметрами
- **Интеграция фона**: Динамическое масштабирование фона и координация движения

### Визуальные улучшения
- **Система PNG наложений**: Многослойные PNG наложения с контролем прозрачности и масштабирования
- **Интеграция эмодзи**: Анимированные наложения эмодзи с поворотом и движением
- **Генерация шума**: Множественные типы шума (розовый, белый, коричневый, синий, фиолетовый, серый, бархатный)
- **Мозаичные эффекты**: Микро-мозаичные паттерны с настраиваемой плотностью
- **Эффекты виньетки**: Настраиваемая интенсивность и радиус виньетки

### Метаданные и уникализация
- **Генерация метаданных устройства**: Реалистичная симуляция метаданных камеры и устройства
- **Подмена GPS локации**: Генерация случайных данных местоположения
- **Манипуляция временными метками**: Генерация пользовательских дат и времени создания
- **Оптимизация кодеков**: Множественные уровни сжатия и опции кодирования
- **Удаление кадров**: Стратегическое удаление кадров для уникализации

### Пользовательские интерфейсы
- **Десктопный GUI**: Полнофункциональное десктопное приложение с предварительным просмотром в реальном времени
- **Веб-интерфейс**: Панель управления на основе браузера с адаптивным дизайном
- **Пакетная обработка**: Обработка нескольких видео с отслеживанием прогресса
- **Управление конфигурацией**: Сохранение и загрузка пресетов обработки

## 📁 Структура проекта

```
MagicUniq/
├── MagicVideoSrc/
│   ├── video_uniquify.py      # Основной движок обработки видео
│   ├── web_gui.py             # Веб-интерфейс приложения
│   └── effects/               # Реализации видео эффектов
├── uniqualizer.py             # Основной движок уникализации
├── uniqualizer_gui.py         # Десктопное GUI приложение
├── image_server.py            # Сервер генерации изображений
├── generator.py               # Утилиты генерации контента
├── landing/
│   └── index.html            # Лендинг страница
├── settings.ini              # Основной файл конфигурации
├── input/                    # Директория входных видео
├── output/                   # Выходные обработанные видео
├── backgrounds/              # Фоновые изображения
├── overlay_images/           # PNG файлы наложений
├── emojis/                   # Ресурсы эмодзи
└── Конфигурации/            # Пресеты конфигурации
```

### Основные компоненты

#### Движок обработки видео (`MagicVideoSrc/video_uniquify.py`)
- `VideoUniquifier`: Основной класс обработки с многопоточным конвейером
- `VideoEffectParams`: Комплексное управление параметрами эффектов
- `EffectProcessor`: Система последовательного применения эффектов
- `MotionCoordinator`: Координация движения и масштабирования фона

#### GUI приложения
- **Десктопный GUI** (`uniqualizer_gui.py`): Нативный десктопный интерфейс
- **Веб GUI** (`MagicVideoSrc/web_gui.py`): Интерфейс на основе браузера с Flask бэкендом

#### Генерация контента
- **Сервер изображений** (`image_server.py`): HTTP API для генерации наложений изображений
- **Генератор** (`generator.py`): Утилиты процедурной генерации контента

## 🛠️ Установка и настройка

### Системные требования
- **ОС**: macOS, Windows 10/11, Linux
- **Python**: 3.10 или выше
- **Poetry**: Для управления зависимостями
- **ОЗУ**: 8ГБ минимум, 16ГБ рекомендуется
- **Хранилище**: 2ГБ свободного места для кэша обработки

### Быстрая установка с Poetry

1. **Установите Poetry** (если не установлен):
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

2. **Клонируйте репозиторий и установите зависимости**:
```bash
git clone <repository-url>
cd bot
poetry install
```

3. **Активируйте виртуальное окружение**:
```bash
poetry shell
```

### Ручная установка зависимостей
Если вы предпочитаете использовать pip:
```bash
pip install -r requirements.txt
```

Основные зависимости:
- `Pillow>=10.0.0` - обработка изображений
- `opencv-python>=4.8.0` - компьютерное зрение
- `PyQt6>=6.5.0` - графический интерфейс
- `aiohttp>=3.8.0` - асинхронный веб-сервер
- `librosa>=0.10.0` - обработка аудио
- `numpy>=1.24.0` - научные вычисления
- `scipy>=1.11.0` - дополнительные научные функции

### Установка FFmpeg
Приложение включает автоматическую установку FFmpeg. Ручная установка:
- **macOS**: `brew install ffmpeg`
- **Windows**: Скачайте с https://ffmpeg.org/
- **Linux**: `sudo apt install ffmpeg` или `sudo yum install ffmpeg`
- Проверьте командой `ffmpeg -version`

## 🎯 Инструкции по использованию

### Запуск с Poetry

#### Десктопное приложение
```bash
poetry run python uniqualizer_gui.py
```
1. Запустите десктопный GUI
2. Выберите входные видео файлы
3. Настройте параметры обработки
4. Запустите пакетную обработку
5. Отслеживайте прогресс в реальном времени

#### Веб-интерфейс
```bash
poetry run python MagicVideoSrc/web_gui.py
```
1. Откройте браузер по адресу `http://localhost:5000`
2. Загрузите видео через веб-интерфейс
3. Настройте параметры с помощью веб-элементов управления
4. Скачайте обработанные видео

#### Сервер изображений
```bash
poetry run python image_server.py
```

#### Генератор контента
```bash
poetry run python generator.py
```

### Альтернативный запуск (без Poetry)
Если вы используете обычный Python:
```bash
python uniqualizer_gui.py
python MagicVideoSrc/web_gui.py
```

### Обработка через командную строку
```python
from MagicVideoSrc.video_uniquify import VideoUniquifier, VideoEffectParams

# Инициализация процессора
processor = VideoUniquifier()

# Настройка эффектов
params = VideoEffectParams(
    scale=0.9,
    saturation=1.2,
    puzzle_enabled=True,
    png_overlay_enabled=True
)

# Обработка видео
processor.process_video("input.mp4", params)
```

## 🔧 Конфигурация

### Основные настройки (`settings.ini`)
```ini
[Scale]
enabled = true
min = 0.8
max = 1.0

[Puzzle]
enabled = true
rows = 4
pattern = random
visible_percent = 25

[PNG-Overlay]
enabled = true
opacity_min = 50
opacity_max = 90
```

### Параметры эффектов
- **Движение**: Амплитуда (0.15-0.25), Скорость (0.3-0.6), Сложность (0.5-1.5)
- **Визуальные**: Размытие (0.3-1.5), Яркость (1.1-1.3), Контрастность (1.1-1.3)
- **Шум**: Интенсивность (0.02-0.08), Доступны множественные типы шума
- **Наложения**: Прозрачность PNG (0-100%), Масштаб (50-200%), Поддержка поворота

## 🌐 Документация API

### Эндпоинты сервера изображений

#### GET `/get_image`
Возвращает случайно сгенерированное изображение наложения
- **Ответ**: Данные JPEG изображения
- **Заголовки**: `Content-Type: image/jpeg`

#### GET `/get_overlay?token={uuid}`
Возвращает изображение наложения для конкретного устройства
- **Параметры**:
  - `token`: UUID устройства для уникальности
- **Ответ**: Данные JPEG изображения
- **Аутентификация**: Система токенов на основе аппаратного обеспечения

### API обработки
```python
# Регистрация задачи обработки
process_id = manager.register_process(
    video_path="input.mp4",
    total_iterations=5,
    pid=os.getpid()
)

# Обновление прогресса
manager.update_progress(process_id, iteration=1, progress=25)
```

## 📊 Конвейер обработки

1. **Валидация входных данных**: Проверка формата файла и кодека
2. **Инициализация эффектов**: Генерация и валидация параметров
3. **Обработка кадров**: Многопоточная покадровая обработка
4. **Обработка аудио**: Параллельное применение аудио эффектов
5. **Кодирование**: Финальная компиляция видео с метаданными
6. **Генерация выходных данных**: Оптимизация файла и внедрение метаданных

## 🔒 Функции безопасности

- **Снятие отпечатков аппаратного обеспечения**: Идентификация устройства на основе UUID
- **Токен-аутентификация**: Безопасные запросы изображений наложения
- **Санитизация метаданных**: Удаление и замена оригинальных метаданных
- **Изоляция процессов**: Изолированная среда обработки видео

## 🚦 Оптимизация производительности

- **Многопоточность**: Параллельный конвейер обработки кадров
- **GPU ускорение**: Аппаратно-ускоренное кодирование при наличии
- **Управление памятью**: Эффективная буферизация кадров и очистка
- **Пакетная обработка**: Оптимизированные рабочие процессы для нескольких видео

## 📖 Дополнительная документация

- **Подробное описание работы**: [`HOW_IT_WORKS.md`](HOW_IT_WORKS.md) - детальное объяснение всех алгоритмов и эффектов
- **Руководство разработчика**: [`DEVELOPMENT.md`](DEVELOPMENT.md) - информация для разработчиков
- **Быстрый старт**: [`QUICKSTART.md`](QUICKSTART.md) - краткая инструкция по установке и запуску
- **Исправления**: [`BUGFIX_UUID.md`](BUGFIX_UUID.md) - информация об исправлении кроссплатформенных ошибок
- **Проверка установки**: `poetry run python check_installation.py`

## 📝 Лицензия

Проприетарное программное обеспечение. Все права защищены.

## 🤝 Поддержка

Для технической поддержки и запросов функций обращайтесь к команде разработки.

## 🔄 Миграция на новую структуру

### Новый способ запуска (рекомендуется)

Используйте единую точку входа `main.py`:

```bash
# Показать справку
python main.py --help

# Запустить графический интерфейс
python main.py gui

# Запустить веб-интерфейс
python main.py web

# Запустить сервер изображений
python main.py server

# Запустить генератор контента
python main.py generator

# Обработать файл
python main.py process image.jpg
python main.py process video.mp4

# Управление конфигурацией
python main.py config show
python main.py config validate
python main.py config reset

# Показать версию
python main.py version
```

### Тестирование системы

```bash
# Базовый тест без зависимостей
python test_basic.py

# Полный тест системы (требует зависимости)
python test_full_system.py
```

### Новые импорты

Старые импорты:
```python
from uniqualizer import UniqualizerConfig
from MagicVideoSrc.video_uniquify import VideoUniquifier
```

Новые импорты:
```python
from magic_uniq.core.image_processor import UniqualizerConfig
from magic_uniq.core.video_processor import VideoUniquifier
from magic_uniq.config.settings import Settings
from magic_uniq.utils.system_utils import get_system_uuid
```

---

**VideoCreater** - Преобразуйте ваш контент, расширьте охват.