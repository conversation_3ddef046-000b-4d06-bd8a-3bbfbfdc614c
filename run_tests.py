#!/usr/bin/env python3
"""
Скрипт для запуска тестов MagicUniq.

Поддерживает различные режимы запуска тестов:
- Все тесты
- Только unit тесты
- Только интеграционные тесты
- Тесты с покрытием кода
- Тесты конкретного модуля
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description=""):
    """Запустить команду и вернуть результат."""
    print(f"\n{'='*60}")
    if description:
        print(f"🚀 {description}")
    print(f"Команда: {' '.join(cmd)}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ {description or 'Команда'} выполнена успешно")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description or 'Команда'} завершилась с ошибкой (код {e.returncode})")
        return False
    except FileNotFoundError:
        print(f"❌ Команда не найдена: {cmd[0]}")
        print("Убедитесь что pytest установлен: pip install pytest")
        return False


def check_dependencies():
    """Проверить наличие необходимых зависимостей."""
    print("🔍 Проверка зависимостей...")
    
    dependencies = ['pytest', 'pytest-cov', 'pytest-asyncio']
    missing = []
    
    for dep in dependencies:
        try:
            __import__(dep.replace('-', '_'))
        except ImportError:
            missing.append(dep)
    
    if missing:
        print(f"❌ Отсутствуют зависимости: {', '.join(missing)}")
        print("Установите их командой:")
        print(f"pip install {' '.join(missing)}")
        return False
    
    print("✅ Все зависимости установлены")
    return True


def run_unit_tests(verbose=False, coverage=False):
    """Запустить unit тесты."""
    cmd = ['python', '-m', 'pytest', 'tests/unit/']
    
    if verbose:
        cmd.append('-v')
    
    if coverage:
        cmd.extend(['--cov=src/magic_uniq', '--cov-report=html', '--cov-report=term'])
    
    cmd.extend(['-m', 'unit'])
    
    return run_command(cmd, "Запуск unit тестов")


def run_integration_tests(verbose=False):
    """Запустить интеграционные тесты."""
    cmd = ['python', '-m', 'pytest', 'tests/integration/', '-m', 'integration']
    
    if verbose:
        cmd.append('-v')
    
    return run_command(cmd, "Запуск интеграционных тестов")


def run_all_tests(verbose=False, coverage=False):
    """Запустить все тесты."""
    cmd = ['python', '-m', 'pytest', 'tests/']
    
    if verbose:
        cmd.append('-v')
    
    if coverage:
        cmd.extend(['--cov=src/magic_uniq', '--cov-report=html', '--cov-report=term'])
    
    return run_command(cmd, "Запуск всех тестов")


def run_specific_module_tests(module_name, verbose=False):
    """Запустить тесты для конкретного модуля."""
    test_patterns = [
        f'tests/unit/test_{module_name}*.py',
        f'tests/unit/test_*_{module_name}.py',
        f'tests/unit/test_*{module_name}*.py'
    ]
    
    cmd = ['python', '-m', 'pytest'] + test_patterns
    
    if verbose:
        cmd.append('-v')
    
    return run_command(cmd, f"Запуск тестов для модуля {module_name}")


def run_tests_with_markers(markers, verbose=False):
    """Запустить тесты с определенными маркерами."""
    cmd = ['python', '-m', 'pytest', 'tests/', '-m', markers]
    
    if verbose:
        cmd.append('-v')
    
    return run_command(cmd, f"Запуск тестов с маркерами: {markers}")


def generate_coverage_report():
    """Сгенерировать отчет о покрытии кода."""
    cmd = ['python', '-m', 'pytest', 'tests/', '--cov=src/magic_uniq', 
           '--cov-report=html', '--cov-report=term', '--cov-report=xml']
    
    success = run_command(cmd, "Генерация отчета о покрытии кода")
    
    if success:
        print("\n📊 Отчет о покрытии сгенерирован:")
        print("  - HTML: htmlcov/index.html")
        print("  - XML: coverage.xml")
        print("  - Терминал: см. выше")
    
    return success


def run_linting():
    """Запустить проверку кода."""
    print("🔍 Проверка стиля кода...")
    
    # Проверяем наличие flake8
    try:
        cmd = ['python', '-m', 'flake8', 'src/', 'tests/', '--max-line-length=88', '--extend-ignore=E203,W503']
        return run_command(cmd, "Проверка стиля кода (flake8)")
    except FileNotFoundError:
        print("⚠️  flake8 не установлен, пропускаем проверку стиля")
        return True


def main():
    """Главная функция."""
    parser = argparse.ArgumentParser(description="Запуск тестов MagicUniq")
    
    parser.add_argument('--unit', action='store_true', help='Запустить только unit тесты')
    parser.add_argument('--integration', action='store_true', help='Запустить только интеграционные тесты')
    parser.add_argument('--coverage', action='store_true', help='Запустить с покрытием кода')
    parser.add_argument('--verbose', '-v', action='store_true', help='Подробный вывод')
    parser.add_argument('--module', help='Запустить тесты для конкретного модуля')
    parser.add_argument('--markers', help='Запустить тесты с определенными маркерами')
    parser.add_argument('--lint', action='store_true', help='Проверить стиль кода')
    parser.add_argument('--check-deps', action='store_true', help='Проверить зависимости')
    
    args = parser.parse_args()
    
    print("🧪 MagicUniq Test Runner")
    print("=" * 60)
    
    # Проверяем зависимости если запрошено
    if args.check_deps:
        return 0 if check_dependencies() else 1
    
    # Проверяем что мы в правильной директории
    if not Path('src/magic_uniq').exists():
        print("❌ Запустите скрипт из корневой директории проекта")
        return 1
    
    success = True
    
    # Проверка стиля кода
    if args.lint:
        success &= run_linting()
    
    # Запуск тестов
    if args.unit:
        success &= run_unit_tests(args.verbose, args.coverage)
    elif args.integration:
        success &= run_integration_tests(args.verbose)
    elif args.module:
        success &= run_specific_module_tests(args.module, args.verbose)
    elif args.markers:
        success &= run_tests_with_markers(args.markers, args.verbose)
    elif args.coverage:
        success &= generate_coverage_report()
    else:
        # Запускаем все тесты по умолчанию
        success &= run_all_tests(args.verbose, args.coverage)
    
    # Итоговый результат
    print("\n" + "=" * 60)
    if success:
        print("🎉 Все тесты прошли успешно!")
        print("\n📋 Доступные команды:")
        print("  python run_tests.py --unit          # Unit тесты")
        print("  python run_tests.py --integration   # Интеграционные тесты")
        print("  python run_tests.py --coverage      # Тесты с покрытием")
        print("  python run_tests.py --module config # Тесты модуля config")
        print("  python run_tests.py --lint          # Проверка стиля")
        print("  python run_tests.py --verbose       # Подробный вывод")
        return 0
    else:
        print("💥 Некоторые тесты завершились с ошибками")
        return 1


if __name__ == '__main__':
    sys.exit(main())
