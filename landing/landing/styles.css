
:root {
    --primary: #4169E1;
    --primary-light: #6495ED;
    --dark: #111827;
    --darker: #0A0F1C;
    --light: #F9FAFB;
    --gray: #374151;
    --light-gray: #6B7280;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

body {
    background-color: var(--darker);
    color: var(--light);
    line-height: 1.6;
}

a {
    color: var(--primary-light);
    text-decoration: none;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    padding: 20px 0;
    position: sticky;
    top: 0;
    background-color: rgba(10, 15, 28, 0.9);
    backdrop-filter: blur(10px);
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: 700;
    color: var(--light);
}

.logo-icon {
    color: var(--primary);
    font-size: 28px;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
}

nav a {
    color: var(--light);
    font-weight: 500;
    transition: color 0.3s;
}

nav a:hover {
    color: var(--primary);
}

.btn {
    display: inline-block;
    background-color: var(--primary);
    color: white;
    padding: 10px 24px;
    border-radius: 8px;
    font-weight: 500;
    transition: background-color 0.3s;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background-color: var(--primary-light);
}

.btn-secondary {
    background-color: transparent;
    border: 1px solid var(--primary);
    color: var(--primary);
}

.btn-secondary:hover {
    background-color: rgba(65, 105, 225, 0.1);
}

/* Hero Section */
.hero {
    padding: 100px 0 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 40px;
}

.hero-content {
    max-width: 600px;
}

h1 {
    font-size: 52px;
    line-height: 1.2;
    margin-bottom: 20px;
    font-weight: 800;
}

h1 span {
    background: linear-gradient(90deg, var(--primary), #8A2BE2);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.hero p {
    font-size: 18px;
    color: var(--light-gray);
    margin-bottom: 30px;
}

.hero-btns {
    display: flex;
    gap: 16px;
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.app-screenshot {
    width: 100%;
    max-width: 600px;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s;
}

.app-screenshot:hover {
    transform: translateY(-10px);
}

/* Features Section */
.features {
    padding: 100px 0;
    background-color: var(--dark);
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

h2 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 16px;
}

.section-header p {
    font-size: 18px;
    color: var(--light-gray);
    max-width: 700px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.feature-card {
    background-color: var(--darker);
    border-radius: 12px;
    padding: 30px;
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: rgba(65, 105, 225, 0.1);
    color: var(--primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 22px;
    margin-bottom: 12px;
}

.feature-card p {
    color: var(--light-gray);
}

/* How It Works */
.how-it-works {
    padding: 100px 0;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.step {
    text-align: center;
    padding: 20px;
}

.step-number {
    width: 50px;
    height: 50px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 700;
    margin: 0 auto 20px;
}

.step h3 {
    margin: 15px 0;
}

.step p {
    color: var(--light-gray);
}

/* Pricing */
.pricing {
    padding: 100px 0;
    background-color: var(--dark);
}

.pricing-cards {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 60px;
}

.pricing-card {
    background-color: var(--darker);
    border-radius: 12px;
    padding: 40px;
    width: 100%;
    max-width: 350px;
    transition: transform 0.3s;
}

.pricing-card:hover {
    transform: translateY(-10px);
}

.pricing-card.featured {
    border: 2px solid var(--primary);
    position: relative;
}

.featured-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.pricing-card h3 {
    font-size: 24px;
    margin-bottom: 20px;
}

.price {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
}

.price span {
    font-size: 16px;
    font-weight: 400;
    color: var(--light-gray);
}

.pricing-features {
    margin: 30px 0;
    list-style: none;
}

.pricing-features li {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.pricing-features li::before {
    content: "✓";
    color: var(--primary);
    margin-right: 10px;
    font-weight: bold;
}

/* Testimonials */
.testimonials {
    padding: 100px 0;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background-color: var(--dark);
    border-radius: 12px;
    padding: 30px;
    transition: transform 0.3s;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.testimonial-content {
    margin-bottom: 20px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--gray);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: var(--light);
}

.author-info h4 {
    font-size: 18px;
}

.author-info p {
    font-size: 14px;
    color: var(--light-gray);
}

/* FAQ */
.faq {
    padding: 100px 0;
    background-color: var(--dark);
}

.faq-container {
    max-width: 800px;
    margin: 60px auto 0;
}

.faq-item {
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.faq-question {
    background-color: var(--darker);
    padding: 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
}

.faq-answer {
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease;
    color: var(--light-gray);
}

.faq-item.active .faq-answer {
    padding: 20px;
    max-height: 500px;
}

/* CTA */
.cta {
    padding: 100px 0;
    text-align: center;
}

.cta-content {
    max-width: 700px;
    margin: 0 auto;
}

.cta h2 {
    font-size: 42px;
    margin-bottom: 20px;
}

.cta p {
    font-size: 18px;
    color: var(--light-gray);
    margin-bottom: 30px;
}

/* Footer */
footer {
    background-color: var(--dark);
    padding: 70px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 50px;
}

.footer-logo {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-about p {
    color: var(--light-gray);
    margin-bottom: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.social-link:hover {
    background-color: var(--primary);
}

.footer-links h4 {
    font-size: 18px;
    margin-bottom: 20px;
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: var(--light-gray);
    transition: color 0.3s;
}

.footer-links a:hover {
    color: var(--primary);
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--light-gray);
    font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 50px 0;
    }

    .hero-btns {
        justify-content: center;
    }

    h1 {
        font-size: 36px;
    }

    .section-header {
        margin-bottom: 40px;
    }

    nav {
        display: none;
    }

    .features, .how-it-works, .pricing, .testimonials, .faq, .cta {
        padding: 70px 0;
    }

    .pricing-cards {
        flex-direction: column;
        align-items: center;
    }
}
