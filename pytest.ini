[tool:pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    network: Tests that require network access
    gui: Tests for GUI components
    cli: Tests for CLI components
    web: Tests for web components
    config: Tests for configuration modules
    core: Tests for core modules
    utils: Tests for utility modules
    services: Tests for service modules
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning
