"""
CLI интерфейс для MagicUniq.

Предоставляет командную строку для управления всеми
компонентами системы уникализации.
"""

import sys
import argparse
from pathlib import Path
from typing import Optional

from ...config.settings import Settings
from ...config.validation import ConfigValida<PERSON>


def main(args: Optional[list] = None):
    """
    Главная функция CLI интерфейса.
    
    Args:
        args: Аргументы командной строки (если None, используются sys.argv)
    """
    parser = create_cli_parser()
    
    if args is None:
        args = sys.argv[1:]
    
    if not args:
        parser.print_help()
        return
    
    parsed_args = parser.parse_args(args)
    
    # Инициализируем настройки
    settings = Settings(parsed_args.config if hasattr(parsed_args, 'config') else None)
    
    # Выполняем команду
    try:
        if parsed_args.command == 'process':
            process_file(parsed_args, settings)
        elif parsed_args.command == 'batch':
            process_batch(parsed_args, settings)
        elif parsed_args.command == 'config':
            manage_config(parsed_args, settings)
        elif parsed_args.command == 'info':
            show_info(parsed_args, settings)
        else:
            parser.print_help()
    except KeyboardInterrupt:
        print("\n⚠️  Операция прервана пользователем")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        sys.exit(1)


def create_cli_parser() -> argparse.ArgumentParser:
    """Создать парсер для CLI интерфейса."""
    parser = argparse.ArgumentParser(
        description="MagicUniq CLI - Интерфейс командной строки",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Доступные команды')
    
    # Process команда
    process_parser = subparsers.add_parser('process', help='Обработать файл')
    process_parser.add_argument('input', help='Входной файл')
    process_parser.add_argument('-o', '--output', help='Выходной файл')
    process_parser.add_argument('-p', '--preset', help='Пресет настроек')
    process_parser.add_argument('-c', '--config', help='Файл конфигурации')
    process_parser.add_argument('-v', '--verbose', action='store_true', help='Подробный вывод')
    
    # Batch команда
    batch_parser = subparsers.add_parser('batch', help='Пакетная обработка')
    batch_parser.add_argument('input_dir', help='Директория с входными файлами')
    batch_parser.add_argument('-o', '--output-dir', help='Директория для результатов')
    batch_parser.add_argument('-p', '--preset', help='Пресет настроек')
    batch_parser.add_argument('-c', '--config', help='Файл конфигурации')
    batch_parser.add_argument('-t', '--threads', type=int, default=1, help='Количество потоков')
    batch_parser.add_argument('-v', '--verbose', action='store_true', help='Подробный вывод')
    
    # Config команда
    config_parser = subparsers.add_parser('config', help='Управление конфигурацией')
    config_subparsers = config_parser.add_subparsers(dest='config_action')
    
    config_subparsers.add_parser('show', help='Показать конфигурацию')
    config_subparsers.add_parser('validate', help='Проверить конфигурацию')
    config_subparsers.add_parser('reset', help='Сбросить к умолчаниям')
    
    # Info команда
    info_parser = subparsers.add_parser('info', help='Информация о системе')
    info_parser.add_argument('target', nargs='?', help='Цель для информации (file, system)')
    
    return parser


def process_file(args, settings: Settings):
    """Обработать один файл."""
    input_path = Path(args.input)
    
    if not input_path.exists():
        raise FileNotFoundError(f"Файл не найден: {input_path}")
    
    # Определяем выходной файл
    if args.output:
        output_path = Path(args.output)
    else:
        output_dir = settings.get_directory('output')
        output_path = output_dir / f"processed_{input_path.name}"
    
    print(f"📁 Входной файл: {input_path}")
    print(f"📁 Выходной файл: {output_path}")
    
    # Определяем тип файла и обрабатываем
    from ...utils.file_utils import FileUtils
    
    if FileUtils.is_image_file(input_path):
        process_image_file(input_path, output_path, args, settings)
    elif FileUtils.is_video_file(input_path):
        process_video_file(input_path, output_path, args, settings)
    else:
        raise ValueError(f"Неподдерживаемый тип файла: {input_path}")


def process_image_file(input_path: Path, output_path: Path, args, settings: Settings):
    """Обработать файл изображения."""
    print("🖼️  Обработка изображения...")
    
    # TODO: Реализовать обработку изображения
    # from ...core.image_processor import ImageProcessor
    # processor = ImageProcessor(settings)
    # processor.process(input_path, output_path, preset=args.preset)
    
    print("✅ Изображение обработано успешно")


def process_video_file(input_path: Path, output_path: Path, args, settings: Settings):
    """Обработать видео файл."""
    print("🎥 Обработка видео...")
    
    # TODO: Реализовать обработку видео
    # from ...core.video_processor import VideoProcessor
    # processor = VideoProcessor(settings)
    # processor.process(input_path, output_path, preset=args.preset)
    
    print("✅ Видео обработано успешно")


def process_batch(args, settings: Settings):
    """Пакетная обработка файлов."""
    input_dir = Path(args.input_dir)
    
    if not input_dir.exists():
        raise FileNotFoundError(f"Директория не найдена: {input_dir}")
    
    # Определяем выходную директорию
    if args.output_dir:
        output_dir = Path(args.output_dir)
    else:
        output_dir = settings.get_directory('output')
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 Входная директория: {input_dir}")
    print(f"📁 Выходная директория: {output_dir}")
    print(f"🧵 Потоков: {args.threads}")
    
    # Находим все поддерживаемые файлы
    from ...utils.file_utils import FileUtils
    
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
    
    image_files = FileUtils.get_files_by_extension(input_dir, image_extensions)
    video_files = FileUtils.get_files_by_extension(input_dir, video_extensions)
    
    total_files = len(image_files) + len(video_files)
    
    if total_files == 0:
        print("⚠️  Не найдено файлов для обработки")
        return
    
    print(f"📊 Найдено файлов: {total_files} ({len(image_files)} изображений, {len(video_files)} видео)")
    
    # TODO: Реализовать пакетную обработку с многопоточностью
    print("✅ Пакетная обработка завершена")


def manage_config(args, settings: Settings):
    """Управление конфигурацией."""
    if args.config_action == 'show':
        import json
        print("📋 Текущая конфигурация:")
        print(json.dumps(settings.to_dict(), indent=2, ensure_ascii=False))
    
    elif args.config_action == 'validate':
        validator = ConfigValidator()
        if validator.validate_config(settings.to_dict()):
            print("✅ Конфигурация валидна")
        else:
            print("❌ Найдены ошибки в конфигурации:")
            for error in validator.get_errors():
                print(f"  • {error}")
            
            if validator.has_warnings():
                print("\n⚠️  Предупреждения:")
                for warning in validator.get_warnings():
                    print(f"  • {warning}")
    
    elif args.config_action == 'reset':
        settings.reset_to_defaults()
        settings.save_config()
        print("✅ Конфигурация сброшена к настройкам по умолчанию")


def show_info(args, settings: Settings):
    """Показать информацию о системе."""
    if args.target == 'file' and len(sys.argv) > 3:
        # Информация о файле
        file_path = Path(sys.argv[3])
        show_file_info(file_path)
    else:
        # Общая информация о системе
        show_system_info(settings)


def show_file_info(file_path: Path):
    """Показать информацию о файле."""
    if not file_path.exists():
        print(f"❌ Файл не найден: {file_path}")
        return
    
    from ...utils.file_utils import FileUtils
    
    print(f"📄 Информация о файле: {file_path}")
    print(f"📏 Размер: {FileUtils.get_file_size(file_path)} байт")
    print(f"🗂️  Тип: {'Изображение' if FileUtils.is_image_file(file_path) else 'Видео' if FileUtils.is_video_file(file_path) else 'Неизвестный'}")
    
    # Дополнительная информация для изображений
    if FileUtils.is_image_file(file_path):
        from ...utils.image_utils import ImageUtils
        image = ImageUtils.load_image(file_path)
        if image:
            info = ImageUtils.get_image_info(image)
            print(f"📐 Размеры: {info['width']}x{info['height']}")
            print(f"🎨 Режим: {info['mode']}")
            print(f"📋 Формат: {info['format']}")


def show_system_info(settings: Settings):
    """Показать информацию о системе."""
    try:
        from ... import __version__
        version = __version__
    except ImportError:
        version = "неизвестна"
    
    print(f"🚀 MagicUniq версия {version}")
    print(f"📁 Рабочая директория: {settings.base_dir}")
    print(f"⚙️  Файл конфигурации: {settings.config_path}")
    
    # Проверяем доступность компонентов
    components = {
        'PIL (Pillow)': 'PIL',
        'OpenCV': 'cv2',
        'NumPy': 'numpy',
        'PyQt6': 'PyQt6',
        'Flask': 'flask',
        'aiohttp': 'aiohttp'
    }
    
    print("\n📦 Доступные компоненты:")
    for name, module in components.items():
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError:
            print(f"  ❌ {name}")


if __name__ == '__main__':
    main()
