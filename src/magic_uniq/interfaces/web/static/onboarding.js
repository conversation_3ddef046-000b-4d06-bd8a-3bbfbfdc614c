class OnboardingManager {
    constructor() {
        this.currentStep = 0;
        this.isLoading = true;
        this.steps = [
            {
                title: 'Привет!',
                content: 'Спасибо, что используешь MagicUniq ❤️',
                subContent: 'Я могу показать, как тут всё устроено. Это займёт всего 30 секунд.',
                position: 'top',
                element: null,
                showMascot: true,
                darkenBackground: true
            },
            {
                title: 'Загрузка файлов',
                content: 'Тут будут ваши видео файлы. Перетащите их сюда или нажмите кнопку "Выберите файл".',
                position: 'right',
                element: '#dropzone',
                autoOpen: false
            },
            {
                title: 'Настройки',
                content: 'Нажмите эту кнопку, чтобы открыть настройки приложения и изменить параметры обработки.',
                position: 'left',
                element: '#settingsToggle',
                autoOpenAfterNext: true
            },
            {
                title: 'Конфигурации',
                content: 'Вы можете создавать и переключаться между разными конфигурациями настроек.',
                position: 'right',
                element: '.config-wrapper',
                requiresModal: 'settings'
            },
            {
                title: 'Запуск',
                content: 'После загрузки файлов нажмите эту кнопку, чтобы начать обработку.',
                position: 'left',  // Left position
                element: '#processingToggle',
                autoOpenAfterNext: true,
                keepOpen: true,  // Keep launch modal open
                closeModalBefore: true
            }
        ];

        this.overlay = null;
        this.settingsOpened = false;
        this.launchOpened = false;

        this.init();
    }

    async init() {
        try {
            // Check with the server if onboarding is complete
            const response = await fetch('/check_onboarding_complete');
            const data = await response.json();

            this.isLoading = false;

            if (!data.complete) {
                // Only start onboarding if server says it's not complete
                setTimeout(() => {
                    this.createOverlay();
                    this.startOnboarding();
                }, 1000);
            }
        } catch (error) {
            console.error('Error checking onboarding status:', error);
            this.isLoading = false;

            // If we can't reach the server, assume it's not complete
            setTimeout(() => {
                this.createOverlay();
                this.startOnboarding();
            }, 1000);
        }
    }

    createOverlay() {
        // Create a semi-transparent overlay
        this.overlay = document.createElement('div');
        this.overlay.className = 'onboarding-overlay';
        document.body.appendChild(this.overlay);
    }

    startOnboarding() {
        this.createModal();
        this.showStep(0);
    }

    createModal() {
        const modal = document.createElement('div');
        modal.id = 'onboardingModal';
        modal.className = 'fixed inset-0 z-50 flex items-start justify-center pt-10';

        // Create modal container
        const modalContent = document.createElement('div');
        modalContent.className = 'onboarding-content relative bg-gray-800 rounded-xl shadow-xl';
        modalContent.style.width = '600px'; // 600px width

        // Add close button
        const closeButton = document.createElement('button');
        closeButton.className = 'absolute top-3 right-3 text-gray-400 hover:text-gray-300';
        closeButton.innerHTML = '<i data-lucide="x" class="w-5 h-5"></i>';
        closeButton.addEventListener('click', () => this.completeOnboarding());

        // Create content container
        const contentContainer = document.createElement('div');
        contentContainer.id = 'onboardingContent';
        contentContainer.className = 'p-6';

        // Create navigation buttons
        const buttonsContainer = document.createElement('div');
        buttonsContainer.className = 'flex justify-between items-center p-4 border-t border-gray-700';

        const skipButton = document.createElement('button');
        skipButton.id = 'skipOnboarding';
        skipButton.className = 'text-gray-400 hover:text-gray-300 px-4 py-2 rounded-lg';
        skipButton.textContent = 'Нет, спасибо';
        skipButton.addEventListener('click', () => this.completeOnboarding());

        const nextButton = document.createElement('button');
        nextButton.id = 'nextStep';
        nextButton.className = 'bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors';
        nextButton.textContent = 'Посмотреть';
        nextButton.addEventListener('click', () => this.nextStep());

        // Assemble the modal
        buttonsContainer.appendChild(skipButton);
        buttonsContainer.appendChild(nextButton);

        modalContent.appendChild(closeButton);
        modalContent.appendChild(contentContainer);
        modalContent.appendChild(buttonsContainer);

        modal.appendChild(modalContent);

        document.body.appendChild(modal);

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons();
        }
    }

    showStep(stepIndex) {
        // Remove existing tooltips and highlights
        this.removeAllTooltips();

        this.currentStep = stepIndex;
        const step = this.steps[stepIndex];
        const contentContainer = document.getElementById('onboardingContent');
        const nextButton = document.getElementById('nextStep');
        const skipButton = document.getElementById('skipOnboarding');
        const modal = document.getElementById('onboardingModal');

        // Handle modal closing if needed
        if (step.closeModalBefore) {
            this.closeSettingsModal();
        }

        // Handle overlay visibility - only show for first step if requested
        this.toggleOverlay(step.darkenBackground === true);

        // Update button text based on step
        if (stepIndex === 0) {
            nextButton.textContent = 'Посмотреть';
            skipButton.textContent = 'Нет, спасибо';
        } else if (stepIndex === this.steps.length - 1) {
            nextButton.textContent = 'Завершить';
            skipButton.style.visibility = 'hidden';
        } else {
            nextButton.textContent = 'Дальше';
            skipButton.textContent = 'Пропустить';
            skipButton.style.visibility = 'visible';
        }

        // Create content for first step modal
        if (stepIndex === 0) {
            let html = `
                <div class="flex items-center gap-6">
                    <div class="mascot-container flex-shrink-0">
                        <video autoplay loop muted playsinline width="150" height="150" class="mascot-video">
                            <source src="http://magicuniq.space/staticV3/mascot.webm" type="video/webm">
                        </video>
                    </div>
                    <div>
                        <h3 class="text-2xl font-semibold mb-3">Привет!</h3>
                        <p class="text-gray-300">${step.content}</p>
                        ${step.subContent ? `<p class="text-gray-400 mt-2">${step.subContent}</p>` : ''}
                    </div>
                </div>
            `;

            contentContainer.innerHTML = html;
            modal.style.display = 'flex';
        } else {
            // Hide the main modal for other steps
            modal.style.display = 'none';

            // Skip the config step if settings aren't open
            if (step.requiresModal === 'settings' && !this.settingsOpened) {
                this.nextStep();
                return;
            }

            // Create tooltip for this step
            this.createTooltip(step);
        }
    }

    createTooltip(step) {
        const targetElement = document.querySelector(step.element);

        if (!targetElement) {
            console.warn(`Element ${step.element} not found for tooltip`);
            // Move to next step if element not found
            setTimeout(() => this.nextStep(), 500);
            return;
        }

        // Highlight the target element with special class
        targetElement.classList.add('onboarding-highlight');

        // Create tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'onboarding-tooltip';
        tooltip.id = 'currentTooltip';

        // Add content to tooltip
        tooltip.innerHTML = `
            <div class="flex items-center justify-between mb-2">
                <h3 class="font-semibold">${step.title}</h3>
                <button class="tooltip-close text-gray-400 hover:text-gray-300">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
            <p>${step.content}</p>
            <div class="flex justify-between mt-4">
                <button class="tooltip-skip text-gray-400 hover:text-gray-300">Пропустить</button>
                <button class="tooltip-next bg-blue-600 hover:bg-blue-700 text-white px-4 py-1 rounded">Дальше</button>
            </div>
        `;

        // Initialize Lucide icons in tooltip
        document.body.appendChild(tooltip);
        if (window.lucide) {
            lucide.createIcons();
        }

        // Position the tooltip
        this.positionTooltip(tooltip, targetElement, step.position);

        // Add event listeners
        const skipBtn = tooltip.querySelector('.tooltip-skip');
        const nextBtn = tooltip.querySelector('.tooltip-next');
        const closeBtn = tooltip.querySelector('.tooltip-close');

        if (skipBtn) skipBtn.addEventListener('click', () => this.completeOnboarding());

        // Add next button event listener
        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                // Auto-open elements if needed
                if (step.autoOpenAfterNext) {
                    if (step.element === '#settingsToggle') {
                        targetElement.click();
                        this.settingsOpened = true;
                        setTimeout(() => this.nextStep(), 500);
                    } else if (step.element === '#processingToggle') {
                        targetElement.click();
                        this.launchOpened = true;
                        setTimeout(() => this.nextStep(), 500);
                    } else {
                        this.nextStep();
                    }
                } else {
                    this.nextStep();
                }
            });
        }

        if (closeBtn) closeBtn.addEventListener('click', () => this.completeOnboarding());

        // Create connector line
        this.createConnector(targetElement, tooltip, step.position);
    }

    positionTooltip(tooltip, targetElement, position) {
        // Get positions
        const targetRect = targetElement.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Position based on requested position
        let top, left;

        switch (position) {
            case 'right':
                left = targetRect.right + 15;
                top = targetRect.top + (targetRect.height / 2) - (tooltipRect.height / 2);
                break;
            case 'left':
                left = targetRect.left - tooltipRect.width - 15;
                top = targetRect.top + (targetRect.height / 2) - (tooltipRect.height / 2);
                break;
            case 'top':
                left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2);
                top = targetRect.top - tooltipRect.height - 15;
                break;
            case 'bottom':
                left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2);
                top = targetRect.bottom + 15;
                break;
        }

        // Keep tooltip within viewport
        left = Math.max(10, Math.min(left, viewportWidth - tooltipRect.width - 10));
        top = Math.max(10, Math.min(top, viewportHeight - tooltipRect.height - 10));

        tooltip.style.top = `${top}px`;
        tooltip.style.left = `${left}px`;
    }

    toggleOverlay(show) {
        if (this.overlay) {
            if (show) {
                this.overlay.classList.add('active');
            } else {
                this.overlay.classList.remove('active');
            }
        }
    }

    removeAllTooltips() {
        document.querySelectorAll('.onboarding-tooltip, .onboarding-connector').forEach(el => {
            el.remove();
        });

        document.querySelectorAll('.onboarding-highlight').forEach(el => {
            el.classList.remove('onboarding-highlight');
        });
    }

    closeSettingsModal() {
        // Find the close button in the settings modal and click it
        const closeBtn = document.querySelector('#settingsModal .modal-backdrop');
        if (closeBtn) {
            closeBtn.click();
            this.settingsOpened = false;
        }
    }

    closeLaunchModal() {
        // Don't close launch modal if we need to keep it open
        const currentStep = this.steps[this.currentStep];
        if (currentStep && currentStep.keepOpen) {
            return;
        }

        // Find the close button in the launch modal and click it
        const closeBtn = document.querySelector('#launchModal .modal-backdrop');
        if (closeBtn) {
            closeBtn.click();
            this.launchOpened = false;
        }
    }

    createConnector(targetElement, tooltipElement, position) {
        // Create a connector (line) between target and tooltip
        const connector = document.createElement('div');
        connector.className = 'onboarding-connector';

        // Get positions
        const targetRect = targetElement.getBoundingClientRect();
        const tooltipRect = tooltipElement.getBoundingClientRect();

        // Calculate connector points
        let startX, startY, endX, endY;

        switch (position) {
            case 'right':
                startX = targetRect.right;
                startY = targetRect.top + targetRect.height / 2;
                endX = tooltipRect.left;
                endY = tooltipRect.top + tooltipRect.height / 2;
                break;
            case 'left':
                startX = targetRect.left;
                startY = targetRect.top + targetRect.height / 2;
                endX = tooltipRect.right;
                endY = tooltipRect.top + tooltipRect.height / 2;
                break;
            case 'top':
                startX = targetRect.left + targetRect.width / 2;
                startY = targetRect.top;
                endX = tooltipRect.left + tooltipRect.width / 2;
                endY = tooltipRect.bottom;
                break;
            case 'bottom':
                startX = targetRect.left + targetRect.width / 2;
                startY = targetRect.bottom;
                endX = tooltipRect.left + tooltipRect.width / 2;
                endY = tooltipRect.top;
                break;
        }

        // Calculate length and angle
        const length = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
        const angle = Math.atan2(endY - startY, endX - startX) * 180 / Math.PI;

        // Apply styles
        connector.style.width = `${length}px`;
        connector.style.left = `${startX}px`;
        connector.style.top = `${startY}px`;
        connector.style.transform = `rotate(${angle}deg)`;

        document.body.appendChild(connector);
    }

    nextStep() {
        // Remove existing tooltips and highlights
        this.removeAllTooltips();

        // Go to next step
        if (this.currentStep < this.steps.length - 1) {
            this.showStep(this.currentStep + 1);
        } else {
            this.completeOnboarding();
        }
    }

    async completeOnboarding() {
        // Save completion status to server
        try {
            const response = await fetch('/set_onboarding_complete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                console.error('Failed to save onboarding completion status');
            }
        } catch (error) {
            console.error('Error saving onboarding completion status:', error);
        }

        // Remove overlay
        if (this.overlay) {
            this.overlay.classList.remove('active');
            setTimeout(() => {
                this.overlay.remove();
            }, 300);
        }

        // Remove tooltips and highlights
        this.removeAllTooltips();

        // Close modals if open and not needed to keep open
        if (this.settingsOpened) {
            this.closeSettingsModal();
        }

        const currentStep = this.steps[this.currentStep];
        if (this.launchOpened && (!currentStep || !currentStep.keepOpen)) {
            this.closeLaunchModal();
        }

        // Remove modal with animation
        const modal = document.getElementById('onboardingModal');
        if (modal) {
            const content = modal.querySelector('.onboarding-content');
            content.classList.add('fade-out');

            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }
}

// Initialize onboarding when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
    window.onboardingManager = new OnboardingManager();
});
