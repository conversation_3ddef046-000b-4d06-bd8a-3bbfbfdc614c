class ControlFactory {
    static createControl(id, metadata) {
        switch(metadata.controlType) {
            case 'range':
                return this.createRangeControl(id, metadata);
            case 'singleRange':
                return this.createSingleRangeControl(id, metadata);
            case 'complex':
                return this.createComplexControl(id, metadata);
            default:
                console.warn(`Unknown control type: ${metadata.controlType}`);
                return null;
        }
    }

    static createRangeControl(id, metadata) {
        const container = document.createElement('div');
        container.className = 'space-y-4';

        const labelContainer = document.createElement('div');
        labelContainer.className = 'flex justify-between items-center';

        const labelText = document.createElement('span');
        labelText.className = 'text-sm text-gray-300';
        labelText.textContent = metadata.translation;

        const valueDisplay = document.createElement('span');
        valueDisplay.className = 'text-sm font-mono text-gray-300';
        valueDisplay.id = `${id}_value`;

        labelContainer.appendChild(labelText);
        labelContainer.appendChild(valueDisplay);

        const rangeContainer = document.createElement('div');
        rangeContainer.className = 'range-slider relative pt-1 pb-4';

        const track = document.createElement('div');
        track.className = 'range-track';

        const selected = document.createElement('div');
        selected.className = 'range-selected';

        const useIntegers = metadata.integer === true;

        const minInput = document.createElement('input');
        minInput.type = 'range';
        minInput.className = 'absolute z-30'; // Increased z-index from z-20 to z-30
        minInput.min = metadata.range[0];
        minInput.max = metadata.range[1];
        minInput.step = useIntegers ? 1 : (metadata.range[1] - metadata.range[0]) / 100;
        minInput.value = metadata.range[0];
        minInput.dataset.setting = `${id}_min`;

        const maxInput = document.createElement('input');
        maxInput.type = 'range';
        maxInput.className = 'absolute z-20'; // Keep z-20 for the max slider
        maxInput.min = metadata.range[0];
        maxInput.max = metadata.range[1];
        maxInput.step = useIntegers ? 1 : (metadata.range[1] - metadata.range[0]) / 100;
        maxInput.value = metadata.range[1];
        maxInput.dataset.setting = `${id}_max`;

        const savedValues = window.settingsManager.settingsData[id.split('_')[0]];
        let minValue = metadata.range[0];
        let maxValue = metadata.range[1];

        if (savedValues) {
            const paramParts = id.split('_');
            const mainParam = paramParts[0]; // "border"
            const subParam = paramParts.slice(1).join('_'); // "radius_min"

            // Для complex-параметров с суффиксом _range
            const rangeKey = `${subParam}_range`;
            if (savedValues[rangeKey] && Array.isArray(savedValues[rangeKey])) {
                [minValue, maxValue] = savedValues[rangeKey];
            }
            // Для старых форматов данных
            else if (savedValues[subParam]?.range) {
                [minValue, maxValue] = savedValues[subParam].range;
            }
            // Для базовых параметров
            else if (Array.isArray(savedValues.range)) {
                [minValue, maxValue] = savedValues.range;
            }
        }

        minInput.value = minValue;
        maxInput.value = maxValue;

        // Добавляем функционал обновления значений
        const updateRange = () => {
            const minVal = useIntegers ? parseInt(minInput.value) : parseFloat(minInput.value);
            const maxVal = useIntegers ? parseInt(maxInput.value) : parseFloat(maxInput.value);
            const percent = x => ((x - metadata.range[0]) / (metadata.range[1] - metadata.range[0])) * 100;

            selected.style.left = `${percent(minVal)}%`;
            selected.style.width = `${percent(maxVal) - percent(minVal)}%`;
            valueDisplay.textContent = useIntegers ?
                `${minVal} - ${maxVal}` :
                `${minVal.toFixed(2)} - ${maxVal.toFixed(2)}`;
        };

        // Add a small offset when sliders are close to each other to prevent overlap issues
        const checkOverlap = () => {
            const minVal = parseFloat(minInput.value);
            const maxVal = parseFloat(maxInput.value);
            const range = metadata.range[1] - metadata.range[0];
            const threshold = range * 0.02; // 2% of the range as threshold

            // If sliders are very close, modify the appearance to make it clear
            if (Math.abs(maxVal - minVal) < threshold) {
                minInput.style.marginLeft = '-5px'; // Slightly offset to the left
            } else {
                minInput.style.marginLeft = '0';
            }
        };

        minInput.addEventListener('input', () => {
            const minVal = parseFloat(minInput.value);
            const maxVal = parseFloat(maxInput.value);
            if (minVal > maxVal) {
                minInput.value = maxVal;
            }
            updateRange();
            checkOverlap();
        });

        maxInput.addEventListener('input', () => {
            const minVal = parseFloat(minInput.value);
            const maxVal = parseFloat(maxInput.value);
            if (maxVal < minVal) {
                maxInput.value = minVal;
            }
            updateRange();
            checkOverlap();
        });

        // When the sliders are very close and user tries to drag,
        // prioritize min slider's movement
        let isDraggingMin = false;

        minInput.addEventListener('mousedown', () => {
            isDraggingMin = true;
            // Temporarily increase z-index even more when being dragged
            minInput.style.zIndex = '40';
        });

        document.addEventListener('mouseup', () => {
            if (isDraggingMin) {
                isDraggingMin = false;
                // Reset z-index after drag
                minInput.style.zIndex = '30';
            }
        });

        // Добавляем обработчики для сохранения
        minInput.addEventListener('change', (event) => {
            if (event.isTrusted) {
                window.settingsManager.autoSaveSettings(id);
            }
        });
        maxInput.addEventListener('change', (event) => {
            if (event.isTrusted) {
                window.settingsManager.autoSaveSettings(id);
            }
        });

        rangeContainer.appendChild(track);
        rangeContainer.appendChild(selected);
        rangeContainer.appendChild(minInput);
        rangeContainer.appendChild(maxInput);

        container.appendChild(labelContainer);
        container.appendChild(rangeContainer);

        updateRange();
        checkOverlap(); // Check initial state
        return container;
    }

    static createSingleRangeControl(id, metadata) {
        const container = document.createElement('div');
        container.className = 'space-y-4';

        const labelContainer = document.createElement('div');
        labelContainer.className = 'flex justify-between items-center';

        const labelText = document.createElement('span');
        labelText.className = 'text-sm text-gray-300';
        labelText.textContent = metadata.translation;

        const valueDisplay = document.createElement('span');
        valueDisplay.className = 'text-sm font-mono text-gray-300';
        valueDisplay.id = `${id}_value`;

        labelContainer.appendChild(labelText);
        labelContainer.appendChild(valueDisplay);

        const rangeContainer = document.createElement('div');
        rangeContainer.className = 'range-slider relative pt-1 pb-4';

        const track = document.createElement('div');
        track.className = 'range-track';

        const selected = document.createElement('div');
        selected.className = 'range-selected';

        const input = document.createElement('input');
        input.type = 'range';
        input.className = 'absolute z-20';
        input.min = metadata.range[0];
        input.max = metadata.range[1];
        input.step = metadata.step || 0.01;
        input.value = metadata.range[0];
        input.dataset.setting = id;

        const savedValues = window.settingsManager.settingsData[id];
        let value = metadata.range[0];

        if (savedValues && savedValues.probability !== undefined) {
            value = savedValues.probability;
        }

        input.value = value;

        const updateValue = () => {
            valueDisplay.textContent = parseFloat(input.value).toFixed(2);
            const percent = ((input.value - metadata.range[0]) / (metadata.range[1] - metadata.range[0])) * 100;
            selected.style.width = `${percent}%`;
        };

        input.addEventListener('input', updateValue);

        // Добавляем обработчик для сохранения
        input.addEventListener('change', (event) => {
            if (event.isTrusted) {
                window.settingsManager.autoSaveSettings(id);
            }
        });

        rangeContainer.appendChild(track);
        rangeContainer.appendChild(selected);
        rangeContainer.appendChild(input);

        container.appendChild(labelContainer);
        container.appendChild(rangeContainer);

        updateValue();
        return container;
    }

    static createComplexControl(id, metadata) {
        const container = document.createElement('div');
        container.className = 'space-y-4';

        Object.entries(metadata.controls).forEach(([controlId, controlMetadata]) => {
            const fullId = `${id}_${controlId}`;
            let control;

            switch(controlMetadata.type) {
                case 'range':
                    control = this.createRangeControl(fullId, controlMetadata);
                    break;
                case 'select':
                    control = this.createSelectControl(fullId, controlMetadata);
                    break;
                case 'toggle':
                    control = this.createToggleControl(fullId, controlMetadata);
                    break;
            }

            if (control) {
                container.appendChild(control);
            }
        });

        return container;
    }

    static createSelectControl(id, metadata) {
        const container = document.createElement('div');
        container.className = 'space-y-2';

        const label = document.createElement('label');
        label.className = 'text-sm text-gray-300';
        label.textContent = metadata.translation;

        const select = document.createElement('select');
        select.className = 'w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-sm text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 hover:bg-gray-600 transition-colors';
        select.dataset.setting = id;

        metadata.options.forEach(option => {
            const opt = document.createElement('option');
            opt.value = option;
            opt.textContent = metadata.translations?.[option] || option;
            select.appendChild(opt);
        });

        select.addEventListener('change', (event) => {
            if (event.isTrusted) {
                window.settingsManager.autoSaveSettings(id);
                // Добавляем анимацию при изменении
                select.classList.add('ring-2', 'ring-blue-500');
                setTimeout(() => {
                    select.classList.remove('ring-2', 'ring-blue-500');
                }, 200);
            }
        });

        const wrapper = document.createElement('div');
        wrapper.className = 'relative';

        const arrow = document.createElement('div');
        arrow.className = 'absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none';
        arrow.innerHTML = '<i data-lucide="chevron-down" class="w-4 h-4 text-gray-400"></i>';

        const savedValues = window.settingsManager.settingsData[id.split('_')[0]];
        if (savedValues) {
            const paramName = id.split('_')[1];
            if (paramName && savedValues[paramName]) {
                select.value = savedValues[paramName];
            } else if (savedValues.pattern) {
                select.value = savedValues.pattern;
            }
        }

        wrapper.appendChild(select);
        wrapper.appendChild(arrow);

        container.appendChild(label);
        container.appendChild(wrapper);

        return container;
    }

    static createToggleControl(id, metadata) {
        const container = document.createElement('div');
        container.className = 'flex items-center justify-between mb-4';

        const label = document.createElement('span');
        label.className = 'text-sm text-gray-300';
        label.textContent = metadata.translation;

        const toggle = document.createElement('label');
        toggle.className = 'switch';

        const input = document.createElement('input');
        input.type = 'checkbox';
        input.checked = false;
        input.dataset.setting = `${id}_enabled`;

        const savedValues = window.settingsManager.settingsData[id.split('_')[0]];
        if (savedValues) {
            const paramName = id.split('_')[1];
            if (paramName) {
                input.checked = savedValues[paramName] || false;
            }
        }

        input.addEventListener('change', (event) => {
            if (event.isTrusted) {
                window.settingsManager.autoSaveSettings(id);
            }
        });

        const slider = document.createElement('span');
        slider.className = 'switch-slider';

        toggle.appendChild(input);
        toggle.appendChild(slider);

        container.appendChild(label);
        container.appendChild(toggle);

        return container;
    }
}

class SettingsManager {
    constructor() {
        this.currentTab = null;
        this.settings = null;
        this.defaultSettings = {};
        this.categories = [];
        this.settingsData = {};
        this.init();
        this.createModal();
        this.initModalHandlers();
    }

    async init() {
        try {
            // Fetch default settings and categories from server
            const [settingsResponse, categoriesResponse] = await Promise.all([
                fetch('/get_default_settings'),
                fetch('/get_categories')
            ]);

            if (!settingsResponse.ok || !categoriesResponse.ok) {
                throw new Error('Failed to fetch settings or categories');
            }

            this.defaultSettings = await settingsResponse.json();
            this.categories = await categoriesResponse.json();
            this.settingsData = JSON.parse(JSON.stringify(this.defaultSettings));

            // Initialize UI after getting data
            this.renderTabs();

            // Show first category by default
            if (this.categories.length > 0) {
                this.showCategory(this.categories[0]);
            }
        } catch (error) {
            console.error('Failed to initialize settings:', error);
            this.showNotification('Ошибка при загрузке настроек', 'error');
        }
    }

    initModalHandlers() {
        // Открытие модального окна
        const settingsToggle = document.getElementById('settingsToggle');
        if (settingsToggle) {
            settingsToggle.addEventListener('click', () => this.openModal());
        }

        // Закрытие модального окна по клику на фон
        const backdrop = document.querySelector('#settingsModal .absolute.inset-0');
        if (backdrop) {
            backdrop.addEventListener('click', (e) => {
                if (e.target === backdrop) {
                    this.closeModal();
                }
            });
        }
    }

    openModal() {
        const modal = document.getElementById('settingsModal');
        const backdrop = modal.querySelector('.modal-backdrop');
        const dialog = modal.querySelector('.modal-dialog');

        modal.classList.remove('hidden');
        modal.classList.add('flex');

        // Trigger animations
        requestAnimationFrame(() => {
            backdrop.classList.add('active');
            dialog.classList.add('active');
        });
    }

    closeModal() {
        const modal = document.getElementById('settingsModal');
        const backdrop = modal.querySelector('.modal-backdrop');
        const dialog = modal.querySelector('.modal-dialog');

        // Start closing animations
        backdrop.classList.remove('active');
        dialog.classList.remove('active');

        // Wait for animations to complete
        setTimeout(() => {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }, 300);
    }

    renderTabs() {
        const tabsContainer = document.getElementById('settingsTabs');
        if (!tabsContainer) return;

        tabsContainer.innerHTML = '';

        this.categories.forEach(category => {
            const button = document.createElement('button');
            button.className = `w-full text-left px-4 py-3 rounded-lg transition-colors flex items-center gap-3
                              hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500`;
            button.setAttribute('data-category', category.id);
            button.innerHTML = `
                <i data-lucide="${category.icon}" class="w-5 h-5"></i>
                <span>${category.label}</span>
            `;
            button.addEventListener('click', () => this.showCategory(category));
            tabsContainer.appendChild(button);
        });

        lucide.createIcons();
    }

    updateIcons() {
        setTimeout(() => {
            lucide.createIcons();
        }, 0);
    }

    async showCategory(category) {
        this.currentTab = category;
        const content = document.getElementById('settingsContent');
        content.innerHTML = '';

        try {
            // Загружаем настройки для выбранной категории
            const response = await fetch(`/get_category_settings/${category.id}`);
            const data = await response.json();

            if (data.success) {
                // Обновляем локальные данные
                Object.assign(this.settingsData, data.settings);
                                // Добавьте лог для отладки
                console.log('Loaded settings:', this.settingsData);
                // Отображаем элементы управления с загруженными значениями
                category.items.forEach(itemId => {
                    const setting = this.settingsData[itemId];
                    if (!setting) return;

                    const section = this.createSettingSection(itemId, setting);
                    content.appendChild(section);
                });

                // Обновляем активную вкладку
                document.querySelectorAll('#settingsTabs button').forEach(btn => {
                    btn.classList.toggle('bg-gray-700',
                        btn.getAttribute('data-category') === category.id);
                });

                this.updateIcons();
            } else {
                throw new Error(data.error || 'Failed to load settings');
            }
        } catch (error) {
            console.error('Error loading category settings:', error);
            this.showNotification('Ошибка при загрузке настроек', 'error');
        }
    }

    createSettingSection(id, setting) {
        const section = document.createElement('div');
        section.className = 'last:mb-0 bg-gray-900/30 rounded-lg p-4';

        const header = document.createElement('div');
        header.className = 'flex items-center justify-between mb-4';

        const title = document.createElement('h3');
        title.className = 'text-lg font-semibold flex items-center gap-2';

        const settingData = this.defaultSettings[id];
        const settingTitle = settingData ? settingData.translation : id;
        title.innerHTML = `${this.getSettingIcon(id)} ${settingTitle}`;

        const enabledSwitch = this.createToggleSwitch(id, setting.enabled);

        header.appendChild(title);
        header.appendChild(enabledSwitch);
        section.appendChild(header);

        if (settingData.processing_time === 'long') {
            const warning = document.createElement('div');
            warning.className = 'text-yellow-500 text-sm mb-4 flex items-center gap-2';
            warning.innerHTML = `
                <i data-lucide="alert-triangle" class="w-4 h-4"></i>
                Этот эффект может значительно увеличить время обработки
            `;
            section.appendChild(warning);
        }

        // Используем фабрику для создания контролов
        const controls = ControlFactory.createControl(id, settingData);
        if (controls) {
            controls.className += ' setting-controls';
            controls.style.display = setting.enabled ? 'block' : 'none';
            section.appendChild(controls);
        }

        enabledSwitch.querySelector('input').addEventListener('change', (e) => {
            if (controls) {
                controls.style.display = e.target.checked ? 'block' : 'none';
            }
        });

        lucide.createIcons();
        return section;
    }

    createModal() {
        // Add close button with proper animation handling
        const headerSection = document.querySelector('#settingsModal .p-6.pb-4');
        if (headerSection) {
            const closeButton = document.createElement('button');
            closeButton.className = 'absolute top-6 right-6 text-gray-400 hover:text-gray-300';
            closeButton.innerHTML = '<i data-lucide="x" class="w-6 h-6"></i>';

            // Use the same closeModal method for consistency
            closeButton.addEventListener('click', () => this.closeModal());

            headerSection.appendChild(closeButton);
        }

        lucide.createIcons();
    }

    createToggleSwitch(id, initialState) {
        const label = document.createElement('label');
        label.className = 'switch';

        const input = document.createElement('input');
        input.type = 'checkbox';
        input.checked = initialState;
        input.dataset.setting = `${id}_enabled`;

        // Добавляем обработчик для автосохранения только при реальном изменении пользователем
        input.addEventListener('change', (event) => {
            // Предотвращаем автоматическое обновление при загрузке
            if (event.isTrusted) {  // Проверяем, что событие вызвано пользователем
                this.autoSaveSettings(`${id}_enabled`);
            }
        });

        const slider = document.createElement('span');
        slider.className = 'switch-slider';

        label.appendChild(input);
        label.appendChild(slider);
        return label;
    }

    getSettingIcon(id) {
        const settingData = this.defaultSettings[id];
        const iconName = settingData?.icon || 'settings-2';
        return `<i data-lucide="${iconName}" class="w-5 h-5"></i>`;
    }


    getCategoryItems(categoryId) {
        const categoryMap = {
            basic: ['scale', 'saturation', 'transparency', 'gamma'],
            color: ['brightness', 'contrast', 'white_balance'],
            motion: ['movement'],
            effects: ['blur', 'vignette', 'noise', 'snow'],
            overlay: ['lines', 'border'],
            advanced: ['frame_removal']
        };
        return categoryMap[categoryId] || [];
    }

    getRangeValues(id) {
        const minInput = document.querySelector(`[data-setting="${id}_min"]`);
        const maxInput = document.querySelector(`[data-setting="${id}_max"]`);
        return {
            min: minInput ? parseFloat(minInput.value) : null,
            max: maxInput ? parseFloat(maxInput.value) : null
        };
    }

    getSettingValue(id) {
        const element = document.querySelector(`[data-setting="${id}"]`);
        if (!element) return null;
        return element.type === 'checkbox' ? element.checked : element.value;
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `fixed bottom-4 right-4 px-4 py-2 rounded-lg text-white text-sm
                                ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}
                                transform transition-all duration-300 translate-y-0 opacity-90`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Уменьшаем время показа уведомления
        setTimeout(() => {
            notification.classList.add('translate-y-full', 'opacity-0');
            setTimeout(() => notification.remove(), 300);
        }, 1500); // Уменьшено с 3000 до 1500 мс
    }

    async autoSaveSettings(changedSettingId) {
        // Получаем базовый id настройки
        const getBaseSettingId = (id) => {
            // Убираем суффиксы _min, _max, _enabled
            const cleanId = id.replace(/_(?:min|max|enabled)$/, '');
            // Ищем родительский параметр в defaultSettings
            return Object.keys(this.defaultSettings).find(key =>
                cleanId === key || cleanId.startsWith(`${key}_`)
            ) || cleanId;
        };

        const baseSettingId = getBaseSettingId(changedSettingId);
        const settingMetadata = this.defaultSettings[baseSettingId];

        if (!settingMetadata) return;

        // Получаем состояние включения
        const enabledState = this.getSettingValue(`${baseSettingId}_enabled`);

        // Формируем данные для отправки на сервер
        const settingData = {
            settingId: baseSettingId,
            type: settingMetadata.controlType, // Новая строка
            value: {
                enabled: enabledState
            }
        };

        // В зависимости от типа контрола собираем данные
        switch(settingMetadata.controlType) {
            case 'range':
                settingData.value.range = this.getRangeValues(baseSettingId);
                break;

            case 'singleRange':
                settingData.value.probability = parseFloat(this.getSettingValue(baseSettingId));
                break;

            case 'complex':
                Object.entries(settingMetadata.controls).forEach(([controlId, controlData]) => {
                    switch(controlData.type) {
                        case 'range':
                            settingData.value[controlId] = {
                                type: 'range',  // Добавляем тип
                                ...this.getRangeValues(`${baseSettingId}_${controlId}`)
                            };
                            break;
                        case 'select':
                            settingData.value[controlId] = {
                                type: 'select', // Добавляем тип
                                value: this.getSettingValue(`${baseSettingId}_${controlId}`)
                            };
                            break;
                        case 'toggle':
                            settingData.value[controlId] = {
                                type: 'toggle', // Добавляем тип
                                value: this.getSettingValue(`${baseSettingId}_${controlId}_enabled`)
                            };
                            break;
                    }
                });
                break;
        }

        try {
            const response = await fetch('/update_setting', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settingData)
            });

            const data = await response.json();
            if (data.success) {
                this.showNotification('Настройка сохранена', 'success');
            } else {
                throw new Error(data.error || 'Failed to save setting');
            }
        } catch (error) {
            this.showNotification('Ошибка при сохранении', 'error');
            console.error('Save error:', error);
        }
    }
}

// Инициализация менеджера настроек при загрузке страницы
document.addEventListener('DOMContentLoaded', () => {
    window.settingsManager = new SettingsManager();
});
settingsToggle.addEventListener('click', () => {
    settingsModal.classList.remove('hidden');
    settingsModal.classList.add('flex');
});

