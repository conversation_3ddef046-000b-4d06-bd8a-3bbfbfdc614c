"""
System utilities for cross-platform operations.

This module provides utilities for system identification and platform-specific operations.
"""

import subprocess
import platform
import uuid
from typing import Optional


class SystemUtils:
    """Utility class for system-related operations."""
    
    @staticmethod
    def get_system_uuid() -> str:
        """
        Get system UUID in a cross-platform way.
        
        Returns:
            str: System UUID string
        """
        try:
            system = platform.system().lower()

            if system == "windows":
                return SystemUtils._get_windows_uuid()
            elif system == "darwin":  # macOS
                return SystemUtils._get_macos_uuid()
            elif system == "linux":
                return SystemUtils._get_linux_uuid()
            else:
                # Fallback: generate UUID based on MAC address
                return str(uuid.uuid1())

        except Exception as e:
            print(f"Error getting system UUID: {e}")
            # Last fallback: random UUID
            return str(uuid.uuid4())
    
    @staticmethod
    def _get_windows_uuid() -> Optional[str]:
        """Get Windows system UUID using PowerShell."""
        try:
            result = subprocess.check_output(
                'powershell "Get-CimInstance -Class Win32_ComputerSystemProduct | Select-Object -Property UUID"',
                shell=True
            ).decode()
            
            lines = result.split('\n')
            for line in lines:
                if line.strip() and not line.startswith('-') and 'UUID' not in line:
                    return line.strip()
        except Exception:
            pass
        return None
    
    @staticmethod
    def _get_macos_uuid() -> Optional[str]:
        """Get macOS system UUID using system_profiler."""
        try:
            result = subprocess.check_output(
                ["system_profiler", "SPHardwareDataType"],
                text=True
            )
            
            for line in result.split('\n'):
                if 'Hardware UUID' in line:
                    return line.split(':')[1].strip()
        except Exception:
            pass
        return None
    
    @staticmethod
    def _get_linux_uuid() -> Optional[str]:
        """Get Linux system UUID using multiple methods."""
        try:
            # Method 1: /sys/class/dmi/id/product_uuid
            try:
                with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                    return f.read().strip()
            except Exception:
                pass
            
            # Method 2: dmidecode
            try:
                result = subprocess.check_output(
                    ["sudo", "dmidecode", "-s", "system-uuid"],
                    text=True
                )
                return result.strip()
            except Exception:
                pass
                
        except Exception:
            pass
        return None


# Convenience function for backward compatibility
def get_system_uuid() -> str:
    """Get system UUID - convenience function."""
    return SystemUtils.get_system_uuid()
