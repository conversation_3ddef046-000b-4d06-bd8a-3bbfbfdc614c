"""
Image processing thread for GUI operations.

This module contains the ImageProcessingThread class that handles
asynchronous image processing operations.
"""

import os
from typing import Dict, Any, Optional
from PyQt6.QtCore import QThread, pyqtSignal


class ImageProcessingThread(QThread):
    """
    Thread for handling image processing operations.
    
    This thread processes images asynchronously to avoid blocking the GUI.
    It supports both single and infinite processing modes.
    """
    
    # Signals
    finished = pyqtSignal()
    error = pyqtSignal(str)
    image_generated = pyqtSignal(int, int)
    backgrounds_depleted = pyqtSignal()
    
    def __init__(self, config: Any, folder_data: Dict[int, Dict[str, Any]], 
                 num_images: int, infinite: bool = False):
        """
        Initialize the processing thread.
        
        Args:
            config: Configuration object for image processing
            folder_data: Dictionary containing folder information
            num_images: Number of images to process
            infinite: Whether to run in infinite processing mode
        """
        super().__init__()
        self.config = config
        self.folder_data = folder_data
        self.num_images = num_images
        self.infinite = infinite
        self.is_running = True
        self.images_generated = {idx: 0 for idx in folder_data.keys()}
        self.processing = False

    def run(self):
        """Main processing loop."""
        try:
            self.processing = True
            
            # Check if background processing is enabled
            if self.config.background_enabled:
                if not self._load_backgrounds():
                    return
            
            # Get active folders based on mode
            active_folders = self._get_active_folders()
            
            if not active_folders:
                self.error.emit("Нет активных папок для обработки")
                self.processing = False
                self.finished.emit()
                return
            
            # Main processing loop
            self._process_images(active_folders)
            
        except Exception as e:
            print(f"Общая ошибка в потоке обработки: {str(e)}")
            self.processing = False
            self.error.emit(str(e))
        finally:
            self.processing = False
            self.finished.emit()

    def _load_backgrounds(self) -> bool:
        """
        Load background images if needed.
        
        Returns:
            bool: True if backgrounds loaded successfully, False otherwise
        """
        try:
            from uniqualizer import load_backgrounds
            backgrounds_info = load_backgrounds(self.config)
            return True
        except Exception as e:
            print(f"Error loading backgrounds: {str(e)}")
            self.is_running = False
            self.backgrounds_depleted.emit()
            return False

    def _get_active_folders(self) -> list:
        """
        Get list of active folders based on current mode.
        
        Returns:
            list: List of active folder indices
        """
        if getattr(self.config, 'overlay_only_mode', False):
            # In overlay_only_mode, allow folders without input files
            return [idx for idx, data in self.folder_data.items() if data['active']]
        else:
            # In normal mode, require input file
            return [idx for idx, data in self.folder_data.items()
                   if data['active'] and data['input_file']]

    def _process_images(self, active_folders: list):
        """
        Process images for active folders.
        
        Args:
            active_folders: List of active folder indices
        """
        current_image = 0
        
        while self.is_running:
            for folder_idx in active_folders:
                if not self.is_running:
                    break
                
                if self._process_single_folder(folder_idx, current_image):
                    self.images_generated[folder_idx] += 1
                    self.image_generated.emit(folder_idx, self.images_generated[folder_idx])
            
            current_image += 1
            if not self.infinite and current_image >= self.num_images:
                break

    def _process_single_folder(self, folder_idx: int, current_image: int) -> bool:
        """
        Process a single folder.
        
        Args:
            folder_idx: Index of the folder to process
            current_image: Current image number
            
        Returns:
            bool: True if processing was successful, False otherwise
        """
        try:
            input_file = self.folder_data[folder_idx]['input_file']
            output_dir = os.path.join(self.config.base_dir, f"output_{folder_idx}")
            
            # Set output directory in config
            self.config.output_dir = output_dir
            
            # Process based on mode
            if getattr(self.config, 'overlay_only_mode', False):
                return self._process_overlay_only(current_image)
            else:
                return self._process_normal(input_file, current_image)
                
        except Exception as e:
            print(f"Ошибка при обработке папки {folder_idx}: {str(e)}")
            if "backgrounds нет изображений" in str(e):
                self.is_running = False
                self.backgrounds_depleted.emit()
            return False

    def _process_overlay_only(self, current_image: int) -> bool:
        """
        Process in overlay-only mode.
        
        Args:
            current_image: Current image number
            
        Returns:
            bool: True if processing was successful
        """
        try:
            from uniqualizer import process_image_thread
            process_image_thread(None, self.config, current_image)
            return True
        except Exception as e:
            self._handle_processing_error(e)
            return False

    def _process_normal(self, input_file: str, current_image: int) -> bool:
        """
        Process in normal mode with input file.
        
        Args:
            input_file: Path to input file
            current_image: Current image number
            
        Returns:
            bool: True if processing was successful
        """
        try:
            from uniqualizer import process_image_thread
            process_image_thread(input_file, self.config, current_image)
            return True
        except Exception as e:
            self._handle_processing_error(e)
            return False

    def _handle_processing_error(self, error: Exception):
        """
        Handle processing errors.
        
        Args:
            error: The exception that occurred
        """
        error_str = str(error)
        print(f"Ошибка при обработке изображения: {error_str}")
        
        if "backgrounds нет изображений" in error_str:
            self.is_running = False
            self.backgrounds_depleted.emit()

    def stop(self):
        """Stop the processing thread."""
        self.is_running = False
