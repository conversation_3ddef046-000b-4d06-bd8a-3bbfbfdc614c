"""
UI Builder for constructing interface components.

This module provides utilities for building consistent UI components
across the application.
"""

from typing import Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, 
    QPushButton, QCheckBox, QLineEdit
)
from PyQt6.QtGui import QFont
from .widgets import RangeSpinBox, HaltonRangeSpinBox, EffectGroup


class UIBuilder:
    """
    Builder class for creating consistent UI components.
    
    This class provides methods to create standardized UI components
    with consistent styling and behavior.
    """
    
    def __init__(self):
        """Initialize the UI builder."""
        self.folder_widgets: Dict[int, Dict[str, Any]] = {}
        self.preset_widgets: Dict[str, Any] = {}
        self.control_widgets: Dict[str, Any] = {}

    def create_folder_panel(self) -> QWidget:
        """
        Create the folder selection panel.
        
        Returns:
            QWidget: Folder selection panel
        """
        folders_panel = QWidget()
        folders_layout = QVBoxLayout(folders_panel)
        folders_layout.setSpacing(10)
        
        # Title
        folders_label = QLabel("Выбор папок для обработки")
        folders_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        folders_layout.addWidget(folders_label)
        
        # Create folder widgets
        for i in range(3):
            folder_widget = self._create_folder_widget(i + 1)
            folders_layout.addWidget(folder_widget)
        
        return folders_panel

    def _create_folder_widget(self, folder_idx: int) -> QWidget:
        """
        Create a single folder widget.
        
        Args:
            folder_idx: Index of the folder
            
        Returns:
            QWidget: Folder widget
        """
        folder_widget = QWidget()
        folder_layout = QHBoxLayout(folder_widget)
        folder_layout.setContentsMargins(0, 0, 0, 0)
        
        # Checkbox for folder activation
        checkbox = QCheckBox(f"Папка {folder_idx}")
        checkbox.setChecked(True)
        
        # Image selection button
        select_btn = QPushButton(f"Выбрать изображение {folder_idx}")
        select_btn.setObjectName(f"select_btn_{folder_idx}")
        
        # File label
        file_label = QLabel("Файл не выбран")
        file_label.setMinimumWidth(200)
        
        # Clear button
        clear_btn = QPushButton("✕")
        clear_btn.setFixedSize(24, 24)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff5a5f;
                color: white;
                border-radius: 12px;
                font-weight: bold;
                padding: 0;
            }
            QPushButton:hover {
                background-color: #e04146;
            }
        """)
        clear_btn.setVisible(False)
        clear_btn.setToolTip("Очистить выбранный файл")
        
        # Counter label
        counter_label = QLabel("Сгенерировано: 0")
        counter_label.setMinimumWidth(120)
        
        # Add to layout
        folder_layout.addWidget(checkbox)
        folder_layout.addWidget(select_btn)
        folder_layout.addWidget(file_label)
        folder_layout.addWidget(clear_btn)
        folder_layout.addWidget(counter_label)
        folder_layout.addStretch()
        
        # Store widget references
        self.folder_widgets[folder_idx] = {
            'checkbox': checkbox,
            'select_btn': select_btn,
            'file_label': file_label,
            'clear_btn': clear_btn,
            'counter_label': counter_label,
            'input_file': None,
            'generated_count': 0
        }
        
        return folder_widget

    def create_preset_panel(self) -> QWidget:
        """
        Create the preset management panel.
        
        Returns:
            QWidget: Preset panel
        """
        preset_panel = QWidget()
        preset_layout = QHBoxLayout(preset_panel)
        preset_layout.setContentsMargins(10, 5, 10, 5)
        
        # Preset name field
        preset_label = QLabel("Имя пресета:")
        preset_name_field = QLineEdit()
        preset_name_field.setMinimumWidth(200)
        preset_name_field.setPlaceholderText("Введите название пресета")
        
        # Export/Import buttons
        export_btn = QPushButton("Экспорт конфига")
        import_btn = QPushButton("Импорт конфига")
        
        # Add to layout
        preset_layout.addWidget(preset_label)
        preset_layout.addWidget(preset_name_field)
        preset_layout.addStretch()
        preset_layout.addWidget(export_btn)
        preset_layout.addWidget(import_btn)
        
        # Store widget references
        self.preset_widgets = {
            'name_field': preset_name_field,
            'export_btn': export_btn,
            'import_btn': import_btn
        }
        
        return preset_panel

    def create_control_panel(self) -> QWidget:
        """
        Create the control buttons panel.
        
        Returns:
            QWidget: Control panel
        """
        control_panel = QWidget()
        control_layout = QVBoxLayout(control_panel)
        
        # Generation mode controls
        generation_layout = QHBoxLayout()
        
        infinite_checkbox = QCheckBox("Бесконечная генерация")
        infinite_checkbox.setObjectName("infinite_generation")
        infinite_checkbox.setFont(QFont("Segoe UI", 9))
        
        num_images_spin = QSpinBox()
        num_images_spin.setRange(1, 1000)
        num_images_spin.setValue(1)
        num_images_spin.setFont(QFont("Segoe UI", 9))
        
        generation_layout.addWidget(infinite_checkbox)
        generation_layout.addWidget(QLabel("Количество изображений:"))
        generation_layout.addWidget(num_images_spin)
        generation_layout.addStretch()
        
        control_layout.addLayout(generation_layout)
        
        # Control buttons
        buttons_layout = QHBoxLayout()
        
        start_btn = QPushButton("Старт")
        stop_btn = QPushButton("Стоп")
        save_btn = QPushButton("Сохранить параметры")
        preview_btn = QPushButton("Предпросмотр")
        video_btn = QPushButton("Создать видео")
        
        buttons_layout.addWidget(start_btn)
        buttons_layout.addWidget(stop_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(preview_btn)
        buttons_layout.addWidget(video_btn)
        
        control_layout.addLayout(buttons_layout)
        
        # Store widget references
        self.control_widgets = {
            'infinite_checkbox': infinite_checkbox,
            'num_images_spin': num_images_spin,
            'start_btn': start_btn,
            'stop_btn': stop_btn,
            'save_btn': save_btn,
            'preview_btn': preview_btn,
            'video_btn': video_btn
        }
        
        return control_panel

    def create_effect_groups(self, parent_layout: QVBoxLayout) -> Dict[str, EffectGroup]:
        """
        Create effect configuration groups.
        
        Args:
            parent_layout: Parent layout to add groups to
            
        Returns:
            Dict[str, EffectGroup]: Dictionary of effect groups
        """
        groups = {}
        
        # RGB Shift group
        rgb_group = EffectGroup("RGB Смещение")
        rgb_group.setObjectName("rgb_shift_group")
        
        rgb_shift_range = RangeSpinBox("Смещение", -10, 10, -1.5, 1.5, 1)
        rgb_shift_range.min_spin.setSingleStep(0.1)
        rgb_shift_range.max_spin.setSingleStep(0.1)
        
        rgb_opacity_range = RangeSpinBox("Прозрачность", 0, 100, 50, 80)
        
        rgb_group.add_widgets([rgb_shift_range, rgb_opacity_range])
        parent_layout.addWidget(rgb_group)
        groups['rgb_shift'] = rgb_group
        
        # Lines group
        lines_group = EffectGroup("Линии")
        lines_group.setObjectName("lines_group")
        
        lines_count = RangeSpinBox("Количество линий", 1, 20, 3, 8)
        line_thickness = RangeSpinBox("Толщина", 0, 5, 0.4, 1.0, 1)
        line_opacity = RangeSpinBox("Прозрачность", 0, 100, 30, 70)
        
        lines_group.add_widgets([lines_count, line_thickness, line_opacity])
        parent_layout.addWidget(lines_group)
        groups['lines_group'] = lines_group
        
        # Add more effect groups as needed...
        # This is a simplified version - the full implementation would include all effects
        
        return groups

    def get_folder_widgets(self) -> Dict[int, Dict[str, Any]]:
        """
        Get folder widget references.
        
        Returns:
            Dict[int, Dict[str, Any]]: Folder widgets dictionary
        """
        return self.folder_widgets

    def get_preset_widgets(self) -> Dict[str, Any]:
        """
        Get preset widget references.
        
        Returns:
            Dict[str, Any]: Preset widgets dictionary
        """
        return self.preset_widgets

    def get_control_widgets(self) -> Dict[str, Any]:
        """
        Get control widget references.
        
        Returns:
            Dict[str, Any]: Control widgets dictionary
        """
        return self.control_widgets
