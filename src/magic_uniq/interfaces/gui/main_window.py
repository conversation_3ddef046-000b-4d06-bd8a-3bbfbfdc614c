"""
Main window for the Uniqualizer GUI application.

This module contains the main application window and its core functionality.
"""

import sys
import os
import json
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QSpinBox, QPushButton, QCheckBox, QFileDialog, QScrollArea, 
    QMessageBox, QLineEdit
)
from PyQt6.QtGui import QFont
from PyQt6.QtCore import Qt

from .widgets import RangeSpinBox, HaltonRangeSpinBox, EffectGroup
from .processing_thread import ImageProcessingThread
from .background_monitor import BackgroundMonitor
from .dialogs import VideoDialog
from .ui_builder import UIBuilder
from .config_manager import ConfigManager


class UniqualizerMainWindow(QMainWindow):
    """
    Main application window for the Uniqualizer GUI.
    
    This class manages the main user interface and coordinates
    between different components of the application.
    """
    
    def __init__(self):
        """Initialize the main window."""
        super().__init__()
        
        # Initialize components
        self.config_manager = ConfigManager()
        self.ui_builder = UIBuilder()
        self.processing_thread: Optional[ImageProcessingThread] = None
        self.background_monitor: Optional[BackgroundMonitor] = None
        self.folder_widgets: Dict[int, Dict[str, Any]] = {}
        self.groups: Dict[str, EffectGroup] = {}
        
        # Initialize UI
        self._setup_window()
        self._setup_ui()
        self._load_configuration()
        self._start_background_monitor()

    def _setup_window(self):
        """Set up basic window properties."""
        self.setWindowTitle("Uniqualizer")
        self.setMinimumWidth(800)
        self._apply_window_styles()

    def _apply_window_styles(self):
        """Apply consistent styling to the main window."""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                color: #333333;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
            QProgressBar {
                border: 1px solid #cccccc;
                border-radius: 3px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #007bff;
            }
            QCheckBox {
                color: #333333;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)

    def _setup_ui(self):
        """Set up the user interface."""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Build UI components
        self._add_folder_panel(main_layout)
        self._add_preset_panel(main_layout)
        self._add_effects_panel(main_layout)
        self._add_control_panel(main_layout)

    def _add_folder_panel(self, main_layout: QVBoxLayout):
        """Add folder selection panel."""
        folders_panel = self.ui_builder.create_folder_panel()
        self.folder_widgets = self.ui_builder.get_folder_widgets()
        
        # Connect folder selection signals
        for idx, widgets in self.folder_widgets.items():
            widgets['select_btn'].clicked.connect(
                lambda checked, folder_idx=idx: self.select_folder_image(folder_idx)
            )
            widgets['clear_btn'].clicked.connect(
                lambda checked, folder_idx=idx: self.clear_folder_image(folder_idx)
            )
        
        main_layout.addWidget(folders_panel)

    def _add_preset_panel(self, main_layout: QVBoxLayout):
        """Add preset management panel."""
        preset_panel = self.ui_builder.create_preset_panel()
        
        # Get preset widgets and connect signals
        preset_widgets = self.ui_builder.get_preset_widgets()
        self.preset_name_field = preset_widgets['name_field']
        
        preset_widgets['export_btn'].clicked.connect(self.export_config)
        preset_widgets['import_btn'].clicked.connect(self.import_config)
        
        main_layout.addWidget(preset_panel)

    def _add_effects_panel(self, main_layout: QVBoxLayout):
        """Add effects configuration panel."""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # Build effect groups
        self.groups = self.ui_builder.create_effect_groups(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)

    def _add_control_panel(self, main_layout: QVBoxLayout):
        """Add control buttons panel."""
        control_panel = self.ui_builder.create_control_panel()
        
        # Get control widgets and connect signals
        control_widgets = self.ui_builder.get_control_widgets()
        
        self.infinite_generation = control_widgets['infinite_checkbox']
        self.num_images_spin = control_widgets['num_images_spin']
        self.start_btn = control_widgets['start_btn']
        self.stop_btn = control_widgets['stop_btn']
        self.save_config_btn = control_widgets['save_btn']
        self.preview_btn = control_widgets['preview_btn']
        self.create_video_btn = control_widgets['video_btn']
        
        # Connect signals
        self.infinite_generation.stateChanged.connect(self.toggle_num_images)
        self.start_btn.clicked.connect(self.start_processing)
        self.stop_btn.clicked.connect(self.stop_processing)
        self.save_config_btn.clicked.connect(self.save_config)
        self.preview_btn.clicked.connect(self.generate_preview)
        self.create_video_btn.clicked.connect(self.show_video_dialog)
        
        # Set initial button states
        self.stop_btn.setEnabled(False)
        self.start_btn.setEnabled(False)
        
        main_layout.addWidget(control_panel)

    def _load_configuration(self):
        """Load saved configuration."""
        try:
            config = self.config_manager.load_config()
            if config:
                self._apply_config_to_ui(config)
        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Ошибка при загрузке конфигурации: {str(e)}")

    def _start_background_monitor(self):
        """Start the background image monitor."""
        try:
            config = self.config_manager.get_config()
            self.background_monitor = BackgroundMonitor(config)
            self.background_monitor.start()
        except Exception as e:
            print(f"Error starting background monitor: {str(e)}")

    def select_folder_image(self, folder_idx: int):
        """
        Select image for a specific folder.
        
        Args:
            folder_idx: Index of the folder
        """
        config = self.config_manager.get_config()
        input_folder = os.path.join(config.base_dir, f"input_{folder_idx}")
        os.makedirs(input_folder, exist_ok=True)
        
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            f"Выберите изображение для папки {folder_idx}",
            input_folder,
            "Images (*.png *.jpg *.jpeg)"
        )
        
        if file_name:
            self._update_folder_selection(folder_idx, file_name)

    def _update_folder_selection(self, folder_idx: int, file_name: str):
        """Update folder selection with new file."""
        widgets = self.folder_widgets[folder_idx]
        widgets['input_file'] = file_name
        widgets['file_label'].setText(os.path.basename(file_name))
        widgets['counter_label'].setText("Сгенерировано: 0")
        widgets['generated_count'] = 0
        widgets['clear_btn'].setVisible(True)
        
        self._update_start_button_state()

    def clear_folder_image(self, folder_idx: int):
        """
        Clear selected image for a folder.
        
        Args:
            folder_idx: Index of the folder
        """
        widgets = self.folder_widgets[folder_idx]
        widgets['input_file'] = None
        widgets['file_label'].setText("Файл не выбран")
        widgets['clear_btn'].setVisible(False)
        
        self._update_start_button_state()

    def _update_start_button_state(self):
        """Update the state of the start button based on selections."""
        config = self.config_manager.get_config()
        
        if getattr(config, 'overlay_only_mode', False):
            # In overlay_only_mode, check only for active folders
            can_start = any(
                widgets['checkbox'].isChecked() 
                for widgets in self.folder_widgets.values()
            )
        else:
            # In normal mode, require input file
            can_start = any(
                widgets['checkbox'].isChecked() and widgets['input_file'] is not None
                for widgets in self.folder_widgets.values()
            )
        
        self.start_btn.setEnabled(can_start)

    def toggle_num_images(self, state: int):
        """
        Toggle number of images spinbox based on infinite generation checkbox.

        Args:
            state: Checkbox state
        """
        self.num_images_spin.setEnabled(not bool(state))

    def start_processing(self):
        """Start image processing."""
        try:
            # Get folder data
            folder_data = self._get_folder_data()
            if not folder_data:
                QMessageBox.warning(self, "Предупреждение", "Нет активных папок для обработки")
                return

            # Update configuration from UI
            self._update_config_from_ui()

            # Create and start processing thread
            config = self.config_manager.get_config()
            num_images = self.num_images_spin.value()
            infinite = self.infinite_generation.isChecked()

            self.processing_thread = ImageProcessingThread(
                config, folder_data, num_images, infinite
            )

            # Connect signals
            self.processing_thread.finished.connect(self._on_processing_finished)
            self.processing_thread.error.connect(self._on_processing_error)
            self.processing_thread.image_generated.connect(self._on_image_generated)
            self.processing_thread.backgrounds_depleted.connect(self._on_backgrounds_depleted)

            # Update UI state
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)

            # Start processing
            self.processing_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Ошибка при запуске обработки: {str(e)}")

    def stop_processing(self):
        """Stop image processing."""
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.stop()
            self.processing_thread.wait(5000)  # Wait up to 5 seconds

        self._reset_ui_state()

    def save_config(self):
        """Save current configuration."""
        try:
            self._update_config_from_ui()
            preset_name = self.preset_name_field.text() or "Стандартный пресет"

            if self.config_manager.save_config(preset_name):
                QMessageBox.information(
                    self, "Успех",
                    f"Пресет '{preset_name}' успешно сохранен"
                )
            else:
                QMessageBox.critical(self, "Ошибка", "Не удалось сохранить конфигурацию")

        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Ошибка при сохранении: {str(e)}")

    def export_config(self):
        """Export configuration to file."""
        # Implementation for config export
        pass

    def import_config(self):
        """Import configuration from file."""
        # Implementation for config import
        pass

    def generate_preview(self):
        """Generate preview image."""
        # Implementation for preview generation
        pass

    def show_video_dialog(self):
        """Show video creation dialog."""
        dialog = VideoDialog(self)
        if dialog.exec() == dialog.DialogCode.Accepted:
            selected_folders = dialog.get_selected_folders()
            video_count = dialog.get_videos_count()
            photo_duration = dialog.get_photo_duration()

            # Process video creation
            self._create_videos(selected_folders, video_count, photo_duration)

    def _get_folder_data(self) -> Dict[int, Dict[str, Any]]:
        """Get folder data for processing."""
        folder_data = {}
        for idx, widgets in self.folder_widgets.items():
            if widgets['checkbox'].isChecked():
                folder_data[idx] = {
                    'active': True,
                    'input_file': widgets['input_file']
                }
        return folder_data

    def _update_config_from_ui(self):
        """Update configuration object from UI values."""
        config = self.config_manager.get_config()
        if config:
            # Update preset name
            config.preset_name = self.preset_name_field.text()

            # Update effect settings from groups
            for group_name, group_widget in self.groups.items():
                if hasattr(config, f"{group_name}_enabled"):
                    setattr(config, f"{group_name}_enabled", group_widget.isChecked())

    def _apply_config_to_ui(self, config_dict: Dict[str, Any]):
        """Apply configuration to UI elements."""
        # Set preset name
        if 'preset_name' in config_dict:
            self.preset_name_field.setText(config_dict['preset_name'])

        # Apply effect group settings
        for group_name, group_widget in self.groups.items():
            enabled_key = f"{group_name}_enabled"
            if enabled_key in config_dict:
                group_widget.setChecked(config_dict[enabled_key])

    def _reset_ui_state(self):
        """Reset UI to initial state."""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

    def _on_processing_finished(self):
        """Handle processing finished signal."""
        self._reset_ui_state()
        QMessageBox.information(self, "Завершено", "Обработка изображений завершена")

    def _on_processing_error(self, error_message: str):
        """Handle processing error signal."""
        self._reset_ui_state()
        QMessageBox.critical(self, "Ошибка", f"Ошибка обработки: {error_message}")

    def _on_image_generated(self, folder_idx: int, count: int):
        """Handle image generated signal."""
        if folder_idx in self.folder_widgets:
            widgets = self.folder_widgets[folder_idx]
            widgets['counter_label'].setText(f"Сгенерировано: {count}")
            widgets['generated_count'] = count

    def _on_backgrounds_depleted(self):
        """Handle backgrounds depleted signal."""
        self._reset_ui_state()
        QMessageBox.warning(
            self, "Предупреждение",
            "Закончились фоновые изображения. Обработка остановлена."
        )

    def _create_videos(self, selected_folders: list, video_count: int, photo_duration: int):
        """Create videos from selected folders."""
        # Implementation for video creation
        QMessageBox.information(
            self, "Информация",
            f"Создание {video_count} видео из папок {selected_folders} "
            f"с длительностью {photo_duration} сек на фото"
        )

    def closeEvent(self, event):
        """Handle application close event."""
        # Stop background monitor
        if self.background_monitor:
            self.background_monitor.stop()

        # Stop processing thread
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.stop()
            self.processing_thread.wait(3000)

        event.accept()
