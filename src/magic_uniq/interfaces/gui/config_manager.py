"""
Configuration management for the GUI application.

This module handles loading, saving, and managing application configurations.
"""

import os
import json
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import QMessageBox


class ConfigManager:
    """
    Manages application configuration.
    
    This class handles loading and saving configuration files,
    validation, and providing configuration data to other components.
    """
    
    def __init__(self, base_dir: str = None):
        """
        Initialize the configuration manager.
        
        Args:
            base_dir: Base directory for configuration files
        """
        self.base_dir = base_dir or os.getcwd()
        self.config_dir = os.path.join(self.base_dir, "Конфигурации")
        self.config_file = os.path.join(self.base_dir, "config.json")
        self._config: Optional[Any] = None
        
        # Ensure directories exist
        os.makedirs(self.config_dir, exist_ok=True)

    def get_config(self) -> Any:
        """
        Get the current configuration object.
        
        Returns:
            Configuration object
        """
        if self._config is None:
            self._load_default_config()
        return self._config

    def load_config(self, config_name: str = None) -> Optional[Dict[str, Any]]:
        """
        Load configuration from file.
        
        Args:
            config_name: Name of the configuration to load (optional)
            
        Returns:
            Configuration dictionary or None if loading failed
        """
        try:
            if config_name:
                config_path = os.path.join(self.config_dir, f"{config_name}.json")
            else:
                config_path = self.config_file
            
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                
                # Validate and apply configuration
                if self._validate_config(config_dict):
                    self._apply_config(config_dict)
                    return config_dict
                else:
                    print("Configuration validation failed, using defaults")
                    self._load_default_config()
            else:
                print(f"Configuration file not found: {config_path}")
                self._load_default_config()
                
        except Exception as e:
            print(f"Error loading configuration: {str(e)}")
            self._load_default_config()
        
        return None

    def save_config(self, config_name: str = None, config_dict: Dict[str, Any] = None) -> bool:
        """
        Save configuration to file.
        
        Args:
            config_name: Name for the configuration file
            config_dict: Configuration dictionary to save
            
        Returns:
            True if save was successful, False otherwise
        """
        try:
            if config_dict is None:
                config_dict = self._config.to_dict() if self._config else {}
            
            if config_name:
                config_path = os.path.join(self.config_dir, f"{config_name}.json")
            else:
                config_path = self.config_file
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"Error saving configuration: {str(e)}")
            return False

    def export_config(self, file_path: str) -> bool:
        """
        Export current configuration to a specific file.
        
        Args:
            file_path: Path where to export the configuration
            
        Returns:
            True if export was successful, False otherwise
        """
        try:
            config_dict = self._config.to_dict() if self._config else {}
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"Error exporting configuration: {str(e)}")
            return False

    def import_config(self, file_path: str) -> bool:
        """
        Import configuration from a specific file.
        
        Args:
            file_path: Path to the configuration file to import
            
        Returns:
            True if import was successful, False otherwise
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            
            if self._validate_config(config_dict):
                self._apply_config(config_dict)
                return True
            else:
                print("Imported configuration validation failed")
                return False
                
        except Exception as e:
            print(f"Error importing configuration: {str(e)}")
            return False

    def list_saved_configs(self) -> list:
        """
        List all saved configuration files.
        
        Returns:
            List of configuration file names (without extension)
        """
        try:
            configs = []
            if os.path.exists(self.config_dir):
                for file in os.listdir(self.config_dir):
                    if file.endswith('.json'):
                        configs.append(file[:-5])  # Remove .json extension
            return sorted(configs)
        except Exception as e:
            print(f"Error listing configurations: {str(e)}")
            return []

    def delete_config(self, config_name: str) -> bool:
        """
        Delete a saved configuration.
        
        Args:
            config_name: Name of the configuration to delete
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            config_path = os.path.join(self.config_dir, f"{config_name}.json")
            if os.path.exists(config_path):
                os.remove(config_path)
                return True
            return False
        except Exception as e:
            print(f"Error deleting configuration: {str(e)}")
            return False

    def _load_default_config(self):
        """Load default configuration."""
        try:
            # Import here to avoid circular imports
            from ...core.image_processor import UniqualizerConfig
            self._config = UniqualizerConfig()
        except ImportError as e:
            print(f"Error importing UniqualizerConfig: {str(e)}")
            self._config = None

    def _validate_config(self, config_dict: Dict[str, Any]) -> bool:
        """
        Validate configuration dictionary.
        
        Args:
            config_dict: Configuration dictionary to validate
            
        Returns:
            True if configuration is valid, False otherwise
        """
        # Basic validation - check for required attributes
        required_attributes = [
            'rgb_shift_enabled', 'rgb_shift_range', 'rgb_shift_opacity',
            'lines_enabled', 'lines_count_range', 'line_thickness_range',
            'emoji_enabled', 'emoji_count_range', 'emoji_size_range',
            'background_enabled', 'blur_range', 'background_opacity_range'
        ]
        
        for attr in required_attributes:
            if attr not in config_dict:
                print(f"Missing required attribute: {attr}")
                return False
        
        return True

    def _apply_config(self, config_dict: Dict[str, Any]):
        """
        Apply configuration dictionary to the config object.
        
        Args:
            config_dict: Configuration dictionary to apply
        """
        try:
            if self._config is None:
                self._load_default_config()
            
            if self._config and hasattr(self._config, 'from_dict'):
                self._config.from_dict(config_dict)
        except Exception as e:
            print(f"Error applying configuration: {str(e)}")

    def get_config_path(self, config_name: str = None) -> str:
        """
        Get the path for a configuration file.
        
        Args:
            config_name: Name of the configuration
            
        Returns:
            Path to the configuration file
        """
        if config_name:
            return os.path.join(self.config_dir, f"{config_name}.json")
        else:
            return self.config_file
