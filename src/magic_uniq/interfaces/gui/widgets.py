"""
Custom widgets for the GUI interface.

This module contains reusable custom widgets used throughout the application.
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, 
    QDoubleSpinBox, QGroupBox
)
from PyQt6.QtGui import <PERSON><PERSON>ont
from PyQt6.QtCore import Qt
from typing import Tuple, Union


class RangeSpinBox(QWidget):
    """
    A widget that provides two spinboxes for setting a range of values.
    
    This widget creates a min/max range selector with appropriate labels
    and validation.
    """
    
    def __init__(self, title: str, min_value: Union[int, float], max_value: Union[int, float], 
                 default_min: Union[int, float], default_max: Union[int, float],
                 decimals: int = 0, parent: QWidget = None):
        """
        Initialize the RangeSpinBox widget.
        
        Args:
            title: Display title for the widget
            min_value: Minimum allowed value
            max_value: Maximum allowed value
            default_min: Default minimum value
            default_max: Default maximum value
            decimals: Number of decimal places (0 for integer spinboxes)
            parent: Parent widget
        """
        super().__init__(parent)
        self._setup_ui(title, min_value, max_value, default_min, default_max, decimals)
    
    def _setup_ui(self, title: str, min_value: Union[int, float], max_value: Union[int, float],
                  default_min: Union[int, float], default_max: Union[int, float], decimals: int):
        """Set up the user interface."""
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Title label
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 9))
        layout.addWidget(title_label)

        # Range controls layout
        range_layout = QHBoxLayout()

        # Create appropriate spinbox type based on decimals
        if decimals > 0:
            self.min_spin = QDoubleSpinBox()
            self.max_spin = QDoubleSpinBox()
            self.min_spin.setDecimals(decimals)
            self.max_spin.setDecimals(decimals)
        else:
            self.min_spin = QSpinBox()
            self.max_spin = QSpinBox()

        # Configure spinboxes
        for spin in [self.min_spin, self.max_spin]:
            spin.setMinimum(min_value)
            spin.setMaximum(max_value)
            spin.setFixedWidth(70)

        self.min_spin.setValue(default_min)
        self.max_spin.setValue(default_max)

        # Add widgets to layout
        range_layout.addWidget(QLabel("Мин:"))
        range_layout.addWidget(self.min_spin)
        range_layout.addWidget(QLabel("Макс:"))
        range_layout.addWidget(self.max_spin)
        range_layout.addStretch()

        layout.addLayout(range_layout)

    def get_range(self) -> Tuple[Union[int, float], Union[int, float]]:
        """
        Get the current range values.
        
        Returns:
            Tuple of (min_value, max_value)
        """
        return (self.min_spin.value(), self.max_spin.value())
    
    def set_range(self, min_value: Union[int, float], max_value: Union[int, float]):
        """
        Set the range values.
        
        Args:
            min_value: Minimum value to set
            max_value: Maximum value to set
        """
        self.min_spin.setValue(min_value)
        self.max_spin.setValue(max_value)


class HaltonRangeSpinBox(RangeSpinBox):
    """
    A specialized RangeSpinBox with validation for Halton noise parameters.
    
    This widget ensures that the minimum value is always less than the maximum value.
    """
    
    def __init__(self, title: str, min_value: Union[int, float], max_value: Union[int, float],
                 default_min: Union[int, float], default_max: Union[int, float],
                 decimals: int = 0, parent: QWidget = None):
        """Initialize the HaltonRangeSpinBox with validation."""
        super().__init__(title, min_value, max_value, default_min, default_max, decimals, parent)
        
        # Add validation handlers
        self.min_spin.valueChanged.connect(self._on_min_changed)
        self.max_spin.valueChanged.connect(self._on_max_changed)

    def _on_min_changed(self, value: Union[int, float]):
        """Handle minimum value change with validation."""
        if value >= self.max_spin.value():
            self.max_spin.setValue(value + 1)

    def _on_max_changed(self, value: Union[int, float]):
        """Handle maximum value change with validation."""
        if value <= self.min_spin.value():
            self.min_spin.setValue(value - 1)


class EffectGroup(QGroupBox):
    """
    A styled group box for effect controls.
    
    This widget provides a consistent look and feel for effect parameter groups
    with checkable functionality.
    """
    
    def __init__(self, title: str, parent: QWidget = None):
        """
        Initialize the EffectGroup.
        
        Args:
            title: Title for the group box
            parent: Parent widget
        """
        super().__init__(title, parent)
        self._setup_ui()
    
    def _setup_ui(self):
        """Set up the user interface and styling."""
        self.setCheckable(True)
        self.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))

        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        # Apply consistent styling
        self.setStyleSheet("""
            QGroupBox {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding: 10px;
            }
            QGroupBox::title {
                color: #333333;
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """)
    
    def add_widget(self, widget: QWidget):
        """
        Add a widget to the effect group.
        
        Args:
            widget: Widget to add to the group
        """
        self.layout.addWidget(widget)
    
    def add_widgets(self, widgets: list):
        """
        Add multiple widgets to the effect group.
        
        Args:
            widgets: List of widgets to add
        """
        for widget in widgets:
            self.add_widget(widget)
