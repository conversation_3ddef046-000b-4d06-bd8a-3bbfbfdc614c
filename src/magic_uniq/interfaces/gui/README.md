# GUI Module Refactoring

Этот модуль был полностью рефакторен для улучшения структуры кода и соблюдения принципов ООП.

## Структура модулей

### Основные модули

- **`main.py`** - Чистая точка входа в приложение
- **`main_window.py`** - Основное окно приложения
- **`widgets.py`** - Пользовательские виджеты
- **`dialogs.py`** - Диалоговые окна
- **`ui_builder.py`** - Построитель интерфейса
- **`config_manager.py`** - Управление конфигурацией

### Вспомогательные модули

- **`processing_thread.py`** - Поток обработки изображений
- **`background_monitor.py`** - Мониторинг фоновых изображений
- **`system_utils.py`** - Системные утилиты

### Архивные файлы

- **`main_old.py`** - Оригинальный монолитный файл (3121 строка)

## Улучшения

### 1. Разделение ответственности (SRP)
- Каждый класс теперь имеет одну четко определенную ответственность
- Системные утилиты отделены от GUI логики
- Конфигурация отделена от бизнес-логики

### 2. Принцип открытости/закрытости (OCP)
- Виджеты можно легко расширять без изменения существующего кода
- Новые диалоги можно добавлять через наследование

### 3. Принцип инверсии зависимостей (DIP)
- Основное окно зависит от абстракций (ConfigManager, UIBuilder)
- Легко тестировать и заменять компоненты

### 4. Улучшенная структура
- Четкое разделение UI и бизнес-логики
- Централизованное управление конфигурацией
- Асинхронная обработка изображений

## Использование

```python
from src.magic_uniq.interfaces.gui.main import main

if __name__ == "__main__":
    main()
```

## Компоненты

### UniqualizerApplication
Основной класс приложения, управляющий жизненным циклом.

### UniqualizerMainWindow
Главное окно приложения с разделенной логикой.

### ConfigManager
Управление загрузкой, сохранением и валидацией конфигураций.

### UIBuilder
Построение консистентных UI компонентов.

### Custom Widgets
- `RangeSpinBox` - Виджет для выбора диапазона значений
- `HaltonRangeSpinBox` - Специализированный виджет с валидацией
- `EffectGroup` - Группа эффектов с чекбоксом

### Processing Components
- `ImageProcessingThread` - Асинхронная обработка изображений
- `BackgroundMonitor` - Автоматическая загрузка фонов

## Преимущества рефакторинга

1. **Читаемость**: Код стал более понятным и структурированным
2. **Тестируемость**: Каждый компонент можно тестировать отдельно
3. **Расширяемость**: Легко добавлять новые функции
4. **Поддерживаемость**: Изменения в одном компоненте не влияют на другие
5. **Переиспользование**: Компоненты можно использовать в других частях приложения
