"""
Dialog windows for the GUI interface.

This module contains various dialog windows used in the application.
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QCheckBox, 
    QPushButton, QSpinBox, QFrame
)
from PyQt6.QtGui import QFont
from PyQt6.QtCore import Qt
from typing import List


class VideoDialog(QDialog):
    """
    Dialog for selecting video creation parameters.
    
    This dialog allows users to select folders, set video duration,
    and specify the number of videos to create.
    """
    
    def __init__(self, parent=None):
        """
        Initialize the video dialog.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.selected_folders = []
        self._setup_ui()
        self._setup_default_selections()

    def _setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("Создание видео")
        self.setMinimumWidth(400)
        self._apply_styles()
        
        # Main layout
        layout = QVBoxLayout(self)
        
        # Add components
        self._add_title(layout)
        self._add_folder_selection(layout)
        self._add_duration_controls(layout)
        self._add_video_count_controls(layout)
        self._add_info_text(layout)
        self._add_buttons(layout)

    def _apply_styles(self):
        """Apply consistent styling to the dialog."""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QCheckBox {
                font-size: 11pt;
            }
            QLabel {
                font-size: 11pt;
                color: #333;
            }
            QSpinBox {
                padding: 4px;
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: white;
            }
        """)

    def _add_title(self, layout: QVBoxLayout):
        """Add title to the dialog."""
        title = QLabel("Выберите папки с фотографиями:")
        title.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        layout.addWidget(title)

    def _add_folder_selection(self, layout: QVBoxLayout):
        """Add folder selection checkboxes."""
        self.folder_group = QFrame()
        self.folder_layout = QVBoxLayout(self.folder_group)
        
        self.folder_checkboxes = []
        for i in range(1, 4):
            checkbox = QCheckBox(f"Папка output_{i}")
            checkbox.stateChanged.connect(
                lambda state, folder=i: self._on_folder_toggled(state, folder)
            )
            self.folder_checkboxes.append(checkbox)
            self.folder_layout.addWidget(checkbox)
        
        layout.addWidget(self.folder_group)

    def _add_duration_controls(self, layout: QVBoxLayout):
        """Add duration control widgets."""
        duration_layout = QHBoxLayout()
        
        duration_label = QLabel("Длительность показа одной фотографии (сек):")
        duration_label.setFont(QFont("Segoe UI", 10))
        
        self.duration_spinbox = QSpinBox()
        self.duration_spinbox.setMinimum(1)
        self.duration_spinbox.setMaximum(10)
        self.duration_spinbox.setValue(1)
        self.duration_spinbox.setFixedWidth(80)
        
        duration_layout.addWidget(duration_label)
        duration_layout.addWidget(self.duration_spinbox)
        duration_layout.addStretch()
        
        layout.addLayout(duration_layout)

    def _add_video_count_controls(self, layout: QVBoxLayout):
        """Add video count control widgets."""
        videos_count_layout = QHBoxLayout()
        
        videos_count_label = QLabel("Количество видео:")
        videos_count_label.setFont(QFont("Segoe UI", 10))
        
        self.videos_count_spinbox = QSpinBox()
        self.videos_count_spinbox.setMinimum(1)
        self.videos_count_spinbox.setMaximum(500)
        self.videos_count_spinbox.setValue(1)
        self.videos_count_spinbox.setFixedWidth(80)
        
        videos_count_layout.addWidget(videos_count_label)
        videos_count_layout.addWidget(self.videos_count_spinbox)
        videos_count_layout.addStretch()
        
        layout.addLayout(videos_count_layout)

    def _add_info_text(self, layout: QVBoxLayout):
        """Add informational text."""
        info_label = QLabel(
            "Видео будет создано, взяв по одной фотографии из каждой выбранной папки.\n"
            "Использованные фотографии будут удалены после создания видео."
        )
        info_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(info_label)

    def _add_buttons(self, layout: QVBoxLayout):
        """Add dialog buttons."""
        button_layout = QHBoxLayout()
        
        create_button = QPushButton("Создать видео")
        create_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("Отмена")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet("""
            background-color: #f8f9fa;
            color: #333333;
            border: 1px solid #dddddd;
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(create_button)
        
        layout.addLayout(button_layout)

    def _setup_default_selections(self):
        """Set up default folder selections."""
        for i, checkbox in enumerate(self.folder_checkboxes, 1):
            checkbox.setChecked(True)
            self.selected_folders.append(i)

    def _on_folder_toggled(self, state: Qt.CheckState, folder: int):
        """
        Handle folder checkbox toggle.
        
        Args:
            state: Checkbox state
            folder: Folder number
        """
        if state == Qt.CheckState.Checked:
            if folder not in self.selected_folders:
                self.selected_folders.append(folder)
        else:
            if folder in self.selected_folders:
                self.selected_folders.remove(folder)

    def get_selected_folders(self) -> List[int]:
        """
        Get list of selected folders.
        
        Returns:
            List of selected folder numbers
        """
        return self.selected_folders.copy()

    def get_videos_count(self) -> int:
        """
        Get number of videos to create.
        
        Returns:
            Number of videos
        """
        return self.videos_count_spinbox.value()

    def get_photo_duration(self) -> int:
        """
        Get photo display duration in seconds.
        
        Returns:
            Duration in seconds
        """
        return self.duration_spinbox.value()

    def reset_selections(self):
        """Reset all selections to default values."""
        self.selected_folders.clear()
        for i, checkbox in enumerate(self.folder_checkboxes, 1):
            checkbox.setChecked(True)
            self.selected_folders.append(i)
        
        self.duration_spinbox.setValue(1)
        self.videos_count_spinbox.setValue(1)
