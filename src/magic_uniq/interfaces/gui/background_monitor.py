"""
Background image monitoring and downloading.

This module provides automatic background image management,
ensuring sufficient background images are available for processing.
"""

import os
import time
import threading
import asyncio
from typing import Any, Optional
import aiohttp
from .system_utils import SystemUtils


class BackgroundMonitor(threading.Thread):
    """
    Monitor and maintain background images.
    
    This class runs in a separate thread and automatically downloads
    background images when the count falls below the minimum threshold.
    """
    
    def __init__(self, config: Any, min_backgrounds: int = 100, check_interval: int = 5):
        """
        Initialize the background monitor.
        
        Args:
            config: Configuration object containing base directory
            min_backgrounds: Minimum number of backgrounds to maintain
            check_interval: Check interval in seconds
        """
        super().__init__()
        self.config = config
        self.daemon = True
        self.running = True
        self.min_backgrounds = min_backgrounds
        self.check_interval = check_interval
        self.token = SystemUtils.get_system_uuid()
        self.server_url = f'https://server2.magicuniq.space/get_image?token={self.token}'
        self.loop: Optional[asyncio.AbstractEventLoop] = None

    def run(self):
        """Main monitoring loop."""
        # Create new event loop for this thread
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

        while self.running:
            try:
                # Check background count
                bg_count = self._count_backgrounds()

                if bg_count < self.min_backgrounds:
                    needed = self.min_backgrounds - bg_count
                    print(f"Downloading {needed} background images...")
                    # Run async download through event loop
                    self.loop.run_until_complete(self._download_backgrounds(needed))

                time.sleep(self.check_interval)

            except Exception as e:
                print(f"Error in background monitor: {str(e)}")
                time.sleep(self.check_interval * 2)  # Increase interval on error

    async def _download_backgrounds(self, count: int):
        """
        Download background images asynchronously.
        
        Args:
            count: Number of backgrounds to download
        """
        background_dir = os.path.join(self.config.base_dir, "backgrounds")
        os.makedirs(background_dir, exist_ok=True)

        # Create connection pool with limits
        connector = aiohttp.TCPConnector(limit=10)
        async with aiohttp.ClientSession(connector=connector) as session:
            tasks = []
            for i in range(count):
                filename = f"bg_{int(time.time())}_{i}.jpg"
                filepath = os.path.join(background_dir, filename)
                tasks.append(self._download_image(session, filepath))

            # Execute downloads and handle results
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Count successful downloads
            successful = sum(1 for r in results if r is True)
            if successful < count:
                print(f"Warning: Downloaded only {successful} out of {count} backgrounds")

    async def _download_image(self, session: aiohttp.ClientSession, filepath: str) -> bool:
        """
        Download a single background image.
        
        Args:
            session: aiohttp session for downloading
            filepath: Path where to save the image
            
        Returns:
            bool: True if download was successful, False otherwise
        """
        try:
            async with session.get(self.server_url, timeout=30) as response:
                if response.status == 200:
                    data = await response.read()
                    if len(data) > 1000:  # Simple check for non-empty file
                        with open(filepath, 'wb') as f:
                            f.write(data)
                        return True
                    else:
                        print(f"Received file too small: {filepath}")
                        return False
                else:
                    print(f"Download error for {filepath}: status {response.status}")
                    return False
                    
        except asyncio.TimeoutError:
            print(f"Timeout downloading {filepath}")
            return False
        except Exception as e:
            print(f"Error downloading {filepath}: {str(e)}")
            return False

    def _count_backgrounds(self) -> int:
        """
        Count existing background images.
        
        Returns:
            int: Number of background images found
        """
        try:
            background_dir = os.path.join(self.config.base_dir, "backgrounds")
            if not os.path.exists(background_dir):
                os.makedirs(background_dir)
                return 0

            files = [f for f in os.listdir(background_dir)
                    if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
            return len(files)
            
        except Exception as e:
            print(f"Error counting backgrounds: {str(e)}")
            return 0

    def stop(self):
        """Stop the background monitor."""
        self.running = False
        if self.loop:
            # Safely request event loop stop
            try:
                self.loop.call_soon_threadsafe(self.loop.stop)
            except Exception as e:
                print(f"Error stopping event loop: {e}")

    def set_minimum_backgrounds(self, count: int):
        """
        Set the minimum number of backgrounds to maintain.
        
        Args:
            count: Minimum background count
        """
        self.min_backgrounds = max(1, count)

    def set_check_interval(self, interval: int):
        """
        Set the check interval.
        
        Args:
            interval: Check interval in seconds
        """
        self.check_interval = max(1, interval)

    def get_status(self) -> dict:
        """
        Get current monitor status.
        
        Returns:
            dict: Status information including background count and running state
        """
        return {
            'running': self.running,
            'background_count': self._count_backgrounds(),
            'min_backgrounds': self.min_backgrounds,
            'check_interval': self.check_interval
        }
