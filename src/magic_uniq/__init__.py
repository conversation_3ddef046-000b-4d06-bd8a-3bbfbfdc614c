"""
MagicUniq - Продвинутый инструмент уникализации видео и изображений.

Этот пакет предоставляет комплексную платформу для обработки и уникализации 
медиа-контента с помощью продвинутых эффектов, манипуляций с метаданными 
и систем наложения.
"""

__version__ = "0.2.0"
__author__ = "MagicUniq Team"
__email__ = "<EMAIL>"

# Условные импорты для избежания ошибок при отсутствии зависимостей
try:
    from .config.settings import Settings
    SETTINGS_AVAILABLE = True
except ImportError:
    Settings = None
    SETTINGS_AVAILABLE = False

try:
    from .utils import FileUtils
    UTILS_AVAILABLE = True
except ImportError:
    FileUtils = None
    UTILS_AVAILABLE = False

__all__ = [
    "__version__",
    "__author__",
    "__email__",
]

if SETTINGS_AVAILABLE:
    __all__.append("Settings")

if UTILS_AVAILABLE:
    __all__.append("FileUtils")
