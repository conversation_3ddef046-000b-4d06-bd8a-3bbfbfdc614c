"""
Утилиты для работы с изображениями.

Предоставляет вспомогательные функции для обработки изображений,
конвертации форматов и базовых операций.
"""

from typing import Tuple, Optional, Union
from pathlib import Path

try:
    from PIL import Image, ImageOps
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    Image = None
    ImageOps = None

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    np = None


class ImageUtils:
    """
    Утилиты для работы с изображениями.
    """
    
    @staticmethod
    def load_image(file_path: Union[str, Path]) -> Optional['Image.Image']:
        """
        Безопасно загрузить изображение.

        Args:
            file_path: Путь к файлу изображения

        Returns:
            Объект PIL Image или None если загрузка не удалась
        """
        if not PIL_AVAILABLE:
            raise ImportError("PIL (Pillow) не установлен")

        try:
            return Image.open(file_path)
        except Exception:
            return None
    
    @staticmethod
    def save_image(image: 'Image.Image', file_path: Union[str, Path],
                   quality: int = 95, optimize: bool = True) -> bool:
        """
        Безопасно сохранить изображение.
        
        Args:
            image: Изображение PIL
            file_path: Путь для сохранения
            quality: Качество JPEG (1-100)
            optimize: Оптимизировать размер файла
            
        Returns:
            True если сохранение прошло успешно
        """
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Определяем формат по расширению
            format_map = {
                '.jpg': 'JPEG',
                '.jpeg': 'JPEG', 
                '.png': 'PNG',
                '.bmp': 'BMP',
                '.tiff': 'TIFF',
                '.webp': 'WEBP'
            }
            
            format_name = format_map.get(file_path.suffix.lower(), 'JPEG')
            
            save_kwargs = {'optimize': optimize}
            if format_name == 'JPEG':
                save_kwargs['quality'] = quality
                # Конвертируем в RGB если нужно
                if image.mode in ('RGBA', 'LA', 'P'):
                    image = image.convert('RGB')
            
            image.save(file_path, format=format_name, **save_kwargs)
            return True
        except Exception:
            return False
    
    @staticmethod
    def resize_image(image: 'Image.Image', size: Tuple[int, int],
                    maintain_aspect: bool = True) -> 'Image.Image':
        """
        Изменить размер изображения.
        
        Args:
            image: Исходное изображение
            size: Новый размер (ширина, высота)
            maintain_aspect: Сохранять пропорции
            
        Returns:
            Изображение с новым размером
        """
        if maintain_aspect:
            return ImageOps.fit(image, size, Image.Resampling.LANCZOS)
        else:
            return image.resize(size, Image.Resampling.LANCZOS)
    
    @staticmethod
    def get_image_info(image: 'Image.Image') -> dict:
        """
        Получить информацию об изображении.
        
        Args:
            image: Изображение PIL
            
        Returns:
            Словарь с информацией об изображении
        """
        return {
            'size': image.size,
            'width': image.width,
            'height': image.height,
            'mode': image.mode,
            'format': image.format,
            'has_transparency': image.mode in ('RGBA', 'LA', 'P')
        }
    
    @staticmethod
    def convert_to_rgb(image: 'Image.Image') -> 'Image.Image':
        """
        Конвертировать изображение в RGB.
        
        Args:
            image: Исходное изображение
            
        Returns:
            Изображение в режиме RGB
        """
        if image.mode != 'RGB':
            if image.mode == 'RGBA':
                # Создаем белый фон для прозрачных изображений
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1])
                return background
            else:
                return image.convert('RGB')
        return image
    
    @staticmethod
    def image_to_numpy(image: 'Image.Image') -> 'np.ndarray':
        """
        Конвертировать PIL изображение в numpy array.
        
        Args:
            image: Изображение PIL
            
        Returns:
            Numpy array
        """
        return np.array(image)
    
    @staticmethod
    def numpy_to_image(array: 'np.ndarray') -> 'Image.Image':
        """
        Конвертировать numpy array в PIL изображение.
        
        Args:
            array: Numpy array
            
        Returns:
            Изображение PIL
        """
        # Убеждаемся что значения в правильном диапазоне
        if array.dtype != np.uint8:
            array = np.clip(array, 0, 255).astype(np.uint8)
        
        return Image.fromarray(array)
    
    @staticmethod
    def create_thumbnail(image: 'Image.Image', size: Tuple[int, int]) -> 'Image.Image':
        """
        Создать миниатюру изображения.
        
        Args:
            image: Исходное изображение
            size: Размер миниатюры
            
        Returns:
            Миниатюра изображения
        """
        thumbnail = image.copy()
        thumbnail.thumbnail(size, Image.Resampling.LANCZOS)
        return thumbnail
    
    @staticmethod
    def calculate_optimal_size(original_size: Tuple[int, int], 
                             max_size: int) -> Tuple[int, int]:
        """
        Вычислить оптимальный размер с сохранением пропорций.
        
        Args:
            original_size: Исходный размер (ширина, высота)
            max_size: Максимальный размер по большей стороне
            
        Returns:
            Новый размер (ширина, высота)
        """
        width, height = original_size
        
        if width <= max_size and height <= max_size:
            return original_size
        
        if width > height:
            new_width = max_size
            new_height = int(height * max_size / width)
        else:
            new_height = max_size
            new_width = int(width * max_size / height)
        
        return new_width, new_height
