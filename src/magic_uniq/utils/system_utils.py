"""
Системные утилиты для MagicUniq.

Содержит функции для работы с системной информацией.
"""

import platform
import subprocess
import uuid


def get_system_uuid():
    """
    Получает UUID системы кроссплатформенным способом.
    
    Returns:
        str: UUID системы
    """
    try:
        system = platform.system().lower()

        if system == "windows":
            # Windows PowerShell команда
            result = subprocess.check_output(
                'powershell "Get-CimInstance -Class Win32_ComputerSystemProduct | Select-Object -Property UUID"',
                shell=True
            ).decode()
            lines = result.split('\n')
            for line in lines:
                if line.strip() and not line.startswith('-') and 'UUID' not in line:
                    return line.strip()

        elif system == "darwin":  # macOS
            # Используем system_profiler для получения Hardware UUID
            result = subprocess.check_output(
                ["system_profiler", "SPHardwareDataType"],
                text=True
            )
            for line in result.split('\n'):
                if 'Hardware UUID' in line:
                    return line.split(':')[1].strip()

        elif system == "linux":
            # Linux - пробуем несколько способов
            try:
                # Способ 1: /sys/class/dmi/id/product_uuid
                with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                    return f.read().strip()
            except:
                try:
                    # Способ 2: dmidecode
                    result = subprocess.check_output(
                        ["sudo", "dmidecode", "-s", "system-uuid"],
                        text=True
                    )
                    return result.strip()
                except:
                    pass

        # Fallback: генерируем UUID на основе MAC адреса
        return str(uuid.uuid1())

    except Exception as e:
        print(f"Ошибка получения UUID системы: {e}")
        # Последний fallback: случайный UUID
        return str(uuid.uuid4())


def get_system_info():
    """
    Получает информацию о системе.
    
    Returns:
        dict: Словарь с информацией о системе
    """
    return {
        "platform": platform.system(),
        "platform_release": platform.release(),
        "platform_version": platform.version(),
        "architecture": platform.machine(),
        "processor": platform.processor(),
        "python_version": platform.python_version(),
        "uuid": get_system_uuid()
    }
