"""
Утилиты для работы с файлами.

Предоставляет вспомогательные функции для работы с файловой системой,
валидации файлов и управления путями.
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Union
import mimetypes


class FileUtils:
    """
    Утилиты для работы с файлами и директориями.
    """
    
    @staticmethod
    def ensure_directory(path: Union[str, Path]) -> Path:
        """
        Убедиться что директория существует, создать если нет.
        
        Args:
            path: Путь к директории
            
        Returns:
            Путь к директории
        """
        directory = Path(path)
        directory.mkdir(parents=True, exist_ok=True)
        return directory
    
    @staticmethod
    def is_image_file(file_path: Union[str, Path]) -> bool:
        """
        Проверить является ли файл изображением.
        
        Args:
            file_path: Путь к файлу
            
        Returns:
            True если файл является изображением
        """
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type is not None and mime_type.startswith('image/')
    
    @staticmethod
    def is_video_file(file_path: Union[str, Path]) -> bool:
        """
        Проверить является ли файл видео.
        
        Args:
            file_path: Путь к файлу
            
        Returns:
            True если файл является видео
        """
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type is not None and mime_type.startswith('video/')
    
    @staticmethod
    def get_files_by_extension(directory: Union[str, Path], 
                              extensions: List[str]) -> List[Path]:
        """
        Получить список файлов с определенными расширениями.
        
        Args:
            directory: Директория для поиска
            extensions: Список расширений (например, ['.jpg', '.png'])
            
        Returns:
            Список путей к файлам
        """
        directory = Path(directory)
        if not directory.exists():
            return []
        
        files = []
        for ext in extensions:
            files.extend(directory.glob(f"*{ext}"))
            files.extend(directory.glob(f"*{ext.upper()}"))
        
        return sorted(files)
    
    @staticmethod
    def safe_filename(filename: str) -> str:
        """
        Создать безопасное имя файла, удалив недопустимые символы.
        
        Args:
            filename: Исходное имя файла
            
        Returns:
            Безопасное имя файла
        """
        # Удаляем недопустимые символы
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Ограничиваем длину
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename
    
    @staticmethod
    def copy_file_safe(src: Union[str, Path], dst: Union[str, Path]) -> bool:
        """
        Безопасно скопировать файл.
        
        Args:
            src: Исходный файл
            dst: Целевой файл
            
        Returns:
            True если копирование прошло успешно
        """
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                return False
            
            # Создаем директорию если нужно
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(src_path, dst_path)
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """
        Получить размер файла в байтах.
        
        Args:
            file_path: Путь к файлу
            
        Returns:
            Размер файла в байтах, -1 если файл не существует
        """
        try:
            return Path(file_path).stat().st_size
        except (OSError, FileNotFoundError):
            return -1
    
    @staticmethod
    def cleanup_temp_files(temp_dir: Union[str, Path], 
                          pattern: str = "*") -> int:
        """
        Очистить временные файлы.
        
        Args:
            temp_dir: Директория с временными файлами
            pattern: Паттерн для поиска файлов
            
        Returns:
            Количество удаленных файлов
        """
        temp_path = Path(temp_dir)
        if not temp_path.exists():
            return 0
        
        count = 0
        for file_path in temp_path.glob(pattern):
            try:
                if file_path.is_file():
                    file_path.unlink()
                    count += 1
            except OSError:
                continue
        
        return count
