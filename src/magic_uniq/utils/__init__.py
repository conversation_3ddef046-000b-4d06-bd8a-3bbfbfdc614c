"""
Утилиты MagicUniq.

Содержит вспомогательные функции и классы
для работы с файлами, изображениями и другими ресурсами.
"""

from .file_utils import FileUtils
from .system_utils import get_system_uuid

# Условный импорт image_utils (требует PIL)
try:
    from .image_utils import ImageUtils
    IMAGE_UTILS_AVAILABLE = True
except ImportError:
    ImageUtils = None
    IMAGE_UTILS_AVAILABLE = False

__all__ = [
    "FileUtils",
    "get_system_uuid",
]

if IMAGE_UTILS_AVAILABLE:
    __all__.append("ImageUtils")
