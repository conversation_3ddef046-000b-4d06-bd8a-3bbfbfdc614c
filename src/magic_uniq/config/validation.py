"""
Валидация конфигурации MagicUniq.

Предоставляет функции для проверки корректности настроек
и параметров конфигурации.
"""

from typing import Dict, Any, List, Optional, Union
import os
from pathlib import Path


class ConfigValidator:
    """
    Валидатор конфигурации MagicUniq.
    
    Проверяет корректность настроек и параметров конфигурации.
    """
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Валидировать полную конфигурацию.
        
        Args:
            config: Словарь с конфигурацией
            
        Returns:
            True если конфигурация валидна
        """
        self.errors.clear()
        self.warnings.clear()
        
        # Проверяем основные секции
        self._validate_directories(config.get('directories', {}))
        self._validate_image_processing(config.get('image_processing', {}))
        self._validate_video_processing(config.get('video_processing', {}))
        self._validate_effects(config.get('effects', {}))
        self._validate_metadata(config.get('metadata', {}))
        self._validate_server(config.get('server', {}))
        
        return len(self.errors) == 0
    
    def _validate_directories(self, directories: Dict[str, Any]) -> None:
        """Валидировать настройки директорий."""
        required_dirs = ['input', 'output', 'resources', 'configs']
        
        for dir_name in required_dirs:
            if dir_name not in directories:
                self.errors.append(f"Отсутствует обязательная директория: {dir_name}")
                continue
            
            dir_path = directories[dir_name]
            if not isinstance(dir_path, str):
                self.errors.append(f"Путь к директории {dir_name} должен быть строкой")
                continue
            
            # Проверяем что путь не пустой
            if not dir_path.strip():
                self.errors.append(f"Путь к директории {dir_name} не может быть пустым")
    
    def _validate_image_processing(self, image_config: Dict[str, Any]) -> None:
        """Валидировать настройки обработки изображений."""
        # Проверяем максимальный размер
        max_size = image_config.get('max_size')
        if max_size is not None:
            if not isinstance(max_size, int) or max_size <= 0:
                self.errors.append("max_size должен быть положительным целым числом")
        
        # Проверяем качество
        quality = image_config.get('quality')
        if quality is not None:
            if not isinstance(quality, int) or not (1 <= quality <= 100):
                self.errors.append("quality должен быть целым числом от 1 до 100")
        
        # Проверяем формат
        format_name = image_config.get('format')
        if format_name is not None:
            valid_formats = ['JPEG', 'PNG', 'BMP', 'TIFF', 'WEBP']
            if format_name not in valid_formats:
                self.errors.append(f"Неподдерживаемый формат: {format_name}. "
                                 f"Поддерживаемые: {', '.join(valid_formats)}")
    
    def _validate_video_processing(self, video_config: Dict[str, Any]) -> None:
        """Валидировать настройки обработки видео."""
        # Проверяем разрешение
        resolution = video_config.get('max_resolution')
        if resolution is not None:
            if not isinstance(resolution, str):
                self.errors.append("max_resolution должен быть строкой")
            elif 'x' not in resolution:
                self.errors.append("max_resolution должен быть в формате 'ширинаxвысота'")
            else:
                try:
                    width, height = resolution.split('x')
                    width, height = int(width), int(height)
                    if width <= 0 or height <= 0:
                        self.errors.append("Разрешение должно содержать положительные числа")
                except ValueError:
                    self.errors.append("Неверный формат разрешения")
        
        # Проверяем FPS
        fps = video_config.get('fps')
        if fps is not None:
            if not isinstance(fps, (int, float)) or fps <= 0:
                self.errors.append("fps должен быть положительным числом")
        
        # Проверяем кодек
        codec = video_config.get('codec')
        if codec is not None:
            valid_codecs = ['h264', 'h265', 'vp9', 'av1']
            if codec not in valid_codecs:
                self.warnings.append(f"Кодек {codec} может быть не поддержан. "
                                   f"Рекомендуемые: {', '.join(valid_codecs)}")
    
    def _validate_effects(self, effects_config: Dict[str, Any]) -> None:
        """Валидировать настройки эффектов."""
        # Проверяем включение эффектов
        enabled = effects_config.get('enabled')
        if enabled is not None and not isinstance(enabled, bool):
            self.errors.append("effects.enabled должен быть булевым значением")
        
        # Проверяем интенсивность
        intensity = effects_config.get('intensity')
        if intensity is not None:
            if not isinstance(intensity, (int, float)) or not (0 <= intensity <= 1):
                self.errors.append("effects.intensity должен быть числом от 0 до 1")
    
    def _validate_metadata(self, metadata_config: Dict[str, Any]) -> None:
        """Валидировать настройки метаданных."""
        # Проверяем булевые значения
        for key in ['remove_original', 'add_custom']:
            value = metadata_config.get(key)
            if value is not None and not isinstance(value, bool):
                self.errors.append(f"metadata.{key} должен быть булевым значением")
    
    def _validate_server(self, server_config: Dict[str, Any]) -> None:
        """Валидировать настройки сервера."""
        # Проверяем хост
        host = server_config.get('host')
        if host is not None and not isinstance(host, str):
            self.errors.append("server.host должен быть строкой")
        
        # Проверяем порт
        port = server_config.get('port')
        if port is not None:
            if not isinstance(port, int) or not (1 <= port <= 65535):
                self.errors.append("server.port должен быть целым числом от 1 до 65535")
        
        # Проверяем debug
        debug = server_config.get('debug')
        if debug is not None and not isinstance(debug, bool):
            self.errors.append("server.debug должен быть булевым значением")
    
    def get_errors(self) -> List[str]:
        """Получить список ошибок валидации."""
        return self.errors.copy()
    
    def get_warnings(self) -> List[str]:
        """Получить список предупреждений валидации."""
        return self.warnings.copy()
    
    def has_errors(self) -> bool:
        """Проверить наличие ошибок."""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """Проверить наличие предупреждений."""
        return len(self.warnings) > 0
