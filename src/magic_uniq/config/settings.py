"""
Централизованные настройки для MagicUniq.
"""

import os
import json
from typing import Dict, Any, Optional
from pathlib import Path


class Settings:
    """Класс для управления настройками MagicUniq."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.base_dir = Path.cwd() / "data"
        # Создаем базовую директорию data, если она не существует
        self.base_dir.mkdir(exist_ok=True)
        self.config_path = config_path or self.base_dir / "configs" / "config.json"
        
        # Настройки по умолчанию
        self._defaults = {
            "directories": {
                "input": "input_images",
                "output": "output_images",
                "backgrounds": "backgrounds",
                "overlays": "overlay_images",
                "emojis": "emojis",
                "resources": "src/resources",
                "configs": "configs",
                "temp": "temp",
                "video_output": "output_videos"
            },
            "image_processing": {
                "max_size": 4096,
                "quality": 95,
                "format": "JPEG"
            },
            "video_processing": {
                "max_resolution": "1920x1080",
                "fps": 30,
                "codec": "h264"
            },
            "effects": {
                "enabled": True,
                "intensity": 0.5
            },
            "metadata": {
                "remove_original": True,
                "add_custom": True
            },
            "server": {
                "host": "localhost",
                "port": 5000,
                "debug": False
            }
        }
        
        self._settings = self._defaults.copy()
        self.load_config()
    
    def load_config(self) -> None:
        """Загрузить конфигурацию из файла."""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self._merge_config(config)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Ошибка загрузки конфигурации: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Получить значение настройки."""
        keys = key.split('.')
        value = self._settings
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Установить значение настройки."""
        keys = key.split('.')
        current = self._settings
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
    
    def get_directory(self, name: str) -> Path:
        """Получить путь к директории."""
        dir_path = self.get(f"directories.{name}")
        if dir_path:
            path = Path(dir_path)
            if not path.is_absolute():
                path = self.base_dir / path
            return path

        return self.base_dir / name

    def get_backgrounds_dir(self) -> Path:
        """Получить путь к директории с фонами."""
        return self.get_directory("backgrounds")

    def get_overlays_dir(self) -> Path:
        """Получить путь к директории с оверлеями."""
        return self.get_directory("overlays")

    def get_emojis_dir(self) -> Path:
        """Получить путь к директории с эмодзи."""
        return self.get_directory("emojis")

    def get_input_dir(self) -> Path:
        """Получить путь к входной директории."""
        return self.get_directory("input")

    def get_output_dir(self) -> Path:
        """Получить путь к выходной директории."""
        return self.get_directory("output")

    def get_video_output_dir(self) -> Path:
        """Получить путь к директории для выходных видео."""
        return self.get_directory("video_output")

    def ensure_directories(self) -> None:
        """Создать все необходимые директории."""
        directories = [
            "input", "output", "backgrounds", "overlays",
            "emojis", "configs", "temp", "video_output"
        ]

        for dir_name in directories:
            dir_path = self.get_directory(dir_name)
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """Получить все настройки в виде словаря."""
        return self._settings.copy()
    
    def reset_to_defaults(self) -> None:
        """Сбросить настройки к значениям по умолчанию."""
        self._settings = self._defaults.copy()
    
    def save_config(self) -> None:
        """Сохранить текущую конфигурацию в файл."""
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._settings, f, indent=2, ensure_ascii=False)
        except IOError as e:
            print(f"Ошибка сохранения конфигурации: {e}")
    
    def _merge_config(self, config: Dict[str, Any]) -> None:
        """Объединить загруженную конфигурацию с настройками по умолчанию."""
        def merge_dict(base: Dict, update: Dict) -> Dict:
            result = base.copy()
            for key, value in update.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        self._settings = merge_dict(self._defaults, config)
