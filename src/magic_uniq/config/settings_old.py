# Unpacker Protection - Не трогать (удалено для совместимости)

class SettingsConfiguration:
    @staticmethod
    def get_default_settings():
        return {
                "scale": {
                    "enabled": False,
                    "controlType": "range",
                    "range": [0.0, 1.0],
                    "translation": "Масштаб",
                    "icon": "minimize-2"
                },
                "saturation": {
                    "enabled": False,
                    "controlType": "range",
                    "range": [1.0, 2.0],
                    "translation": "Насыщенность",
                    "icon": "droplet"
                },
                "transparency": {
                    "enabled": False,
                    "controlType": "range",
                    "range": [0.0, 1.0],
                    "translation": "Прозрачность",
                    "icon": "eye"
                },
                "gamma": {
                    "enabled": False,
                    "controlType": "range",
                    "range": [1.0, 3.0],
                    "translation": "Гамма",
                    "icon": "audio-waveform"
                },
                "brightness": {
                    "enabled": False,
                    "controlType": "range",
                    "range": [0, 2.0],
                    "translation": "Яркость",
                    "icon": "sun"
                },
                "contrast": {
                    "enabled": False,
                    "controlType": "range",
                    "range": [0, 2.0],
                    "translation": "Контраст",
                    "icon": "contrast"
                },

                # Motion Effects
            "movement": { # это индекс-название эффекта, будет сохраняться в uppercase в конфиг. нельзя использовать нижние подчеркивания
                "enabled": False, # это основной переключатель
                "controlType": "complex", # это тип параметра, бывают разные и под разные цели разные типы
                "icon": "move-3d", # это иконка, используется из иконок lucide
                "translation": "Движение", # перевод названия вкладки, будет отображаться в названии параметра в интерфейсе
                "controls": { # тут все параметры которые используются
                    "pattern": { # это выбор паттерна, можно выбрать один паттерн и он запишется в конфиг
                        "type": "select",
                        "options": ['circular', 'figure8', 'random', 'spiral_out', 'zigzag', 'combination'],
                        "translation": "Тип движения",
                        "translations": {
                            'circular': 'Круговое',
                            'figure8': 'Восьмёрка',
                            'random': 'Случайное',
                            'spiral_out': 'Спираль',
                            'zigzag': 'Зигзаг',
                            'combination': 'Комбинация'
                        }
                    },
                    "amplitude": { # это двойной слайдер, запишет в конфиг значение от X до Y
                        "type": "range",
                        "range": [0.0, 1.0],
                        "translation": "Амплитуда"
                    },
                    "speed": {
                        "type": "range",
                        "range": [0.0, 1.0],
                        "translation": "Скорость"
                    },
                    "complexity": {
                        "type": "range",
                        "range": [0.1, 2.5],
                        "translation": "Сложность"
                    }
                }
            },
            "backgroundscale": {
                "enabled": False,
                "controlType": "complex",
                "icon": "scaling",
                "translation": "Масштабирование фона",
                "controls": {
                    "start_scale": {
                        "type": "range",
                        "range": [1.0, 3.0],
                        "translation": "Начальный масштаб"
                    },
                    "end_scale": {
                        "type": "range",
                        "range": [1.0, 3.0],
                        "translation": "Конечный масштаб"
                    },
                    "speed": {
                        "type": "range",
                        "range": [1.0, 2.5],
                        "translation": "Скорость"
                    },
                    "pattern": {
                        "type": "select",
                        "options": ['linear', 'ease-in', 'ease-out', 'ease-in-out'],
                        "translation": "Паттерн анимации",
                        "translations": {
                            'linear': 'Линейный',
                            'ease-in': 'Плавное начало',
                            'ease-out': 'Плавное окончание',
                            'ease-in-out': 'Плавное начало и конец'
                        }
                    }
                }
            },

            # Special Effects
            "blur": {
                "enabled": False,
                "controlType": "range",
                "range": [0.05, 0.5],
                "translation": "Размытие",
                "icon": "circle-dashed",
                "processing_time": "long"
            },
            "vignette": {
                "enabled": False,
                "controlType": "complex",
                "icon": "circle-dot",
                "translation": "Виньетка",
                "processing_time": "long",
                "controls": {
                    "intensity": {
                        "type": "range",
                        "range": [0.1, 0.95],
                        "translation": "Интенсивность"
                    },
                    "radius": {
                        "type": "range",
                        "range": [0.1, 0.95],
                        "translation": "Радиус"
                    }
                }
            },
            "noise": {
                "enabled": False,
                "controlType": "range",
                "range": [0.01, 0.5],
                "translation": "Шум",
                "icon": "cloud-snow",
                "processing_time": "long"
            },
            "whitebalance": {
                "enabled": False,
                "controlType": "complex",
                "icon": "cloud-sun",
                "translation": "Баланс белого",
                "controls": {
                    "temperature": {
                        "type": "range",
                        "range": [1.0, 2.0],
                        "translation": "Температура"
                    },
                    "tint": {
                        "type": "range",
                        "range": [-0.01, 0.2],
                        "translation": "Оттенок"
                    }
                }
            },
            "snow": {
                "enabled": False,
                "controlType": "complex",
                "icon": "snowflake",
                "translation": "Снег",
                "processing_time": "long",
                "controls": {
                    "intensity": {
                        "type": "range",
                        "range": [0.1, 2.0],
                        "translation": "Интенсивность"
                    },
                    "size": {
                        "type": "range",
                        "range": [1, 15],
                        "translation": "Размер снежинок",
                        "integer": True
                    },
                    "opacity": {
                        "type": "range",
                        "range": [0.1, 1.0],
                        "translation": "Прозрачность"
                    },
                    "wind_strength": {
                        "type": "range",
                        "range": [0.1, 2.0],
                        "translation": "Сила ветра"
                    }
                }
            },
            "lines": {
                "enabled": False,
                "controlType": "complex",
                "icon": "shrink",
                "translation": "Линии",
                "processing_time": "long",
                "controls": {
                    "animated": {
                        "type": "toggle",
                        "translation": "Анимация линий"
                    },
                    "count": {
                        "type": "range",
                        "range": [1, 20],
                        "translation": "Количество линий",
                        "integer": True
                    },
                    "width": {
                        "type": "range",
                        "range": [1, 15],
                        "translation": "Толщина линий",
                        "integer": True
                    },
                    "opacity": {
                        "type": "range",
                        "range": [0.1, 1.001],
                        "translation": "Прозрачность"
                    },
                    "angle": {
                        "type": "range",
                        "range": [0, 360],
                        "translation": "Угол наклона",
                        "integer": True
                    },
                    "length": {
                        "type": "range",
                        "range": [0.1, 1.001],
                        "translation": "Длина линий"
                    },
                    "fade_speed": {
                        "type": "range",
                        "range": [0.001, 0.01],
                        "translation": "Скорость появления/исчезновения"
                    },
                    "min_delay": {
                        "type": "range",
                        "range": [5, 15],
                        "translation": "Минимальная задержка",
                        "integer": True
                    },
                    "max_delay": {
                        "type": "range",
                        "range": [150, 200],
                        "translation": "Максимальная задержка",
                        "integer": True
                    },
                    "transition_threshold": {
                        "type": "range",
                        "range": [0.9, 1],
                        "translation": "Порог перехода"
                    }
                }
            },
            "microdots": {
                "enabled": False,
                "controlType": "complex",
                "icon": "grip",
                "translation": "Микро-точки",
                "controls": {
                    "density": {
                        "type": "range",
                        "range": [0.0001, 0.10],
                        "translation": "Плотность"
                    },
                    "opacity": {
                        "type": "range",
                        "range": [0.001, 1.0],
                        "translation": "Прозрачность"
                    }
                }
            },
            "mosaic": {
                "enabled": False,
                "controlType": "complex",
                "icon": "grid-3x3",
                "translation": "Мозаика",
                "controls": {
                    "size": {
                        "type": "range",
                        "range": [1, 40],
                        "translation": "Размер",
                        "integer": True
                    },
                    "opacity": {
                        "type": "range",
                        "range": [0.1, 1.0],
                        "translation": "Прозрачность"
                    }
                }
            },
            "border": {
                "enabled": False,
                "controlType": "complex",
                "icon": "square",
                "translation": "Скругление углов",
                "controls": {
                    "radius_min": {
                        "type": "range",
                        "range": [5, 10],
                        "translation": "Минимальный радиус",
                        "integer": True
                    },
                    "radius_max": {
                        "type": "range",
                        "range": [150, 200],
                        "translation": "Максимальный радиус",
                        "integer": True
                    },
                    "radius_speed": {
                        "type": "range",
                        "range": [0.02, 0.7],
                        "translation": "Скорость изменения"
                    }
                }
            },
            "frame_removal": {
                "enabled": False,
                "controlType": "complex",
                "icon": "scissors",
                "translation": "Удаление кадров",
                "controls": {
                    "percentage": {
                        "type": "range",
                        "range": [1, 10],
                        "translation": "Диапазон удаления (%)",
                        "description": "Рандомное удаление кадров в выбранном диапазоне",
                        "integer": True
                    }
                }
            },
            "metadata": {
                "enabled": False,
                "controlType": "complex",
                "icon": "file-text",
                "translation": "Удалять метаданные",
                "controls": {
                    "generate": {
                        "type": "toggle",
                        "translation": "Генерировать новые метаданные"
                    },
                }
            },
            "png-overlay": {
                "enabled": False,
                "controlType": "complex",
                "icon": "image",
                "translation": "Наложение изображения",
                "controls": {
                    "opacity": {
                        "type": "range",
                        "range": [1, 100],
                        "translation": "Прозрачность (%)",
                        "integer": True
                    },
                    "scale": {
                        "type": "range",
                        "range": [100, 200],
                        "translation": "Масштаб (%)",
                        "integer": True
                    },
                    "delete-used": {
                        "type": "toggle",
                        "translation": "Удалять использованные"
                    }
                }
            },
            "wave-distortion": {
                "enabled": False,
                "controlType": "complex",
                "icon": "waves",
                "translation": "Волновые искажения",
                "controls": {
                    "amplitude": {
                        "type": "range",
                        "range": [1, 100],
                        "translation": "Амплитуда (%)",
                        "integer": True
                    },
                    "frequency": {
                        "type": "range",
                        "range": [1, 100],
                        "translation": "Частота (%)",
                        "integer": True
                    },
                    "speed": {
                        "type": "range",
                        "range": [1, 100],
                        "translation": "Скорость (%)",
                        "integer": True
                    }
                }
            },
            "emoji": {
                "enabled": False,
                "controlType": "complex",
                "icon": "smile",
                "translation": "Эмодзи",
                "controls": {
                    "count": {
                        "type": "range",
                        "range": [1, 30],
                        "translation": "Количество ",
                        "integer": True
                    },
                    "size": {
                        "type": "range",
                        "range": [1, 100],
                        "translation": "Размер (%)",
                        "integer": True
                    },
                    "speed": {
                        "type": "range",
                        "range": [1, 100],
                        "translation": "Скорость движения (%)",
                        "integer": True
                    },
                    "rotation_speed": {
                        "type": "range",
                        "range": [1, 100],
                        "translation": "Скорость вращения (%)",
                        "integer": True
                    },
                    "opacity": {
                        "type": "range",
                        "range": [1, 100],
                        "translation": "Непрозрачность (%)",
                        "integer": True
                    }
                }
            },
            "compression-level": {
                "enabled": False,
                "controlType": "complex",
                "icon": "package",
                "translation": "Сжатие видео",
                "controls": {
                    "level": {
                        "type": "select",
                        "options": ["none", "medium", "high"],
                        "translation": "Уровень сжатия",
                        "translations": {
                            "none": "Без сжатия",
                            "medium": "Среднее",
                            "high": "Сильное"
                        },
                        "description": "Выберите уровень сжатия видео. 'Без сжатия' сохраняет оригинальное качество."
                    }
                }
            },
            "video-overlay": {
                "enabled": False,
                "controlType": "complex",
                "icon": "video",
                "translation": "Наложение видео",
                "controls": {
                    "opacity": {
                        "type": "range",
                        "range": [1, 100],
                        "translation": "Прозрачность (%)",
                        "integer": True
                    },
                    "scale": {
                        "type": "range",
                        "range": [100, 200],
                        "translation": "Масштаб (%)",
                        "integer": True
                    },
                    "rotation": {
                        "type": "range",
                        "range": [-10, 10],
                        "translation": "Поворот (°)",
                        "integer": True
                    },
                    "delete-used": {
                        "type": "toggle",
                        "translation": "Удалять использованные"
                    }
                }
            },
            "frequency-shift": {
                "enabled": False,
                "controlType": "complex",
                "icon": "audio-lines",
                "translation": "Частотный сдвиг",
                "controls": {
                    "ammount": {
                        "type": "range",
                        "range": [0.5, 12.0],
                        "translation": "Величина"
                    },
                    "randomization": {
                        "type": "range",
                        "range": [0.1, 0.8],
                        "translation": "Рандомизация"
                    }
                }
            },

            "phase-shift": {
                "enabled": False,
                "controlType": "complex",
                "icon": "audio-waveform",
                "translation": "Фазовый сдвиг",
                "controls": {
                    "ammount": {
                        "type": "range",
                        "range": [1.2, 1.5],
                        "translation": "Величина"
                    },
                    "freq": {
                        "type": "range",
                        "range": [50, 15000],
                        "translation": "Частота"
                    }
                }
            },
            "micro-delay": {
                "enabled": False,
                "controlType": "complex",
                "icon": "waypoints",
                "translation": "Микро задержки",
                "time": {
                    "ammount": {
                        "type": "range",
                        "range": [1.0, 20.0],
                        "translation": "Время (мс)"
                    },
                    "feedback": {
                        "type": "range",
                        "range": [0.1, 0.8],
                        "translation": "Отдача"
                    }
                }
            },
            "stereo-effects": {
                "enabled": False,
                "controlType": "complex",
                "icon": "speaker",
                "translation": "Стерео эффекты",
                "controls": {
                    "width": {
                        "type": "range",
                        "range": [0.5, 2.0],
                        "translation": "Ширина"
                    },
                    "rotation": {
                        "type": "range",
                        "range": [-0.8, 0.8],
                        "translation": "Вращение"
                    }
                }
            },
            "compression": {
                "enabled": False,
                "controlType": "complex",
                "icon": "cloud-sun",
                "translation": "Компрессия",
                "controls": {
                    "threshold": {
                        "type": "range",
                        "range": [-40.0, 10.0],
                        "translation": "Порог"
                    },
                    "ratio": {
                        "type": "range",
                        "range": [1.2, 8.0],
                        "translation": "Соотношение"
                    },
                    "attack": {
                        "type": "range",
                        "range": [1.0, 50.0],
                        "translation": "Время атаки"
                    },
                    "release": {
                        "type": "range",
                        "range": [10.0, 500.0],
                        "translation": "Время восстановления"
                    },
                }
            },
            "audio-noice": {
                "enabled": False,
                "controlType": "complex",
                "icon": "scaling",
                "translation": "Шум",
                "controls": {
                    "intensity": {
                        "type": "range",
                        "range": [0.0001, 0.05],
                        "translation": "Интенсивность"
                    },
                    "type": {
                        "type": "select",
                        "options": ["white", "pink", "brown", "blue", "purple", "gray", "velvet"],
                        "translation": "Тип шума",
                        "translations": {
                            "white": "Белый",
                            "pink": "Розовый",
                            "brown": "Коричневый",
                            "blue": "Синий",
                            "purple": "Фиолетовый",
                            "gray": "Серый",
                            "velvet": "Бархатный"
                        }
                    }
                }
            },
            "puzzle": {
                "enabled": False,
                "controlType": "complex",
                "icon": "puzzle",
                "translation": "Видео-пазл",
                "processing_time": "long",
                "controls": {
                    "rows": {
                        "type": "range",
                        "range": [2, 30],
                        "translation": "Размер сетки",
                        "integer": True
                    },
                    "pattern": {
                        "type": "select",
                        "options": ['random', 'sequential', 'shift'],
                        "translation": "Паттерн отображения",
                        "translations": {
                            'random': 'Случайный',
                            'sequential': 'Последовательный',
                            'shift': 'Смещение'
                        }
                    },
                    "cycle_speed": {
                        "type": "range",
                        "range": [1, 30],
                        "translation": "Скорость появления",
                        "description": "Чем выше значение, тем медленнее появляются пазлы",
                        "integer": True
                    },
                    "visible_percent": {
                        "type": "range",
                        "range": [5, 60],
                        "translation": "Видимые части (%)",
                        "description": "Процент видимых частей пазла в каждом кадре",
                        "integer": True
                    }
                }
            },

        }

    @staticmethod
    def get_categories():
        return [
            {
                'id': 'basic',
                'label': 'Базовые эффекты',
                'icon': 'sliders',
                'items': ['scale', 'saturation', 'transparency', 'gamma', 'brightness', 'contrast']
            },
            {
                'id': 'color',
                'label': 'Цвет и свет',
                'icon': 'palette',
                'items': ['whitebalance', 'vignette']
            },
            {
                'id': 'motion',
                'label': 'Движение',
                'icon': 'move-3d',
                'items': ['movement', 'backgroundscale', "wave-distortion", "emoji"]
            },
            {
                'id': 'effects',
                'label': 'Эффекты',
                'icon': 'wand-2',
                'items': ['blur', 'noise', 'microdots', 'mosaic', 'puzzle', 'snow']
            },
            {
                'id': 'lines',
                'label': 'Линии',
                'icon': 'layout',
                'items': ['lines']
            },
            {
                'id': 'border',
                'label': 'Границы',
                'icon': 'square',
                'items': ['border']
            },
            {
                'id': 'advanced',
                'label': 'Дополнительно',
                'icon': 'settings-2',
                'items': ['frame_removal', 'metadata', 'png-overlay', "compression-level", "video-overlay"]
            },
            {
                'id': 'audio',
                'label': 'Звук',
                'icon': 'volume-2',
                'items': ['frequency-shift', 'phase-shift', 'stereo-effects', 'compression', 'audio-noice']
            },
        ]
