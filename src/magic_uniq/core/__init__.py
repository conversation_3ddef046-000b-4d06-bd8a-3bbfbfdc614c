"""
Основные компоненты обработки медиа-контента.

Этот модуль содержит базовую бизнес-логику для обработки изображений и видео,
включая применение эффектов и работу с метаданными.
"""

from .video_processor import VideoUniquifier, VideoEffectParams, EncodingParams

# Импортируем основные классы из image_processor
try:
    from .image_processor import (
        UniqualizerConfig,
        process_image,
        process_image_thread,
        NoBackgroundsError,
        create_slideshow_from_photos
    )
    IMAGE_PROCESSOR_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Image processor not available: {e}")
    IMAGE_PROCESSOR_AVAILABLE = False

__all__ = [
    "VideoUniquifier",
    "VideoEffectParams",
    "EncodingParams",
]

if IMAGE_PROCESSOR_AVAILABLE:
    __all__.extend([
        "UniqualizerConfig",
        "process_image",
        "process_image_thread",
        "NoBackgroundsError",
        "create_slideshow_from_photos"
    ])
