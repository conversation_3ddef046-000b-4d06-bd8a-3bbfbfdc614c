# Unpacker Protection - Не трогать (удалено для совместимости)

LOCATIONS = [
    # Европа
    {"city": "Paris", "country": "France", "points": [
        (48.8584, 2.2945, "Eiffel Tower"),
        (48.8606, 2.3376, "Louvre Museum"),
        (48.8738, 2.2950, "Arc de Triomphe"),
        (48.8530, 2.3499, "Notre-Dame Cathedral"),
        (48.8715, 2.3077, "Galeries Lafayette")
    ]},
    {"city": "Barcelona", "country": "Spain", "points": [
        (41.4036, 2.1744, "Sagrada Familia"),
        (41.3851, 2.1734, "La Rambla"),
        (41.3916, 2.1646, "Casa Batllo"),
        (41.3916, 2.1827, "Gothic Quarter")
    ]},
    {"city": "Rome", "country": "Italy", "points": [
        (41.8902, 12.4922, "Colosseum"),
        (41.9022, 12.4539, "Vatican Museums"),
        (41.9009, 12.4833, "Trevi Fountain"),
        (41.8986, 12.4769, "Pantheon")
    ]},
    {"city": "London", "country": "United Kingdom", "points": [
        (51.5007, -0.1246, "Big <PERSON>"),
        (51.5033, -0.1195, "The London Eye"),
        (51.5074, -0.1278, "British Museum"),
        (51.5009, -0.1415, "Buckingham Palace"),
        (51.5138, -0.0984, "St. Paul's Cathedral"),
        (51.5081, -0.0759, "Tower Bridge"),
        (51.4994, -0.1248, "Westminster Abbey")
    ]},
    {"city": "Amsterdam", "country": "Netherlands", "points": [
        (52.3702, 4.8952, "Royal Palace"),
        (52.3667, 4.8945, "Anne Frank House"),
        (52.3600, 4.8852, "Vondelpark"),
        (52.3726, 4.8923, "Dam Square"),
        (52.3584, 4.8811, "Van Gogh Museum")
    ]},
    {"city": "Venice", "country": "Italy", "points": [
        (45.4371, 12.3326, "St. Mark's Square"),
        (45.4408, 12.3155, "Rialto Bridge"),
        (45.4338, 12.3387, "Bridge of Sighs"),
        (45.4343, 12.3388, "Doge's Palace"),
        (45.4412, 12.3266, "Grand Canal")
    ]},
    # Азия
    {"city": "Tokyo", "country": "Japan", "points": [
        (35.6895, 139.6917, "Shibuya Crossing"),
        (35.7100, 139.8107, "Tokyo Skytree"),
        (35.6586, 139.7454, "Roppongi Hills"),
        (35.6762, 139.6503, "Shinjuku Gyoen")
    ]},
    {"city": "Seoul", "country": "South Korea", "points": [
        (37.5519, 126.9918, "Myeongdong"),
        (37.5796, 126.9770, "Gyeongbokgung Palace"),
        (37.5511, 126.9882, "Namsan Seoul Tower"),
        (37.5270, 127.0432, "Gangnam District")
    ]},
    {"city": "Singapore", "country": "Singapore", "points": [
        (1.2849, 103.8636, "Gardens by the Bay"),
        (1.2839, 103.8594, "Marina Bay Sands"),
        (1.2904, 103.8520, "Clarke Quay"),
        (1.3138, 103.8159, "Singapore Botanic Gardens"),
        (1.2571, 103.8236, "Sentosa Island")
    ]},
    {"city": "Bangkok", "country": "Thailand", "points": [
        (13.7563, 100.5018, "Grand Palace"),
        (13.7466, 100.4893, "Wat Arun"),
        (13.7508, 100.4935, "Wat Pho"),
        (13.7469, 100.5349, "Terminal 21"),
        (13.7563, 100.5167, "Siam Paragon")
    ]},
    # США
    {"city": "New York", "country": "United States", "points": [
        (40.7484, -73.9857, "Empire State Building"),
        (40.7580, -73.9855, "Times Square"),
        (40.7527, -73.9772, "Grand Central Terminal"),
        (40.7484, -73.9857, "Bryant Park"),
        (40.7115, -74.0134, "World Trade Center")
    ]},
    {"city": "Los Angeles", "country": "United States", "points": [
        (34.0522, -118.2437, "Downtown LA"),
        (34.1016, -118.3267, "Hollywood Walk of Fame"),
        (34.0736, -118.2400, "Arts District"),
        (34.0736, -118.3091, "The Grove")
    ]},
    {"city": "San Francisco", "country": "United States", "points": [
        (37.8199, -122.4783, "Golden Gate Bridge"),
        (37.8102, -122.4104, "Pier 39"),
        (37.7952, -122.4028, "Union Square"),
        (37.7749, -122.4194, "Twin Peaks"),
        (37.8012, -122.4184, "Lombard Street")
    ]},
    {"city": "Vancouver", "country": "Canada", "points": [
        (49.2827, -123.1207, "Stanley Park"),
        (49.2762, -123.1238, "Granville Island"),
        (49.2845, -123.1116, "Gastown"),
        (49.2866, -123.1135, "Vancouver Lookout"),
        (49.2690, -123.1390, "Kitsilano Beach")
    ]},
    # Ближний Восток
    {"city": "Dubai", "country": "UAE", "points": [
        (25.1972, 55.2744, "Burj Khalifa"),
        (25.1412, 55.1854, "Palm Jumeirah"),
        (25.2048, 55.2708, "Dubai Mall"),
        (25.0776, 55.1306, "Dubai Marina")
    ]},
    # Австралия
    {"city": "Sydney", "country": "Australia", "points": [
        (-33.8568, 151.2153, "Sydney Opera House"),
        (-33.8523, 151.2108, "Sydney Harbour Bridge"),
        (-33.8568, 151.2153, "Circular Quay"),
        (-33.8919, 151.2773, "Bondi Beach"),
        (-33.8696, 151.2070, "Darling Harbour")
    ]}
]

# Популярные модели телефонов с их характеристиками
DEVICES = {
    # Apple устройства
    "iPhone 15 Pro Max": {
        "make": "Apple",
        "model": "iPhone 15 Pro Max",
        "software": "iOS 17.4.1",
        "lens": "Main Camera f/1.78",
        "sensor": "48 MP, f/1.78, 24mm",
        "iso_range": (32, 6400),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [13, 24, 48, 120],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "Sensor-shift OIS"
        }
    },
    "iPhone 15 Pro": {
        "make": "Apple",
        "model": "iPhone 15 Pro",
        "software": "iOS 17.4.1",
        "lens": "Main Camera f/1.78",
        "sensor": "48 MP, f/1.78, 24mm",
        "iso_range": (32, 6400),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [13, 24, 48, 120],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "Sensor-shift OIS"
        }
    },
    "iPhone 14 Pro Max": {
        "make": "Apple",
        "model": "iPhone 14 Pro Max",
        "software": "iOS 17.4.1",
        "lens": "Main Camera f/1.78",
        "sensor": "48 MP, f/1.78, 24mm",
        "iso_range": (32, 6400),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [13, 24, 48],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "Sensor-shift OIS"
        }
    },
    "iPhone 15": {
        "make": "Apple",
        "model": "iPhone 15",
        "software": "iOS 17.4.1",
        "lens": "Main Camera f/1.6",
        "sensor": "48 MP, f/1.6, 26mm",
        "iso_range": (32, 6400),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [13, 26, 48],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS"
        }
    },
    "iPhone 15 Plus": {
        "make": "Apple",
        "model": "iPhone 15 Plus",
        "software": "iOS 17.4.1",
        "lens": "Main Camera f/1.6",
        "sensor": "48 MP, f/1.6, 26mm",
        "iso_range": (32, 6400),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [13, 26, 48],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS"
        }
    },
    # Samsung устройства
    "Samsung Galaxy S24 Ultra": {
        "make": "Samsung",
        "model": "Galaxy S24 Ultra",
        "software": "One UI 6.1 (Android 14)",
        "lens": "Main Camera f/1.7",
        "sensor": "200 MP, f/1.7, 23mm",
        "iso_range": (50, 6400),
        "exposure_range": (1/12000, 1/30),
        "focal_lengths": [12, 23, 70, 230],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "7680x4320",
            "fps": [24, 30, 60],
            "stabilization": "OIS + VDIS"
        }
    },
    "Samsung Galaxy S24+": {
        "make": "Samsung",
        "model": "Galaxy S24+",
        "software": "One UI 6.1 (Android 14)",
        "lens": "Main Camera f/1.7",
        "sensor": "50 MP, f/1.7, 23mm",
        "iso_range": (50, 6400),
        "exposure_range": (1/12000, 1/30),
        "focal_lengths": [12, 23, 70],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + VDIS"
        }
    },
    "Samsung Galaxy Z Fold 5": {
        "make": "Samsung",
        "model": "Galaxy Z Fold 5",
        "software": "One UI 6.1 (Android 14)",
        "lens": "Main Camera f/1.7",
        "sensor": "50 MP, f/1.7, 23mm",
        "iso_range": (50, 6400),
        "exposure_range": (1/12000, 1/30),
        "focal_lengths": [12, 23, 70],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + VDIS"
        }
    },
    "Samsung Galaxy S24": {
        "make": "Samsung",
        "model": "Galaxy S24",
        "software": "One UI 6.1 (Android 14)",
        "lens": "Main Camera f/1.8",
        "sensor": "50 MP, f/1.8, 23mm",
        "iso_range": (50, 6400),
        "exposure_range": (1/12000, 1/30),
        "focal_lengths": [12, 23, 70],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + VDIS"
        }
    },
    "Samsung Galaxy Z Flip 5": {
        "make": "Samsung",
        "model": "Galaxy Z Flip 5",
        "software": "One UI 6.1 (Android 14)",
        "lens": "Main Camera f/1.7",
        "sensor": "12 MP, f/1.7, 24mm",
        "iso_range": (50, 6400),
        "exposure_range": (1/12000, 1/30),
        "focal_lengths": [24, 48],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + VDIS"
        }
    },
    # Google устройства
    "Google Pixel 8 Pro": {
        "make": "Google",
        "model": "Pixel 8 Pro",
        "software": "Android 14",
        "lens": "Main Camera f/1.68",
        "sensor": "50 MP, f/1.68, 25mm",
        "iso_range": (64, 6400),
        "exposure_range": (1/10000, 1/30),
        "focal_lengths": [24, 50, 120],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + EIS"
        }
    },
    "Google Pixel 8": {
        "make": "Google",
        "model": "Pixel 8",
        "software": "Android 14",
        "lens": "Main Camera f/1.7",
        "sensor": "50 MP, f/1.7, 25mm",
        "iso_range": (64, 6400),
        "exposure_range": (1/10000, 1/30),
        "focal_lengths": [24, 50],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + EIS"
        }
    },
    "Google Pixel 7a": {
        "make": "Google",
        "model": "Pixel 7a",
        "software": "Android 14",
        "lens": "Main Camera f/1.7",
        "sensor": "64 MP, f/1.7, 25mm",
        "iso_range": (64, 6400),
        "exposure_range": (1/10000, 1/30),
        "focal_lengths": [25, 50],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + EIS"
        }
    },
    # Xiaomi устройства
    "Xiaomi 14 Ultra": {
        "make": "Xiaomi",
        "model": "14 Ultra",
        "software": "MIUI 15 (Android 14)",
        "lens": "Leica Summilux f/1.9",
        "sensor": "50 MP, f/1.9, 23mm",
        "iso_range": (50, 12800),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [12, 23, 75, 120],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "7680x4320",
            "fps": [24, 30, 60],
            "stabilization": "OIS + EIS"
        }
    },
    "Xiaomi 14 Pro": {
        "make": "Xiaomi",
        "model": "14 Pro",
        "software": "MIUI 15 (Android 14)",
        "lens": "Leica Summilux f/1.9",
        "sensor": "50 MP, f/1.9, 23mm",
        "iso_range": (50, 12800),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [12, 23, 75],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + EIS"
        }
    },
    # OPPO устройства
    "OPPO Find X7 Ultra": {
        "make": "OPPO",
        "model": "Find X7 Ultra",
        "software": "ColorOS 14 (Android 14)",
        "lens": "Hasselblad f/1.8",
        "sensor": "50 MP, f/1.8, 23mm",
        "iso_range": (50, 12800),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [12, 23, 65, 135],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + VDIS"
        }
    },
    # vivo устройства
    "vivo X100 Pro+": {
        "make": "vivo",
        "model": "X100 Pro+",
        "software": "OriginOS 4 (Android 14)",
        "lens": "ZEISS f/1.75",
        "sensor": "50 MP, f/1.75, 23mm",
        "iso_range": (50, 12800),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [12, 23, 70, 100],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + Gimbal"
        }
    },
    # OnePlus устройства
    "OnePlus 12": {
        "make": "OnePlus",
        "model": "12",
        "software": "OxygenOS 14 (Android 14)",
        "lens": "Hasselblad f/1.6",
        "sensor": "50 MP, f/1.6, 23mm",
        "iso_range": (50, 6400),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [12, 23, 70],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + EIS"
        }
    },
    # Sony устройства
    "Sony Xperia 1 V": {
        "make": "Sony",
        "model": "Xperia 1 V",
        "software": "Android 14",
        "lens": "ZEISS f/1.7",
        "sensor": "52 MP, f/1.7, 24mm",
        "iso_range": (64, 12800),
        "exposure_range": (1/12000, 1/30),
        "focal_lengths": [16, 24, 85, 125],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60, 120],
            "stabilization": "OIS + EIS"
        }
    },
    # Huawei устройства
    "Huawei P70 Pro": {
        "make": "Huawei",
        "model": "P70 Pro",
        "software": "HarmonyOS 4.2",
        "lens": "XMAGE f/1.8",
        "sensor": "50 MP, f/1.8, 23mm",
        "iso_range": (50, 12800),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [14, 23, 70],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + AIS"
        }
    },

    "Huawei Mate 60 Pro+": {
        "make": "Huawei",
        "model": "Mate 60 Pro+",
        "software": "HarmonyOS 4.0",
        "lens": "XMAGE f/1.4",
        "sensor": "48 MP, f/1.4, 23mm",
        "iso_range": (50, 12800),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [14, 23, 90],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + AIS"
        }
    },

    "Huawei Mate 60 Pro": {
        "make": "Huawei",
        "model": "Mate 60 Pro",
        "software": "HarmonyOS 4.0",
        "lens": "XMAGE f/1.4",
        "sensor": "48 MP, f/1.4, 23mm",
        "iso_range": (50, 12800),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [14, 23, 85],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + AIS"
        }
    },

    "Huawei Mate 60": {
        "make": "Huawei",
        "model": "Mate 60",
        "software": "HarmonyOS 4.0",
        "lens": "XMAGE f/1.8",
        "sensor": "50 MP, f/1.8, 23mm",
        "iso_range": (50, 6400),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [14, 23, 50],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + AIS"
        }
    },

    "Huawei Mate X5": {
        "make": "Huawei",
        "model": "Mate X5",
        "software": "HarmonyOS 4.0",
        "lens": "XMAGE f/1.8",
        "sensor": "50 MP, f/1.8, 23mm",
        "iso_range": (50, 6400),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [14, 23, 70],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + AIS"
        }
    },

    "Huawei P60 Pro": {
        "make": "Huawei",
        "model": "P60 Pro",
        "software": "HarmonyOS 3.1",
        "lens": "XMAGE f/1.4",
        "sensor": "48 MP, f/1.4, 23mm",
        "iso_range": (50, 12800),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [14, 23, 90],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + AIS"
        }
    },

    "Huawei P60 Art": {
        "make": "Huawei",
        "model": "P60 Art",
        "software": "HarmonyOS 3.1",
        "lens": "XMAGE f/1.4",
        "sensor": "48 MP, f/1.4, 23mm",
        "iso_range": (50, 12800),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [14, 23, 90],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + AIS"
        }
    },

    "Huawei nova 12 Pro": {
        "make": "Huawei",
        "model": "nova 12 Pro",
        "software": "HarmonyOS 4.0",
        "lens": "XMAGE f/1.9",
        "sensor": "50 MP, f/1.9, 23mm",
        "iso_range": (50, 6400),
        "exposure_range": (1/8000, 1/30),
        "focal_lengths": [23, 50],
        "video_settings": {
            "codec": "HEVC",
            "resolution": "3840x2160",
            "fps": [24, 30, 60],
            "stabilization": "OIS + AIS"
        }
    }
}
