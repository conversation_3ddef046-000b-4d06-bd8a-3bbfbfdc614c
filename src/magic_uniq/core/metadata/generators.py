"""
Генераторы метаданных для различных типов устройств и камер.

Содержит функции для создания реалистичных метаданных
для различных устройств и камер.
"""

from typing import Dict, Any
from datetime import datetime, timedelta
import random


class MetadataGenerator:
    """
    Генератор метаданных для медиа-файлов.
    
    Создает реалистичные метаданные для различных устройств,
    камер и условий съемки.
    """
    
    def __init__(self):
        self.device_models = [
            "iPhone 14 Pro",
            "iPhone 13",
            "Samsung Galaxy S23",
            "Google Pixel 7",
            "Canon EOS R5",
            "Nikon D850",
            "Sony A7R IV"
        ]
    
    def generate_camera_metadata(self) -> Dict[str, Any]:
        """
        Сгенерировать метаданные камеры.
        
        Returns:
            Словарь с метаданными камеры
        """
        # TODO: Перенести логику из оригинального кода
        return {
            "make": random.choice(["Apple", "Samsung", "Google", "Canon", "Nikon", "Sony"]),
            "model": random.choice(self.device_models),
            "iso": random.choice([100, 200, 400, 800, 1600]),
            "aperture": random.choice([1.8, 2.0, 2.8, 4.0, 5.6]),
            "shutter_speed": random.choice(["1/60", "1/125", "1/250", "1/500"])
        }
    
    def generate_timestamp(self) -> datetime:
        """
        Сгенерировать случайную временную метку.
        
        Returns:
            Случайная дата и время
        """
        # Генерируем дату в пределах последнего года
        now = datetime.now()
        days_ago = random.randint(1, 365)
        return now - timedelta(days=days_ago)
    
    def generate_gps_coordinates(self) -> Dict[str, float]:
        """
        Сгенерировать случайные GPS координаты.
        
        Returns:
            Словарь с координатами
        """
        # TODO: Перенести логику из оригинального кода
        return {
            "latitude": random.uniform(-90, 90),
            "longitude": random.uniform(-180, 180),
            "altitude": random.uniform(0, 1000)
        }
