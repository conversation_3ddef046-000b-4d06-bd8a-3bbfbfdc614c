"""
Базовые классы для эффектов обработки медиа-контента.

Определяет общий интерфейс и базовую функциональность
для всех типов эффектов.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    Image = None

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    np = None


class BaseEffect(ABC):
    """
    Базовый класс для всех эффектов.
    
    Определяет общий интерфейс для применения эффектов
    к изображениям и видео.
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        Инициализация эффекта.
        
        Args:
            name: Название эффекта
            config: Конфигурация эффекта
        """
        self.name = name
        self.config = config or {}
        self.enabled = self.config.get('enabled', True)
    
    @abstractmethod
    def apply(self, data: Any, **kwargs) -> Any:
        """
        Применить эффект к данным.
        
        Args:
            data: Входные данные (изображение, кадр видео и т.д.)
            **kwargs: Дополнительные параметры
            
        Returns:
            Обработанные данные
        """
        pass
    
    def is_enabled(self) -> bool:
        """Проверить, включен ли эффект."""
        return self.enabled
    
    def set_enabled(self, enabled: bool) -> None:
        """Включить/выключить эффект."""
        self.enabled = enabled
    
    def get_config(self) -> Dict[str, Any]:
        """Получить конфигурацию эффекта."""
        return self.config.copy()
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """Обновить конфигурацию эффекта."""
        self.config.update(config)
        self.enabled = self.config.get('enabled', self.enabled)


class ImageEffect(BaseEffect):
    """
    Базовый класс для эффектов обработки изображений.
    """
    
    @abstractmethod
    def apply(self, image: 'Image.Image', **kwargs) -> 'Image.Image':
        """
        Применить эффект к изображению.
        
        Args:
            image: Входное изображение PIL
            **kwargs: Дополнительные параметры
            
        Returns:
            Обработанное изображение
        """
        pass


class VideoEffect(BaseEffect):
    """
    Базовый класс для эффектов обработки видео.
    """
    
    @abstractmethod
    def apply(self, frame: 'np.ndarray', frame_index: int = 0, **kwargs) -> 'np.ndarray':
        """
        Применить эффект к кадру видео.
        
        Args:
            frame: Кадр видео как numpy array
            frame_index: Индекс кадра в видео
            **kwargs: Дополнительные параметры
            
        Returns:
            Обработанный кадр
        """
        pass


class EffectChain:
    """
    Цепочка эффектов для последовательного применения.
    """
    
    def __init__(self):
        self.effects = []
    
    def add_effect(self, effect: BaseEffect) -> None:
        """Добавить эффект в цепочку."""
        self.effects.append(effect)
    
    def remove_effect(self, effect_name: str) -> bool:
        """
        Удалить эффект из цепочки по имени.
        
        Returns:
            True если эффект был удален, False если не найден
        """
        for i, effect in enumerate(self.effects):
            if effect.name == effect_name:
                del self.effects[i]
                return True
        return False
    
    def apply_all(self, data: Any, **kwargs) -> Any:
        """
        Применить все включенные эффекты в цепочке.
        
        Args:
            data: Входные данные
            **kwargs: Дополнительные параметры
            
        Returns:
            Данные после применения всех эффектов
        """
        result = data
        for effect in self.effects:
            if effect.is_enabled():
                result = effect.apply(result, **kwargs)
        return result
    
    def get_enabled_effects(self) -> list:
        """Получить список включенных эффектов."""
        return [effect for effect in self.effects if effect.is_enabled()]
    
    def clear(self) -> None:
        """Очистить цепочку эффектов."""
        self.effects.clear()
