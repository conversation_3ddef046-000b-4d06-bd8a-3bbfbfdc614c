import random
import traceback
from dataclasses import dataclass
from pathlib import Path
import tempfile
from typing import Tuple, List, Optional, Dict, Set
import cv2 #90mb
import numpy as np
import subprocess
from concurrent.futures import ThreadPoolExecutor
import os
from traceback import print_exc
from datetime import datetime, timedelta
import time
import json
from scipy.signal import sawtooth
import scipy.interpolate
import librosa
import soundfile as sf
from tqdm import tqdm
from .metadata.metadata_config import LOCATIONS, DEVICES
from .effects.snow_effect import SnowEffect
import queue
import shutil
import signal
import threading
import sys
import colorsys
import configparser
import aiohttp
from ..utils.system_utils import get_system_uuid

from multiprocessing import Manager, Value, Process, Event
import threading
from concurrent.futures import ThreadPoolExecutor
from functools import partial
import asyncio
import psutil  # Для мониторинга процессов ОС

# Добавьте этот код в начало файла video_uniquify.py после импортов
import threading

# Unpacker Protection - Не трогать (удалено для совместимости)

# Глобальное состояние для отслеживания используемых фонов
_global_background_lock = threading.Lock()
_used_backgrounds = set()

class UniquificationManager:
    """
    Менеджер процессов уникализации.
    Обеспечивает централизованное управление и мониторинг всех процессов уникализации,
    используя PID процессов операционной системы для надежного отслеживания.
    """
    def __init__(self):
        # Используем Manager для создания разделяемых объектов между процессами
        self._manager = Manager()

        # Словарь для хранения информации о процессах {process_id: process_info}
        # process_info теперь включает pid процесса
        self.processes = self._manager.dict()

        # Флаг глобальной остановки
        self.stop_flag = Event()

        # Путь для хранения временных файлов
        self.temp_dir = Path(tempfile.gettempdir()) / 'UniqTemp'
        self.temp_dir.mkdir(exist_ok=True)

        # Запускаем мониторинг процессов в отдельном потоке
        self.monitor_thread = threading.Thread(target=self._monitor_processes, daemon=True)
        self.monitor_thread.start()

    def register_process(self, video_path: str, total_iterations: int, pid: int) -> str:
        """
        Регистрирует новый процесс уникализации.

        Args:
            video_path: Путь к обрабатываемому видео
            total_iterations: Общее количество итераций
            pid: PID процесса операционной системы, выполняющего уникализацию

        Returns:
            str: Уникальный идентификатор процесса (process_id)
        """
        process_id = str(int(time.time() * 1000))  # Используем timestamp как ID

        process_info = {
            'video_path': video_path,
            'start_time': datetime.now().isoformat(),
            'total_iterations': total_iterations,
            'current_iteration': 0,
            'progress': 0.0,
            'status': 'running',
            'last_update': time.time(),
            'pid': pid  # Сохраняем PID процесса для мониторинга
        }

        self.processes[process_id] = process_info
        # print(f"Зарегистрирован процесс {process_id} (PID: {pid}) для видео: {video_path}")
        return process_id

    def update_progress(self, process_id: str, iteration: int, progress: float):
        """
        Обновляет прогресс процесса уникализации.

        Args:
            process_id: ID процесса, полученный от register_process
            iteration: Текущая итерация обработки
            progress: Прогресс текущей итерации в процентах (0-100)
        """
        if process_id in self.processes:
            process_info = self.processes[process_id]
            process_info.update({
                'current_iteration': iteration,
                'progress': progress,
                'last_update': time.time()
            })
            self.processes[process_id] = process_info

    def stop_all(self):
        """
        Останавливает все зарегистрированные процессы уникализации.
        Использует API операционной системы для надежного завершения процессов.
        """
        # print("Останавливаем все процессы уникализации...")

        for process_id in list(self.processes.keys()):
            try:
                process_info = self.processes[process_id]
                pid = process_info.get('pid')

                if pid is None:
                    continue

                try:
                    # Получаем родительский процесс через psutil
                    parent = psutil.Process(pid)

                    # Получаем все дочерние процессы рекурсивно
                    children = parent.children(recursive=True)

                    # Останавливаем дочерние процессы
                    for child in children:
                        try:
                            child.terminate()
                        except psutil.NoSuchProcess:
                            continue

                    # Останавливаем родительский процесс
                    parent.terminate()

                    # Ждем завершения процессов с таймаутом
                    gone, alive = psutil.wait_procs(children + [parent], timeout=3)

                    # Принудительно завершаем процессы, которые все еще живы
                    for p in alive:
                        try:
                            p.kill()
                        except psutil.NoSuchProcess:
                            continue

                    # Проверяем и завершаем процессы-зомби
                    for p in children + [parent]:
                        try:
                            if p.is_running() and p.status() == psutil.STATUS_ZOMBIE:
                                os.waitpid(p.pid, 0)
                        except (psutil.NoSuchProcess, ChildProcessError):
                            continue

                except psutil.NoSuchProcess:
                    #print(f"Процесс {pid} уже не существует")
                    pass

                # Обновляем статус процесса
                process_info['status'] = 'stopped'
                self.processes[process_id] = process_info

            except Exception as e:
                print(f"Ошибка при остановке процесса {process_id}: {str(e)}")

        self.stop_flag.set()
        # print("Все процессы уникализации остановлены.")

    def resume_all(self):
        """
        Возобновляет все остановленные процессы уникализации.
        """
        self.stop_flag.clear()

        for process_id in self.processes:
            process_info = self.processes[process_id]
            if process_info['status'] == 'stopped':
                process_info['status'] = 'running'
                self.processes[process_id] = process_info
        # print("Возобновлены все процессы уникализации.")

    def get_process_info(self, process_id: str) -> dict:
        """
        Получает информацию о конкретном процессе, включая системные метрики.

        Args:
            process_id: ID процесса

        Returns:
            dict: Словарь с информацией о процессе или пустой словарь
        """
        if process_id not in self.processes:
            return {}

        process_info = dict(self.processes[process_id])
        pid = process_info.get('pid')

        if pid:
            try:
                process = psutil.Process(pid)
                # Добавляем системные метрики
                process_info.update({
                    'cpu_percent': process.cpu_percent(),
                    'memory_percent': process.memory_percent(),
                    'is_running': process.is_running(),
                    'status': process.status()
                })
            except psutil.NoSuchProcess:
                process_info['status'] = 'stopped'
                process_info['is_running'] = False

        return process_info

    def get_all_processes(self) -> dict:
        """
        Получает информацию о всех зарегистрированных процессах.

        Returns:
            dict: Словарь {process_id: process_info}
        """
        return {k: dict(v) for k, v in self.processes.items()}

    def _monitor_processes(self):
        """
        Приватный метод для мониторинга процессов в фоновом потоке.
        Проверяет живость процессов по PID, таймауты неактивности и завершение.
        Очищает неактивные и завершенные процессы из списка.
        """
        while not getattr(self, '_stop_monitor', False):
            try:
                current_time = time.time()
                inactive_processes = []
                completed_processes = []

                for process_id, info in self.processes.items():
                    try:
                        pid = info.get('pid')
                        if pid is None:
                            # print(f"Монитор: процесс {process_id} не имеет PID, помечаем как неактивный.")
                            inactive_processes.append(process_id)
                            continue

                        if not psutil.pid_exists(pid):
                            # print(f"Монитор: процесс {process_id} (PID {pid}) не найден, помечаем как неактивный.")
                            inactive_processes.append(process_id)
                            continue

                        process = psutil.Process(pid)
                        if not process.is_running():
                            # print(f"Монитор: процесс {process_id} (PID {pid}) не запущен, помечаем как неактивный.")
                            inactive_processes.append(process_id)
                            continue

                        if current_time - info['last_update'] > 300:  # 5 минут таймаут неактивности
                            # print(f"Монитор: процесс {process_id} (PID {pid}) не обновлялся > 5 мин, помечаем как неактивный.")
                            inactive_processes.append(process_id)
                            continue

                        if info['progress'] >= 100.0:
                            completed_processes.append(process_id)
                            continue

                        if info['status'] == 'stopped':
                            if current_time - info['last_update'] > 600:  # 10 минут таймаут для остановленных процессов
                                inactive_processes.append(process_id)
                                continue

                    except psutil.NoSuchProcess:
                        # print(f"Монитор: процесс {process_id} (PID {info.get('pid')}) исчез (NoSuchProcess), неактивен.")
                        inactive_processes.append(process_id)
                    except Exception as e:
                        print(f"Монитор: ошибка проверки процесса {process_id} (PID {info.get('pid')}): {str(e)}")
                        inactive_processes.append(process_id)

                # Очищаем неактивные процессы
                for process_id in inactive_processes:
                    try:
                        # print(f"Монитор: удаляем неактивный процесс {process_id}.")
                        if process_id in self.processes:
                            del self.processes[process_id]
                    except Exception as e:
                        print(f"Монитор: ошибка при очистке неактивного процесса {process_id}: {str(e)}")

                # Очищаем завершенные процессы
                for process_id in completed_processes:
                    try:
                        final_info = self.processes[process_id]
                        if current_time - final_info['last_update'] > 60:  # 1 минута после завершения
                            # print(f"Монитор: удаляем завершенный процесс {process_id}.")
                            if process_id in self.processes:
                                del self.processes[process_id]
                    except Exception as e:
                        print(f"Монитор: ошибка при очистке завершенного процесса {process_id}: {str(e)}")

            except Exception as e:
                print(f"Монитор: критическая ошибка в цикле мониторинга: {str(e)}")

            for _ in range(60):  # Спим 60 секунд с проверкой флага остановки каждую секунду
                if getattr(self, '_stop_monitor', False):
                    break
                time.sleep(1)

    def cleanup(self):
        """
        Очищает временную директорию и освобождает ресурсы.
        """
        try:
            # Останавливаем все активные процессы
            self.stop_all()

            # Останавливаем поток мониторинга
            self._stop_monitor = True
            if self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)

            # Очищаем временную директорию
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                # print(f"Менеджер: временная директория '{self.temp_dir}' очищена.")
        except Exception as e:
            print(f"Менеджер: ошибка при очистке ресурсов: {str(e)}")

def load_config(config_path: str = "settings.ini") -> configparser.ConfigParser:
    """
    Загружает конфигурацию из INI файла

    Args:
        config_path (str): Путь к файлу конфигурации

    Returns:
        configparser.ConfigParser: Объект с загруженной конфигурацией
    """
    config = configparser.ConfigParser()
    config.read(config_path)
    return config

@dataclass
class VideoEffectParams:
    scale: float = 0.9
    puzzle_cycle_speed: int = 1  # Скорость появления пазлов
    saturation: float = 1.2
    transparency: float = 0.94
    gamma: float = 1.2
    movement_amplitude: float = random.uniform(0.15, 0.25)
    movement_speed: float = random.uniform(0.3, 0.6)
    movement_complexity: float = random.uniform(0.5, 1.5)
    movement_pattern: str = 'circular'  # Можно выбрать: circular, figure8, spiral, zigzag, diagonal, combination, random
    blur_amount: float = random.uniform(0.3, 1.5)
    brightness: float = random.uniform(1.1, 1.3)
    contrast: float = random.uniform(1.1, 1.3)
    vignette_intensity: float = random.uniform(0.2, 0.6)
    vignette_radius: float = random.uniform(0.2, 0.9)
    noise_intensity: float = random.uniform(0.02, 0.08)
    white_balance_temperature: float = random.uniform(0.8, 1.2)
    white_balance_tint: float = random.uniform(-0.1, 0.1)
    frame_removal_probability: float = 0.5
    frame_removal_percentage: float = 0.0
    snowfall_intensity: float = random.uniform(0.5, 1.5)
    snowflake_size: int = random.randint(1, 10)
    snowflake_opacity: float = random.uniform(0.5, 1.0)
    wind_strength: float = random.uniform(0.5, 1.5)
    border_radius_min: int = random.randint(10, 40)
    border_radius_max: int = random.randint(80, 150)
    border_radius_speed: float = random.uniform(0.5, 2.0)
    lines_enabled: bool = False
    lines_animated: bool = False
    lines_count: int = 5
    line_width: float = 3.0
    line_opacity: float = 0.5
    line_length: float = 0.6
    line_fade_speed: float = 0.005
    line_min_delay: int = 60
    line_max_delay: int = 120
    line_transition_threshold: float = 0.98
    background_start_scale: float = 1.8
    background_end_scale: float = 1.0
    background_scale_speed: float = 0.5
    background_scale_pattern: str = 'linear'
    mosaic_enabled: bool = False
    mosaic_size: int = 2
    mosaic_opacity: float = 0.8
    micro_dots_enabled: bool = False
    micro_dots_density: float = 0.001
    micro_dots_opacity: float = 0.01
    png_overlay_enabled: bool = False
    png_overlay_opacity: float = 0.7
    png_overlay_scale: float = 1.5
    wave_distortion_enabled: bool = False
    wave_amplitude: float = random.uniform(0.1, 0.5)
    wave_frequency: float = random.uniform(0.5, 2.0)
    wave_speed: float = random.uniform(0.1, 1.0)
    emoji_enabled: bool = False
    emoji_count: int = 5
    emoji_size: int = 30
    emoji_speed: float = 2.0
    emoji_rotation_speed: float = 1.0
    emoji_opacity: float = 0.8
    compression_level: str = "medium"
    video_overlay_enabled: bool = False
    video_overlay_opacity: float = 70.0
    video_overlay_scale: float = 100.0
    video_overlay_rotation: float = 0.0
    puzzle_enabled: bool = False
    puzzle_rows: int = 4
    puzzle_pattern: str = 'random'
    puzzle_cycle_speed: int = 1
    puzzle_visible_percent: int = 25  # Новый параметр - процент видимых частей

class VideoUniquifierConfig:
    def __init__(self, config_path: str = "settings.ini"):
        # Загружаем конфигурацию
        self.config = load_config(config_path)

        # Директории - используем кроссплатформенные пути в папке data/
        self.base_dir = Path.cwd() / "data"
        # Создаем базовую директорию data, если она не существует
        self.base_dir.mkdir(exist_ok=True)
        self.input_dir = self.base_dir / "input"
        self.output_dir = self.base_dir / "output"
        self.bg_dir = self.base_dir / "backgrounds"

        # Количество итераций
        self.iterations = 1
        self.infinite_loop = False

        # Scale
        self.scale_enabled = self.config.getboolean('Scale', 'enabled', fallback=False)
        self.scale_range = (
            self.config.getfloat('Scale', 'min', fallback=0.0),
            self.config.getfloat('Scale', 'max', fallback=1.0)
        )

        # Saturation
        self.saturation_enabled = self.config.getboolean('Saturation', 'enabled', fallback=False)
        self.saturation_range = (
            self.config.getfloat('Saturation', 'min', fallback=1.0),
            self.config.getfloat('Saturation', 'max', fallback=2.0)
        )

        # Transparency
        self.transparency_enabled = self.config.getboolean('Transparency', 'enabled', fallback=False)
        self.transparency_range = (
            self.config.getfloat('Transparency', 'min', fallback=0.0),
            self.config.getfloat('Transparency', 'max', fallback=1.0)
        )

        # Gamma
        self.gamma_enabled = self.config.getboolean('Gamma', 'enabled', fallback=False)
        self.gamma_range = (
            self.config.getfloat('Gamma', 'min', fallback=1.0),
            self.config.getfloat('Gamma', 'max', fallback=3.0)
        )

        # Brightness
        self.brightness_enabled = self.config.getboolean('Brightness', 'enabled', fallback=False)
        self.brightness_range = (
            self.config.getfloat('Brightness', 'min', fallback=0.0),
            self.config.getfloat('Brightness', 'max', fallback=2.0)
        )

        # Contrast
        self.contrast_enabled = self.config.getboolean('Contrast', 'enabled', fallback=False)
        self.contrast_range = (
            self.config.getfloat('Contrast', 'min', fallback=0.0),
            self.config.getfloat('Contrast', 'max', fallback=2.0)
        )

        # Movement
        self.movement_enabled = self.config.getboolean('Movement', 'enabled', fallback=False)
        self.movement_pattern = self.config.get('Movement', 'pattern', fallback='circular')
        self.movement_amplitude_range = (
            self.config.getfloat('Movement', 'amplitude_min', fallback=0.0),
            self.config.getfloat('Movement', 'amplitude_max', fallback=1.0)
        )
        self.movement_speed_range = (
            self.config.getfloat('Movement', 'speed_min', fallback=0.0),
            self.config.getfloat('Movement', 'speed_max', fallback=1.0)
        )
        self.movement_complexity_range = (
            self.config.getfloat('Movement', 'complexity_min', fallback=0.1),
            self.config.getfloat('Movement', 'complexity_max', fallback=2.2)
        )

        # Background Scale
        self.background_scale_enabled = self.config.getboolean('Background_Scale', 'enabled', fallback=False)
        self.background_start_scale = (
            self.config.getfloat('Background_Scale', 'start_scale_min', fallback=1.0),
            self.config.getfloat('Background_Scale', 'start_scale_max', fallback=3.0)
        )
        self.background_end_scale = (
            self.config.getfloat('Background_Scale', 'end_scale_min', fallback=1.0),
            self.config.getfloat('Background_Scale', 'end_scale_max', fallback=3.0)
        )
        self.background_scale_speed = (
            self.config.getfloat('Background_Scale', 'speed_min', fallback=1.0),
            self.config.getfloat('Background_Scale', 'speed_max', fallback=2.5)
        )
        self.background_scale_pattern = self.config.get('Background_Scale', 'pattern', fallback='linear')

        # Lines
        self.lines_enabled = self.config.getboolean('Lines', 'enabled', fallback=False)
        self.lines_animated = self.config.getboolean('Lines', 'animated', fallback=False)
        # Только количество линий загружаем как целое число
        self.lines_count_range = (
            self.config.getint('Lines', 'count_min', fallback=1),
            self.config.getint('Lines', 'count_max', fallback=20)
        )
        self.line_width_range = (
            self.config.getfloat('Lines', 'width_min', fallback=1.0),
            self.config.getfloat('Lines', 'width_max', fallback=15.0)
        )
        self.line_opacity_range = (
            self.config.getfloat('Lines', 'opacity_min', fallback=0.1),
            self.config.getfloat('Lines', 'opacity_max', fallback=1.0)
        )
        self.line_angle_range = (
            self.config.getfloat('Lines', 'angle_min', fallback=0.0),
            self.config.getfloat('Lines', 'angle_max', fallback=360.0)
        )
        self.line_length_range = (
            self.config.getfloat('Lines', 'length_min', fallback=0.1),
            self.config.getfloat('Lines', 'length_max', fallback=1.0)
        )
        self.line_fade_speed_range = (
            self.config.getfloat('Lines', 'fade_speed_min', fallback=0.001),
            self.config.getfloat('Lines', 'fade_speed_max', fallback=0.01)
        )
        self.line_min_delay_range = (
            self.config.getfloat('Lines', 'min_delay_min', fallback=5.0),
            self.config.getfloat('Lines', 'min_delay_max', fallback=15.0)
        )
        self.line_max_delay_range = (
            self.config.getfloat('Lines', 'max_delay_min', fallback=150.0),
            self.config.getfloat('Lines', 'max_delay_max', fallback=200.0)
        )
        self.line_transition_threshold_range = (
            self.config.getfloat('Lines', 'transition_threshold_min', fallback=0.9),
            self.config.getfloat('Lines', 'transition_threshold_max', fallback=0.99)
        )

        # Snow
        self.snow_enabled = self.config.getboolean('Snow', 'enabled', fallback=False)
        self.snowfall_intensity_range = (
            self.config.getfloat('Snow', 'intensity_min', fallback=0.1),
            self.config.getfloat('Snow', 'intensity_max', fallback=2.0)
        )
        self.snowflake_size_range = (
            self.config.getfloat('Snow', 'size_min', fallback=1.0),
            self.config.getfloat('Snow', 'size_max', fallback=15.0)
        )
        self.snowflake_opacity_range = (
            self.config.getfloat('Snow', 'opacity_min', fallback=0.1),
            self.config.getfloat('Snow', 'opacity_max', fallback=1.0)
        )
        self.wind_strength_range = (
            self.config.getfloat('Snow', 'wind_strength_min', fallback=0.1),
            self.config.getfloat('Snow', 'wind_strength_max', fallback=2.0)
        )

        # Blur
        self.blur_enabled = self.config.getboolean('Blur', 'enabled', fallback=False)
        self.blur_range = (
            self.config.getfloat('Blur', 'min', fallback=0.05),
            self.config.getfloat('Blur', 'max', fallback=0.185)
        )

        # Noise
        self.noise_enabled = self.config.getboolean('Noise', 'enabled', fallback=False)
        self.noise_intensity_range = (
            self.config.getfloat('Noise', 'min', fallback=0.1668),
            self.config.getfloat('Noise', 'max', fallback=0.5)
        )

        # Vignette
        self.vignette_enabled = self.config.getboolean('Vignette', 'enabled', fallback=False)
        self.vignette_intensity_range = (
            self.config.getfloat('Vignette', 'intensity_min', fallback=0.1),
            self.config.getfloat('Vignette', 'intensity_max', fallback=0.95)
        )
        self.vignette_radius_range = (
            self.config.getfloat('Vignette', 'radius_min', fallback=0.1),
            self.config.getfloat('Vignette', 'radius_max', fallback=0.95)
        )

        # White Balance
        self.white_balance_enabled = self.config.getboolean('White_Balance', 'enabled', fallback=False)
        self.white_balance_temperature_range = (
            self.config.getfloat('White_Balance', 'temperature_min', fallback=1.0),
            self.config.getfloat('White_Balance', 'temperature_max', fallback=2.0)
        )
        self.white_balance_tint_range = (
            self.config.getfloat('White_Balance', 'tint_min', fallback=0.0),
            self.config.getfloat('White_Balance', 'tint_max', fallback=1.0)
        )

        # Micro Dots
        self.micro_dots_enabled = self.config.getboolean('Micro_Dots', 'enabled', fallback=False)
        self.micro_dots_density_range = (
            self.config.getfloat('Micro_Dots', 'density_min', fallback=0.0001),
            self.config.getfloat('Micro_Dots', 'density_max', fallback=0.10)
        )
        self.micro_dots_opacity_range = (
            self.config.getfloat('Micro_Dots', 'opacity_min', fallback=0.001),
            self.config.getfloat('Micro_Dots', 'opacity_max', fallback=1.0)
        )

        # Mosaic
        self.mosaic_enabled = self.config.getboolean('Mosaic', 'enabled', fallback=False)
        self.mosaic_size_range = (
            self.config.getfloat('Mosaic', 'size_min', fallback=1.0),
            self.config.getfloat('Mosaic', 'size_max', fallback=40.0)
        )
        self.mosaic_opacity_range = (
            self.config.getfloat('Mosaic', 'opacity_min', fallback=0.1),
            self.config.getfloat('Mosaic', 'opacity_max', fallback=1.0)
        )

        # Border
        self.border_enabled = self.config.getboolean('Border', 'enabled', fallback=False)
        self.border_radius_min_range = (
            self.config.getfloat('Border', 'radius_min_min', fallback=5.0),
            self.config.getfloat('Border', 'radius_min_max', fallback=10.0)
        )
        self.border_radius_max_range = (
            self.config.getfloat('Border', 'radius_max_min', fallback=150.0),
            self.config.getfloat('Border', 'radius_max_max', fallback=200.0)
        )
        self.border_radius_speed_range = (
            self.config.getfloat('Border', 'radius_speed_min', fallback=0.02),
            self.config.getfloat('Border', 'radius_speed_max', fallback=0.7)
        )

        # PNG Overlay
        self.png_overlay_enabled = self.config.getboolean('Png-Overlay', 'enabled', fallback=False)
        self.png_overlay_opacity_range = (
            self.config.getint('Png-Overlay', 'opacity_min', fallback=1),
            self.config.getint('Png-Overlay', 'opacity_max', fallback=100)
        )
        self.png_overlay_scale_range = (
            self.config.getint('Png-Overlay', 'scale_min', fallback=100),
            self.config.getint('Png-Overlay', 'scale_max', fallback=200)
        )
        self.png_overlay_delete_used = self.config.getboolean('Png-Overlay', 'delete-used', fallback=False)

        # Сдвиг звуковой частоты
        self.frequency_shift_enabled = self.config.getboolean('Frequency-Shift', 'enabled', fallback=False)
        self.frequency_shift_amount_range = (
            self.config.getfloat('Frequency-Shift', 'amount_min', fallback=0.5),
            self.config.getfloat('Frequency-Shift', 'amount_max', fallback=12.00)
        )
        self.frequency_shift_randomization_range = (
            self.config.getfloat('Frequency-Shift', 'randomization_min', fallback=0.1),
            self.config.getfloat('Frequency-Shift', 'randomization_max', fallback=0.8)
        )

        # Фазовый сдвиг звука
        self.phase_shift_enabled = self.config.getboolean('Phase-Shift', 'enabled', fallback=False)
        self.phase_shift_amount_range = (
            self.config.getfloat('Phase-Shift', 'amount_min', fallback=0.2),
            self.config.getfloat('Phase-Shift', 'amount_max', fallback=1.5)
        )
        self.phase_shift_frequency_range = (
            self.config.getfloat('Phase-Shift', 'freq_min', fallback=50),
            self.config.getfloat('Phase-Shift', 'freq_max', fallback=15000)
        )

        # Аудио микрозадержка
        self.micro_delay_enabled = self.config.getboolean('Micro-Delay', 'enabled', fallback=False)
        self.micro_delay_time_range = (
            self.config.getfloat('Micro-Delay', 'time_min', fallback=1.0),
            self.config.getfloat('Micro-Delay', 'time_max', fallback=20.0)
        )
        self.micro_delay_feedback_range = (
            self.config.getfloat('Micro-Delay', 'feedback_min', fallback=0.1),
            self.config.getfloat('Micro-Delay', 'feedback_max', fallback=0.8)
        )

        # Манипуляции со стереофоническим звуком
        self.stereo_manipulation_enabled = self.config.getboolean('Stereo-Effects', 'enabled', fallback=False)
        self.stereo_width_range = (
            self.config.getfloat('Stereo-Effects', 'width_min', fallback=0.5),
            self.config.getfloat('Stereo-Effects', 'width_max', fallback=2.0)
        )
        self.stereo_rotation_range = (
            self.config.getfloat('Stereo-Effects', 'rotation_min', fallback=-0.8),
            self.config.getfloat('Stereo-Effects', 'rotation_max', fallback=0.8)
        )

        # Компрессия
        self.compression_enabled = self.config.getboolean('Compression', 'enabled', fallback=False)
        self.compression_threshold_range = (
            self.config.getfloat('Compression', 'threshold_min', fallback=-40),
            self.config.getfloat('Compression', 'threshold_max', fallback=-10)
        )
        self.compression_ratio_range = (
            self.config.getfloat('Compression', 'ratio_min', fallback=1.2),
            self.config.getfloat('Compression', 'ratio_max', fallback=8.0)
        )
        self.compression_attack_range = (
            self.config.getfloat('Compression', 'attack_min', fallback=1),
            self.config.getfloat('Compression', 'attack_max', fallback=50)
        )
        self.compression_release_range = (
            self.config.getfloat('Compression', 'release_min', fallback=10),
            self.config.getfloat('Compression', 'release_max', fallback=500)
        )

         # Параметры шума для аудио
        self.audio_noise_enabled = self.config.getboolean('Audio-Noise', 'enabled', fallback=False)
        self.audio_noise_intensity_range = (
            self.config.getfloat('Audio-Noise', 'intensity_min', fallback=0.0001),
            self.config.getfloat('Audio-Noise', 'intensity_max', fallback=0.05)
        )
        self.audio_noise_type = self.config.get('Audio-Noise', 'type', fallback='pink')  # white/pink/brown/blue/violet/grey/velvet на выбор 7 типов шума

        # Frame Removal
        self.frame_removal_enabled = self.config.getboolean('Frame_Removal', 'enabled', fallback=False)
        self.frame_removal_min = self.config.getfloat('Frame_Removal', 'percentage_min', fallback=1)
        self.frame_removal_max = self.config.getfloat('Frame_Removal', 'percentage_max', fallback=10)

        # новые параметры для метаданных
        self.metadata_remove_original = self.config.getboolean('Metadata', 'remove_original', fallback=True)
        self.metadata_generate_new = self.config.getboolean('Metadata', 'generate_new', fallback=False)

        # Волновые искажения
        self.wave_distortion_enabled = self.config.getboolean('Wave-Distortion', 'enabled', fallback=False)
        self.wave_amplitude_range = (
            self.config.getint('Wave-Distortion', 'amplitude_min', fallback=1),
            self.config.getint('Wave-Distortion', 'amplitude_max', fallback=100)
        )
        self.wave_frequency_range = (
            self.config.getint('Wave-Distortion', 'frequency_min', fallback=1),
            self.config.getint('Wave-Distortion', 'frequency_max', fallback=100)
        )
        self.wave_speed_range = (
            self.config.getint('Wave-Distortion', 'speed_min', fallback=1),
            self.config.getint('Wave-Distortion', 'speed_max', fallback=100)
        )
        # Эмодзи
        self.emoji_enabled = self.config.getboolean('Emoji', 'enabled', fallback=False)
        self.emoji_count_range = (
            self.config.getint('Emoji', 'count_min', fallback=1),
            self.config.getint('Emoji', 'count_max', fallback=30)
        )
        self.emoji_size_range = (
            self.config.getint('Emoji', 'size_min', fallback=1),
            self.config.getint('Emoji', 'size_max', fallback=100)
        )
        self.emoji_speed_range = (
            self.config.getint('Emoji', 'speed_min', fallback=1),
            self.config.getint('Emoji', 'speed_max', fallback=100)
        )
        self.emoji_rotation_speed_range = (
            self.config.getint('Emoji', 'rotation_speed_min', fallback=1),
            self.config.getint('Emoji', 'rotation_speed_max', fallback=100)
        )
        self.emoji_opacity_range = (
            self.config.getint('Emoji', 'opacity_min', fallback=1),
            self.config.getint('Emoji', 'opacity_max', fallback=100)
        )

        # Уровень Сжатия
        self.compression_level_enabled = self.config.getboolean('Compression-Level', 'enabled', fallback=False)
        self.compression_level = self.config.get('Compression-Level', 'level', fallback='medium')

        # Video Overlay - добавляем новую секцию настроек
        self.video_overlay_enabled = self.config.getboolean('Video-Overlay', 'enabled', fallback=False)
        self.video_overlay_opacity_range = (
            self.config.getfloat('Video-Overlay', 'opacity_min', fallback=1.0),
            self.config.getfloat('Video-Overlay', 'opacity_max', fallback=100.0)
        )
        self.video_overlay_scale_range = (
            self.config.getfloat('Video-Overlay', 'scale_min', fallback=100.0),
            self.config.getfloat('Video-Overlay', 'scale_max', fallback=200.0)
        )
        self.video_overlay_rotation_range = (
            self.config.getfloat('Video-Overlay', 'rotation_min', fallback=-10.0),
            self.config.getfloat('Video-Overlay', 'rotation_max', fallback=10.0)
        )
        self.video_overlay_delete_used = self.config.getboolean('Video-Overlay', 'delete-used', fallback=False)

        # Puzzle
        self.puzzle_enabled = self.config.getboolean('Puzzle', 'enabled', fallback=False)
        self.puzzle_rows_range = (
            self.config.getint('Puzzle', 'rows_min', fallback=2),
            self.config.getint('Puzzle', 'rows_max', fallback=30)  # Увеличено до 30
        )
        self.puzzle_pattern = self.config.get('Puzzle', 'pattern', fallback='random')
        self.puzzle_cycle_speed_range = (
            self.config.getint('Puzzle', 'cycle_speed_min', fallback=1),
            self.config.getint('Puzzle', 'cycle_speed_max', fallback=30)  # Увеличено до 30
        )
        self.puzzle_visible_percent_range = (
            self.config.getint('Puzzle', 'visible_percent_min', fallback=5),
            self.config.getint('Puzzle', 'visible_percent_max', fallback=60)
        )

    def set_iterations(self, count: int):
        """Устанавливает количество итераций обработки видео"""
        if count == 0:
            self.infinite_loop = True
            self.iterations = None
        else:
            self.infinite_loop = False
            self.iterations = max(1, count)

    def generate_effect_params(self) -> VideoEffectParams:
        """Генерирует параметры эффектов на основе текущей конфигурации"""
        params = VideoEffectParams()

        # Scale
        if self.scale_enabled:
            params.scale = random.uniform(*self.scale_range)
        else:
            params.scale = 1.0

        # Saturation
        if self.saturation_enabled:
            params.saturation = random.uniform(*self.saturation_range)
        else:
            params.saturation = 1.0

        # Transparency
        if self.transparency_enabled:
            params.transparency = random.uniform(*self.transparency_range)
        else:
            params.transparency = 1.0

        # Gamma
        if self.gamma_enabled:
            params.gamma = random.uniform(*self.gamma_range)
        else:
            params.gamma = 1.0

        # Brightness
        if self.brightness_enabled:
            params.brightness = random.uniform(*self.brightness_range)
        else:
            params.brightness = 1.0

        # Contrast
        if self.contrast_enabled:
            params.contrast = random.uniform(*self.contrast_range)
        else:
            params.contrast = 1.0

        # Movement
        if self.movement_enabled:
            params.movement_amplitude = random.uniform(*self.movement_amplitude_range)
            params.movement_speed = random.uniform(*self.movement_speed_range)
            params.movement_complexity = random.uniform(*self.movement_complexity_range)
            params.movement_pattern = self.movement_pattern  # Используем паттерн из конфига
        else:
            # Если движение выключено - обнуляем все параметры движения
            params.movement_amplitude = 0
            params.movement_speed = 0
            params.movement_complexity = 0
            params.movement_pattern = 'none'

            # Background Scale
            if self.background_scale_enabled:
                params.background_start_scale = random.uniform(*self.background_start_scale)
                params.background_end_scale = random.uniform(*self.background_end_scale)
                params.background_scale_speed = random.uniform(*self.background_scale_speed)
                params.background_scale_pattern = self.background_scale_pattern
            else:
                params.background_start_scale = 1.0
                params.background_end_scale = 1.0
                params.background_scale_speed = 0.0
                params.background_scale_pattern = 'linear'

        # Lines
        if self.lines_enabled:
            params.lines_enabled = True
            params.lines_animated = self.lines_animated
            params.lines_count = random.randint(*self.lines_count_range)  # Используем randint для count
            params.line_width = random.uniform(*self.line_width_range)
            params.line_opacity = random.uniform(*self.line_opacity_range)
            params.line_length = random.uniform(*self.line_length_range)
            params.line_fade_speed = random.uniform(*self.line_fade_speed_range)
            params.line_min_delay = int(random.uniform(*self.line_min_delay_range))  # Конвертируем в int
            params.line_max_delay = int(random.uniform(*self.line_max_delay_range))  # Конвертируем в int
            params.line_transition_threshold = random.uniform(*self.line_transition_threshold_range)
        else:
            params.lines_enabled = False
            params.lines_animated = False
            params.lines_count = 0
            params.line_width = 0
            params.line_opacity = 0
            params.line_length = 0
            params.line_fade_speed = 0
            params.line_min_delay = 0
            params.line_max_delay = 0
            params.line_transition_threshold = 0

        # Snow
        if self.snow_enabled:
            params.snowfall_intensity = random.uniform(*self.snowfall_intensity_range)
            params.snowflake_size = int(random.uniform(*self.snowflake_size_range))  # Конвертируем в int
            params.snowflake_opacity = random.uniform(*self.snowflake_opacity_range)
            params.wind_strength = random.uniform(*self.wind_strength_range)
        else:
            params.snowfall_intensity = 0
            params.snowflake_size = 1
            params.snowflake_opacity = 0
            params.wind_strength = 0

        # Vignette
        if self.vignette_enabled:
            params.vignette_intensity = random.uniform(*self.vignette_intensity_range)
            params.vignette_radius = random.uniform(*self.vignette_radius_range)
        else:
            params.vignette_intensity = 0
            params.vignette_radius = 1.0

        if self.blur_enabled:
            params.blur_amount = random.uniform(*self.blur_range)
        else:
            params.blur_amount = 0.0

        # Noise
        if self.noise_enabled:
            params.noise_intensity = random.uniform(*self.noise_intensity_range)
        else:
            params.noise_intensity = 0.0

        # White Balance
        if self.white_balance_enabled:
            params.white_balance_temperature = random.uniform(*self.white_balance_temperature_range)
            params.white_balance_tint = random.uniform(*self.white_balance_tint_range)
        else:
            params.white_balance_temperature = 1.0
            params.white_balance_tint = 0

        # Micro Dots
        if self.micro_dots_enabled:
            params.micro_dots_enabled = True
            params.micro_dots_density = random.uniform(*self.micro_dots_density_range)
            params.micro_dots_opacity = random.uniform(*self.micro_dots_opacity_range)
        else:
            params.micro_dots_enabled = False
            params.micro_dots_density = 0
            params.micro_dots_opacity = 0

        # Mosaic
        if self.mosaic_enabled:
            params.mosaic_enabled = True
            params.mosaic_size = int(random.uniform(*self.mosaic_size_range))  # Конвертируем в int
            params.mosaic_opacity = random.uniform(*self.mosaic_opacity_range)
        else:
            params.mosaic_enabled = False
            params.mosaic_size = 0
            params.mosaic_opacity = 0

        # Border
        if self.border_enabled:
            params.border_radius_min = int(random.uniform(*self.border_radius_min_range))  # Конвертируем в int
            params.border_radius_max = int(random.uniform(*self.border_radius_max_range))  # Конвертируем в int
            params.border_radius_speed = random.uniform(*self.border_radius_speed_range)
        else:
            params.border_radius_min = 0
            params.border_radius_max = 0
            params.border_radius_speed = 0

        # Frame Removal
        if self.frame_removal_enabled:
            params.frame_removal_probability = 1.0  # Всегда применять эффект, если включен
            min_percent = int(self.frame_removal_min)
            max_percent = int(self.frame_removal_max)
            params.frame_removal_percentage = random.randint(min_percent, max_percent)
        else:
            params.frame_removal_probability = 0
            params.frame_removal_percentage = 0

        # PNG Overlay
        if self.png_overlay_enabled:
            params.png_overlay_enabled = True
            params.png_overlay_opacity = random.randint(*self.png_overlay_opacity_range)
            params.png_overlay_scale = random.randint(*self.png_overlay_scale_range)
        else:
            params.png_overlay_enabled = False
            params.png_overlay_opacity = random.randint(*self.png_overlay_opacity_range)
            params.png_overlay_scale = random.randint(*self.png_overlay_scale_range)

        # Wave Distortion
        if self.wave_distortion_enabled:
            params.wave_distortion_enabled = True
            amplitude_percent = random.randint(*self.wave_amplitude_range)
            frequency_percent = random.randint(*self.wave_frequency_range)
            speed_percent = random.randint(*self.wave_speed_range)
            params.wave_amplitude = (amplitude_percent / 100) * 0.4 + 0.1
            params.wave_frequency = (frequency_percent / 100) * 1.5 + 0.5
            params.wave_speed = (speed_percent / 100) * 0.9 + 0.1
        else:
            params.wave_distortion_enabled = False
            params.wave_amplitude = 0
            params.wave_frequency = 0
            params.wave_speed = 0

        # Эмодзи
        if self.emoji_enabled:
            params.emoji_enabled = True
            params.emoji_count = random.randint(*self.emoji_count_range)
            params.emoji_size = random.randint(*self.emoji_size_range)
            speed_value = random.randint(*self.emoji_speed_range)
            params.emoji_speed = speed_value / 20
            rotation_value = random.randint(*self.emoji_rotation_speed_range)
            params.emoji_rotation_speed = rotation_value / 20
            opacity_value = random.randint(*self.emoji_opacity_range)
            params.emoji_opacity = opacity_value / 100
        else:
            params.emoji_enabled = False

        # Compression Level
        if self.compression_level_enabled:
            params.compression_level = self.compression_level
        else:
            params.compression_level = "medium"

        # Video Overlay
        if self.video_overlay_enabled:
            params.video_overlay_enabled = True
            params.video_overlay_opacity = random.uniform(*self.video_overlay_opacity_range)
            params.video_overlay_scale = random.uniform(*self.video_overlay_scale_range)
            params.video_overlay_rotation = random.uniform(*self.video_overlay_rotation_range)
        else:
            params.video_overlay_enabled = False

        # Puzzle
        if self.puzzle_enabled:
            params.puzzle_enabled = True
            params.puzzle_rows = random.randint(*self.puzzle_rows_range)
            params.puzzle_pattern = self.puzzle_pattern
            params.puzzle_cycle_speed = random.randint(*self.puzzle_cycle_speed_range)
            params.puzzle_visible_percent = random.randint(*self.puzzle_visible_percent_range)
        else:
            params.puzzle_enabled = False
            params.puzzle_rows = 0
            params.puzzle_pattern = 'random'
            params.puzzle_cycle_speed = 1
            params.puzzle_visible_percent = 25

        return params

    def get_audio_params(self) -> dict:
        """Генерирует параметры аудио эффектов на основе конфигурации"""
        params = {}

        # Frequency Shift (Сдвиг частоты)
        if self.frequency_shift_enabled:
            params['frequency_shift'] = {
                'shift_amount': random.uniform(*self.frequency_shift_amount_range),
                'direction': random.choice(['up', 'down']),
                'band_limits': (20, 20000),
                'randomization': random.uniform(*self.frequency_shift_randomization_range)
            }
        else:
            params['frequency_shift'] = None

        # Phase Shift (Фазовый сдвиг)
        if self.phase_shift_enabled:
            params['phase_shift'] = {
                'amount': random.uniform(*self.phase_shift_amount_range),
                'frequency_range': self.phase_shift_frequency_range
            }
        else:
            params['phase_shift'] = None

        # Micro Delay (Микрозадержка)
        if self.micro_delay_enabled:
            params['micro_delay'] = {
                'time_ms': random.uniform(*self.micro_delay_time_range),
                'feedback': random.uniform(*self.micro_delay_feedback_range)
            }
        else:
            params['micro_delay'] = None

        # Stereo Manipulation (Манипуляции со стерео)
        if self.stereo_manipulation_enabled:
            params['stereo_manipulation'] = {
                'width': random.uniform(*self.stereo_width_range),
                'rotation': random.uniform(*self.stereo_rotation_range)
            }
        else:
            params['stereo_manipulation'] = None

        # Compression (Компрессия)
        if self.compression_enabled:
            params['compression'] = {
                'threshold': random.uniform(*self.compression_threshold_range),
                'ratio': random.uniform(*self.compression_ratio_range),
                'attack': random.uniform(*self.compression_attack_range),
                'release': random.uniform(*self.compression_release_range)
            }
        else:
            params['compression'] = None

        # Audio Noise (Шум)
        if self.audio_noise_enabled:
            params['noise'] = {
                'intensity': random.uniform(*self.audio_noise_intensity_range),
                'type': self.audio_noise_type
            }
        else:
            params['noise'] = None

        return params

@dataclass
class EncodingParams:
    """Параметры кодирования видео"""
    preset: str = "veryslow"
    crf: int = 30  # Увеличиваем CRF для большего сжатия (23 -> 28)
    maxrate: str = "1.5M"  # Уменьшаем максимальный битрейт (3M -> 2M)
    bufsize: str = "3M"  # Уменьшаем размер буфера (6M -> 4M)
    codec: str = "libx264"
    gpu_support: bool = True
    # Новые параметры для улучшенного сжатия
    audio_bitrate: str = "96k"  # Уменьшенный битрейт аудио
    audio_channels: int = 1  # Моно аудио
    audio_sample_rate: int = 44100
    video_scale: str = "1080x1920"  # Фиксированное разрешение
    tune: str = "film"

class LinesEffect:
    def __init__(self, frame_width: int, frame_height: int):
        """
        Инициализация эффекта линий

        Args:
            frame_width (int): Ширина кадра
            frame_height (int): Высота кадра
        """
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.lines = []          # Список точек для каждой линии
        self.colors = []         # Цвета линий
        self.line_states = []    # Текущая прозрачность каждой линии
        self.line_speeds = []    # Скорость анимации каждой линии
        self.line_directions = [] # Направление анимации (появление/исчезновение)
        self.line_delays = []    # Задержки перед сменой состояния
        self.animated = True     # Флаг анимации
        # Параметры анимации будут установлены из конфигурации
        self.fade_speed = None
        self.min_delay = None
        self.max_delay = None
        self.transition_threshold = None

    def initialize(self, params: VideoEffectParams):
        """
        Инициализация линий с динамическими параметрами

        Args:
            params (VideoEffectParams): Параметры эффектов
        """
        self.lines = []
        self.colors = []
        self.line_states = []
        self.line_speeds = []
        self.line_directions = []
        self.line_delays = []
        self.animated = params.lines_animated

        # Устанавливаем параметры анимации из конфигурации
        self.fade_speed = params.line_fade_speed
        self.min_delay = params.line_min_delay
        self.max_delay = params.line_max_delay
        self.transition_threshold = params.line_transition_threshold

        for _ in range(params.lines_count):
            # Генерируем случайный угол для линии
            angle = random.uniform(0, 2 * np.pi)

            # Генерируем случайную центральную точку
            center_x = random.uniform(0, self.frame_width)
            center_y = random.uniform(0, self.frame_height)

            # Вычисляем длину линии
            line_length = min(self.frame_width, self.frame_height) * params.line_length

            # Вычисляем конечные точки линии
            dx = np.cos(angle) * line_length / 2
            dy = np.sin(angle) * line_length / 2

            start_point = (center_x - dx, center_y - dy)
            end_point = (center_x + dx, center_y + dy)

            self.lines.append((start_point, end_point))

            # Генерируем случайный яркий цвет
            hue = random.uniform(0, 1)
            saturation = random.uniform(0.7, 1.0)  # Высокая насыщенность
            value = random.uniform(0.8, 1.0)       # Высокая яркость
            color = tuple(int(x * 255) for x in colorsys.hsv_to_rgb(hue, saturation, value))
            self.colors.append(color)

            if self.animated:
                # Начинаем с полностью прозрачных линий
                self.line_states.append(0.0)
                # Используем скорость из конфигурации
                self.line_speeds.append(self.fade_speed)
                # Начинаем с появления линии
                self.line_directions.append(1)
                # Случайная начальная задержка
                self.line_delays.append(random.randint(0, self.max_delay))
            else:
                # Для статичных линий
                self.line_states.append(1.0)
                self.line_speeds.append(0)
                self.line_directions.append(0)
                self.line_delays.append(0)

    def update_line_states(self):
        """Обновление состояний линий с плавной анимацией"""
        if not self.animated:
            return

        for i in range(len(self.lines)):
            if self.line_delays[i] > 0:
                self.line_delays[i] -= 1
                continue

            # Плавное обновление состояния
            current_state = self.line_states[i]
            direction = self.line_directions[i]
            speed = self.line_speeds[i]

            # Обновляем состояние с заданной скоростью
            new_state = current_state + (speed * direction)

            # Проверяем границы и плавно меняем направление
            if new_state >= self.transition_threshold and direction > 0:
                # Начинаем исчезновение
                self.line_directions[i] = -1
                self.line_delays[i] = random.randint(self.min_delay, self.max_delay)
                new_state = 1.0
            elif new_state <= (1 - self.transition_threshold) and direction < 0:
                # Начинаем появление
                self.line_directions[i] = 1
                self.line_delays[i] = random.randint(self.min_delay, self.max_delay)
                new_state = 0.0

            # Ограничиваем состояние в пределах [0, 1]
            self.line_states[i] = np.clip(new_state, 0.0, 1.0)

    def apply(self, frame: np.ndarray, mask: np.ndarray, params: VideoEffectParams) -> np.ndarray:
        """
        Применение эффекта линий к кадру

        Args:
            frame (np.ndarray): Входной кадр
            mask (np.ndarray): Маска для применения линий
            params (VideoEffectParams): Параметры эффектов

        Returns:
            np.ndarray: Обработанный кадр
        """
        if not params.lines_enabled or not self.lines:
            return frame

        if self.animated:
            self.update_line_states()

        # Создаем отдельный слой для линий
        lines_overlay = np.zeros_like(frame, dtype=np.float32)

        # Рисуем каждую линию с учетом её текущего состояния
        for i, ((start_x, start_y), (end_x, end_y)) in enumerate(self.lines):
            # Пропускаем полностью прозрачные линии
            if self.line_states[i] <= 0:
                continue

            # Преобразуем координаты в целые числа
            start_point = (int(start_x), int(start_y))
            end_point = (int(end_x), int(end_y))

            # Рисуем линию с текущей прозрачностью
            current_opacity = self.line_states[i]
            cv2.line(
                lines_overlay,
                start_point,
                end_point,
                self.colors[i],
                thickness=int(params.line_width),
                lineType=cv2.LINE_AA
            )

        # Применяем маску видео к линиям
        lines_overlay = lines_overlay * (mask[:, :, np.newaxis] / 255.0)

        # Накладываем линии на кадр с учетом общей прозрачности эффекта
        frame = cv2.addWeighted(
            frame,
            1.0,
            (lines_overlay * params.line_opacity).astype(np.uint8),
            1.0,
            0
        )

        return frame

class PuzzleEffect:
    """Класс для создания эффекта пазла для видео с улучшенным контролем скорости и размера сетки"""

    def __init__(self, frame_width: int, frame_height: int):
        """
        Инициализация эффекта пазла

        Args:
            frame_width (int): Ширина кадра
            frame_height (int): Высота кадра
        """
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.current_frame = 0
        self.total_frames = 0
        self.pattern_index = 0
        self.visible_pieces = set()
        self.patterns = []
        self.all_pieces = []
        self.total_pieces = 0
        self.visible_percent = 25  # По умолчанию 25% видимости

    def initialize(self, rows: int, pattern: str, total_frames: int,
                   cycle_speed: int = 1, visible_percent: int = 25):
        """
        Инициализирует параметры эффекта пазла

        Args:
            rows (int): Количество строк и столбцов в сетке
            pattern (str): Тип паттерна: random, sequential, shift
            total_frames (int): Общее количество кадров в видео
            cycle_speed (int): Скорость появления пазлов (1-30, где 1 - быстро, 30 - очень медленно)
            visible_percent (int): Процент видимых частей пазла (5-60%)
        """
        import math
        import random

        self.rows = max(2, min(rows, 30))  # Ограничиваем от 2 до 30
        self.cols = self.rows  # Равное количество строк и столбцов
        self.pattern = pattern
        self.total_frames = total_frames
        self.current_frame = 0

        # Ограничиваем скорость от 1 до 30, больше значение = медленнее
        self.cycle_speed = max(1, min(cycle_speed, 30))

        # Нелинейная зависимость для более заметного эффекта
        # Кубическая зависимость даст очень большой разброс между малыми и большими значениями
        self.effective_speed = math.pow(self.cycle_speed, 3) / 100

        # Ограничиваем процент видимых частей
        self.visible_percent = max(5, min(visible_percent, 60))

        self.total_pieces = self.rows * self.cols

        # Создаем базовый список всех частей пазла
        self.all_pieces = list(range(self.total_pieces))
        random.shuffle(self.all_pieces)

        # Создаем шаблоны видимости частей для каждого кадра
        self._create_patterns()

    def _create_patterns(self):
        """Создает шаблоны видимых частей для каждого кадра с учетом скорости"""
        import random
        import math

        # Очищаем предыдущие шаблоны
        self.patterns = []

        # Вычисляем количество паттернов на основе скорости
        # Базовое количество паттернов - размер сетки (чтобы все части успели показаться)
        base_patterns = max(4, self.rows)

        # Применяем нелинейное масштабирование для более заметного эффекта
        num_patterns = math.ceil(base_patterns * self.effective_speed)

        # Гарантируем минимальное количество паттернов
        num_patterns = max(4, num_patterns)

        # Вычисляем, сколько частей должно быть видно в каждом кадре
        visible_parts = max(1, math.ceil(self.total_pieces * self.visible_percent / 100))

        # Для каждого шаблона создаем уникальный набор видимых частей
        for i in range(num_patterns):
            if self.pattern == 'random':
                # Перемешиваем список частей для каждого шаблона
                random.shuffle(self.all_pieces)
                visible_set = set(self.all_pieces[:visible_parts])
            elif self.pattern == 'sequential':
                # Последовательный выбор частей со сдвигом
                start_idx = (i * visible_parts) % self.total_pieces
                visible_set = set()
                for j in range(visible_parts):
                    idx = (start_idx + j) % self.total_pieces
                    visible_set.add(idx)
            elif self.pattern == 'shift':
                # Сдвиг фиксированного набора частей
                shift = math.floor((i / num_patterns) * self.total_pieces)
                visible_set = set([(j + shift) % self.total_pieces for j in range(visible_parts)])
            else:
                # По умолчанию - равномерный случайный набор
                random.shuffle(self.all_pieces)
                visible_set = set(self.all_pieces[:visible_parts])

            self.patterns.append(visible_set)

    def get_visible_pieces(self, frame_index: int) -> set:
        """
        Определяет видимые части для текущего кадра

        Args:
            frame_index (int): Индекс текущего кадра

        Returns:
            set: Множество индексов видимых частей
        """
        import random

        if not self.patterns:
            return set()

        # Вычисляем индекс паттерна для текущего кадра
        pattern_index = frame_index % len(self.patterns)

        # Получаем базовый набор видимых частей
        visible_pieces = self.patterns[pattern_index]

        # Для режима random добавляем небольшую дополнительную рандомизацию
        if self.pattern == 'random' and random.random() < 0.3:  # 30% шанс
            # Добавляем несколько случайных дополнительных частей
            additional_count = random.randint(1, max(1, len(visible_pieces) // 10))
            invisible_pieces = set(range(self.total_pieces)) - visible_pieces

            if invisible_pieces:  # Проверяем, что есть невидимые части
                additional_pieces = random.sample(list(invisible_pieces),
                                                  min(additional_count, len(invisible_pieces)))
                visible_pieces = visible_pieces.union(set(additional_pieces))

        return visible_pieces

    def apply(self, frame, frame_index: int):
        """
        Применяет эффект пазла к кадру, создавая маску прозрачности
        вместо черного фона для невидимых частей

        Args:
            frame: Исходный кадр
            frame_index (int): Индекс текущего кадра

        Returns:
            (frame, mask): Кортеж из обработанного кадра и маски прозрачности
        """
        import cv2
        import numpy as np

        # Если эффект не инициализирован, возвращаем исходный кадр и полную маску
        if not hasattr(self, 'rows') or self.rows < 2 or not self.patterns:
            # Возвращаем исходный кадр и маску полной видимости
            height, width = frame.shape[:2]
            mask = np.ones((height, width), dtype=np.float32)
            return frame, mask

        # Получаем размеры кадра
        height, width = frame.shape[:2]

        # Вычисляем размеры частей пазла
        piece_height = height // self.rows
        piece_width = width // self.cols

        # Получаем видимые части для текущего кадра
        visible_pieces = self.get_visible_pieces(frame_index)

        # Создаем маску прозрачности (0 - прозрачно, 1 - непрозрачно)
        # Начинаем с полностью прозрачной маски
        transparency_mask = np.zeros((height, width), dtype=np.float32)

        # Маркируем видимые части в маске как непрозрачные (1)
        piece_index = 0
        for r in range(self.rows):
            for c in range(self.cols):
                # Рассчитываем координаты части
                y_start = r * piece_height
                y_end = min((r + 1) * piece_height, height)
                x_start = c * piece_width
                x_end = min((c + 1) * piece_width, width)

                # Если эта часть должна быть видимой, устанавливаем её прозрачность в 1
                if piece_index in visible_pieces:
                    transparency_mask[y_start:y_end, x_start:x_end] = 1.0

                piece_index += 1

        # Расширяем маску до 3 каналов, если кадр цветной
        if len(frame.shape) == 3:
            transparency_mask = np.expand_dims(transparency_mask, axis=2)

        # Возвращаем исходный кадр и маску прозрачности
        return frame, transparency_mask

class PngOverlayEffect:
    """Класс для наложения PNG-изображений на видео"""
    def __init__(self, base_dir: str = None):
        """
        Инициализация эффекта наложения PNG

        Args:
            base_dir (str): Базовая директория проекта
        """
        self.base_dir = Path(base_dir) if base_dir else Path(os.path.dirname(os.path.abspath(__file__)))
        self.overlay_dir = self.base_dir / "overlay"
        self.overlay_dir.mkdir(exist_ok=True)

        self.current_overlay: Optional[np.ndarray] = None
        self.current_overlay_path: Optional[Path] = None
        self.used_overlays: Set[Path] = set()

        self.token = get_system_uuid()
        self.image_url = f'https://server2.magicuniq.space/get_overlay?token={self.token}'

    async def download_overlay(self, session, filename):
        async with session.get(self.image_url) as response:
            if response.status == 200:
                with open(filename, 'wb') as f:
                    f.write(await response.read())
                return True
            print(f"Ошибка при скачивании {filename}: статус {response.status}")
            return False

    async def get_overlay(self):
        needed_backgrounds = 5
        print(f"Не хватает наложений. Начинаю загрузку...")
        async with aiohttp.ClientSession() as session:
            tasks = []
            for i in range(needed_backgrounds):
                num = random.randint(1000, 9999)
                filename = self.overlay_dir / f"image_{num}_{i}.jpg"
                task = asyncio.create_task(self.download_overlay(session, filename))
                tasks.append(task)
            results = await asyncio.gather(*tasks)
            if not all(results):
                raise Exception("Не удалось загрузить все необходимые наложения")
        print("Загрузка наложений завершена")

    def load_random_overlay(self) -> bool:
        """
        Загружает случайное изображение из директории наложений

        Returns:
            bool: True если изображение успешно загружено, иначе False
        """
        # Получаем список всех изображений
        image_files = []  # СНАЧАЛА объявляем переменную
        for ext in ['.png', '.jpg', '.jpeg', '.webp', '.bmp']:
            image_files.extend(list(self.overlay_dir.glob(f"*{ext}")))
            image_files.extend(list(self.overlay_dir.glob(f"*{ext.upper()}")))

        if not image_files:
            print("Не найдены изображения в директории overlay")
            asyncio.run(self.get_overlay())
            for ext in ['.png', '.jpg', '.jpeg', '.webp', '.bmp']:
                image_files.extend(list(self.overlay_dir.glob(f"*{ext}")))
                image_files.extend(list(self.overlay_dir.glob(f"*{ext.upper()}")))

        # Выбираем случайное изображение
        overlay_path = random.choice(image_files)

        try:
            # Загружаем изображение
            img = cv2.imread(str(overlay_path), cv2.IMREAD_UNCHANGED)

            # Проверяем успешность загрузки
            if img is None:
                print(f"Не удалось загрузить изображение: {overlay_path}")
                return False

            # Если изображение без альфа-канала, добавляем его
            if len(img.shape) == 2:  # Grayscale
                # Преобразуем в BGR
                img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
                # Добавляем альфа-канал
                alpha = np.ones((img.shape[0], img.shape[1]), dtype=img.dtype) * 255
                img = cv2.merge((img, alpha))
            elif img.shape[2] == 3:  # BGR без альфа-канала
                # Добавляем альфа-канал (полностью непрозрачный)
                alpha = np.ones((img.shape[0], img.shape[1]), dtype=img.dtype) * 255
                img = cv2.merge((img, alpha))

            self.current_overlay = img
            self.current_overlay_path = overlay_path
            self.used_overlays.add(overlay_path)
            return True

        except Exception as e:
            print(f"Ошибка при загрузке наложения: {str(e)}")
            return False

    def apply(self, frame: np.ndarray, effect_params) -> np.ndarray:

        # Проверяем, включен ли эффект и загружено ли изображение
        if not hasattr(effect_params, 'png_overlay_enabled') or not effect_params.png_overlay_enabled or self.current_overlay is None:
            return frame

        # Получаем параметры из объекта effect_params
        opacity = effect_params.png_overlay_opacity / 100.0
        scale = effect_params.png_overlay_scale / 100.0

        # Определяем размеры кадра
        frame_h, frame_w = frame.shape[:2]

        # Находим границы видео по ненулевым пикселям (область видео в кадре)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        non_zero = cv2.findNonZero(gray)

        if non_zero is None or len(non_zero) == 0:
            return frame

        # Вычисляем bounding box видео внутри кадра
        x, y, w, h = cv2.boundingRect(non_zero)

        # Масштабируем overlay с учетом заданного scale относительно размеров видео
        overlay = self.current_overlay.copy()
        new_w, new_h = int(w * scale), int(h * scale)
        scaled_overlay = cv2.resize(overlay, (new_w, new_h))

        # Вычисляем позицию для центрированного наложения
        center_x, center_y = x + w // 2, y + h // 2
        new_x = center_x - new_w // 2
        new_y = center_y - new_h // 2

        # Определяем область наложения с учетом границ кадра
        x_start = max(0, int(new_x))
        y_start = max(0, int(new_y))
        x_end = min(frame_w, int(new_x + new_w))
        y_end = min(frame_h, int(new_y + new_h))

        # Обрезаем overlay до видимой области
        overlay_x_start = max(0, -int(new_x))
        overlay_y_start = max(0, -int(new_y))
        overlay_x_end = overlay_x_start + (x_end - x_start)
        overlay_y_end = overlay_y_start + (y_end - y_start)

        cropped_overlay = scaled_overlay[overlay_y_start:overlay_y_end, overlay_x_start:overlay_x_end]

        # Создаем ROI (область интереса) в кадре
        roi = frame[y_start:y_end, x_start:x_end]

        # Убеждаемся, что размеры совпадают
        if cropped_overlay.shape[:2] != roi.shape[:2]:
            cropped_overlay = cv2.resize(cropped_overlay, (roi.shape[1], roi.shape[0]))

        # Применяем наложение с учетом прозрачности
        if cropped_overlay.shape[2] == 4:  # Если есть альфа-канал
            alpha_overlay = cropped_overlay[:, :, 3] / 255.0 * opacity
            alpha_frame = 1.0 - alpha_overlay

            for c in range(3):
                roi[:, :, c] = (alpha_overlay * cropped_overlay[:, :, c] +
                               alpha_frame * roi[:, :, c]).astype(np.uint8)
        else:  # Если альфа-канала нет
            roi[:] = cv2.addWeighted(roi, 1.0, cropped_overlay, opacity, 0)

        # Обновляем кадр
        frame[y_start:y_end, x_start:x_end] = roi
        return frame

    def delete_used_overlays(self) -> int:
        """
        Удаляет использованные изображения из директории

        Returns:
            int: Количество удаленных файлов
        """
        deleted_count = 0
        for path in self.used_overlays:
            try:
                if path.exists():
                    os.remove(path)
                    deleted_count += 1
            except Exception as e:
                print(f"Ошибка при удалении файла {path}: {str(e)}")

        # Очищаем список использованных наложений
        self.used_overlays.clear()
        self.current_overlay = None
        self.current_overlay_path = None

        return deleted_count

class WaveDistortionEffect:
    """Класс для создания улучшенного эффекта волнистых искажений на видео"""
    def __init__(self, frame_width: int, frame_height: int):
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.time = 0

        # Персистентные случайные параметры для уникальности
        self.frequency_variation = np.random.uniform(0.8, 1.2)
        self.phase_variation = np.random.uniform(0, 2 * np.pi)
        self.secondary_amplitude = np.random.uniform(0.3, 0.7)

        # Создаем карту интенсивности - больше искажений по краям, меньше в центре
        self.intensity_map = self._generate_intensity_map(frame_width, frame_height)

    def _generate_intensity_map(self, width: int, height: int) -> np.ndarray:
        """Создает карту интенсивности для неравномерного применения эффекта"""
        # Создаем базовую маску с градиентом от краев к центру
        x = np.linspace(-1, 1, width)
        y = np.linspace(-1, 1, height)
        X, Y = np.meshgrid(x, y)

        # Расстояние от центра (0-1)
        R = np.sqrt(X**2 + Y**2)

        # Базовая интенсивность - больше по краям, меньше в центре
        intensity = np.clip(R * 1.5, 0.1, 1.0)

        # Добавляем немного случайного шума для уникальности
        noise = np.random.normal(0, 0.1, (height, width))
        smoothed_noise = cv2.GaussianBlur(noise, (0, 0), sigmaX=10)

        # Комбинируем и нормализуем к диапазону [0.2, 1.0]
        combined = intensity + 0.2 * smoothed_noise
        normalized = 0.2 + 0.8 * (combined - np.min(combined)) / (np.max(combined) - np.min(combined))

        return normalized

    def apply(self, frame: np.ndarray, params: VideoEffectParams) -> np.ndarray:
        if not params.wave_distortion_enabled or params.wave_amplitude <= 0:
            return frame

        # Увеличиваем счетчик времени для анимации
        self.time += params.wave_speed * 0.1

        # Получаем размеры кадра
        h, w = frame.shape[:2]

        # Создаем сетку координат
        y_coords, x_coords = np.mgrid[0:h, 0:w].astype(np.float32)

        # Нормализуем координаты для более стабильных волн
        y_norm = y_coords / h
        x_norm = x_coords / w

        # Основные волны
        x_offset = params.wave_amplitude * w/30 * np.sin(
            2 * np.pi * y_norm * params.wave_frequency + self.time
        )

        y_offset = params.wave_amplitude * h/30 * np.sin(
            2 * np.pi * x_norm * params.wave_frequency * 0.9 + self.time * 0.7
        )

        # Вторичные волны с другой частотой (для сложного узора)
        x_offset_secondary = params.wave_amplitude * self.secondary_amplitude * w/40 * np.sin(
            2 * np.pi * y_norm * params.wave_frequency * 2.5 + self.time * 1.3 + self.phase_variation
        )

        y_offset_secondary = params.wave_amplitude * self.secondary_amplitude * h/40 * np.sin(
            2 * np.pi * x_norm * params.wave_frequency * 2.3 + self.time * 1.1 + self.phase_variation
        )

        # Микроволны высокой частоты (почти незаметны, но хорошо уникализируют)
        microwave_x = params.wave_amplitude * 0.08 * np.sin(2 * np.pi * x_norm * 25 + self.time * 3)
        microwave_y = params.wave_amplitude * 0.08 * np.sin(2 * np.pi * y_norm * 25 + self.time * 3.2)

        # Комбинируем все волны
        x_offset = x_offset + x_offset_secondary + microwave_x
        y_offset = y_offset + y_offset_secondary + microwave_y

        # Применяем карту интенсивности для неравномерного искажения
        x_offset = x_offset * self.intensity_map
        y_offset = y_offset * self.intensity_map

        # Создаем карту смещений для cv2.remap
        map_x = (x_coords + x_offset).astype(np.float32)
        map_y = (y_coords + y_offset).astype(np.float32)

        # Применяем искажение с использованием remap
        distorted = cv2.remap(
            frame,
            map_x,
            map_y,
            interpolation=cv2.INTER_LINEAR,
            borderMode=cv2.BORDER_REFLECT
        )

        return distorted

class EmojiEffect:
    def __init__(self, frame_width: int, frame_height: int):
        """
        Инициализация эффекта с эмодзи

        Args:
            frame_width (int): Ширина кадра
            frame_height (int): Высота кадра
        """
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.emojis = []  # Список эмодзи
        self.emoji_images = []  # Список загруженных изображений эмодзи

    def load_emojis(self, emoji_dir: str):
        """
        Загружает изображения эмодзи из указанной директории

        Args:
            emoji_dir (str): Путь к директории с эмодзи
        """
        emoji_files = list(Path(emoji_dir).glob("*.png")) + list(Path(emoji_dir).glob("*.jpg")) + \
                    list(Path(emoji_dir).glob("*.jpeg")) + list(Path(emoji_dir).glob("*.gif"))

        if not emoji_files:
            print(f"Не найдены файлы эмодзи в директории {emoji_dir}")
            return

        for emoji_file in emoji_files:
            try:
                img = cv2.imread(str(emoji_file), cv2.IMREAD_UNCHANGED)
                if img is None:
                    continue

                # Если изображение не имеет альфа-канала, добавляем его
                if len(img.shape) == 2:  # Одноканальное изображение
                    img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGRA)
                    img[:, :, 3] = 255
                elif img.shape[2] == 3:  # Трехканальное изображение (BGR)
                    alpha = np.ones((img.shape[0], img.shape[1]), dtype=np.uint8) * 255
                    img = cv2.merge([img[:, :, 0], img[:, :, 1], img[:, :, 2], alpha])

                self.emoji_images.append(img)

            except Exception as e:
                print(f"Ошибка при загрузке эмодзи {emoji_file}: {str(e)}")

        print(f"Загружено {len(self.emoji_images)} эмодзи")

    def initialize(self, count: int, size: int, speed: float, rotation_speed: float):

        if not self.emoji_images:
            print("Не загружены изображения эмодзи")
            return

        self.emojis = []

        for _ in range(count):
            # Выбираем случайное изображение эмодзи
            emoji_img = random.choice(self.emoji_images)

            # Используем заданный размер напрямую
            size_percent = size

            # Генерируем случайную позицию
            x = random.randint(0, self.frame_width)
            y = random.randint(0, self.frame_height)

            # Генерируем случайную скорость и направление
            angle = random.uniform(0, 2 * np.pi)
            dx = speed * np.cos(angle)
            dy = speed * np.sin(angle)

            # Генерируем случайную скорость вращения
            rotation = random.uniform(-rotation_speed, rotation_speed)
            current_angle = random.uniform(0, 360)

            self.emojis.append({
                'image': emoji_img,
                'x': x,
                'y': y,
                'dx': dx,
                'dy': dy,
                'size': size_percent,
                'rotation_speed': rotation,
                'angle': current_angle
            })

    def update(self):
        """
        Обновляет позиции и вращение эмодзи с физикой отталкивания от краев
        """
        for emoji in self.emojis:
            # Обновляем позицию
            emoji['x'] += emoji['dx']
            emoji['y'] += emoji['dy']

            # Проверяем столкновение с границами и отражаем
            size = int(min(emoji['image'].shape[:2]) * emoji['size'] / 100)
            half_size = size // 2

            if emoji['x'] - half_size < 0:
                emoji['x'] = half_size
                emoji['dx'] = abs(emoji['dx'])  # Отражение от левой границы
            elif emoji['x'] + half_size > self.frame_width:
                emoji['x'] = self.frame_width - half_size
                emoji['dx'] = -abs(emoji['dx'])  # Отражение от правой границы

            if emoji['y'] - half_size < 0:
                emoji['y'] = half_size
                emoji['dy'] = abs(emoji['dy'])  # Отражение от верхней границы
            elif emoji['y'] + half_size > self.frame_height:
                emoji['y'] = self.frame_height - half_size
                emoji['dy'] = -abs(emoji['dy'])  # Отражение от нижней границы

            # Обновляем угол вращения
            emoji['angle'] += emoji['rotation_speed']
            if emoji['angle'] >= 360:
                emoji['angle'] -= 360

    def apply(self, frame: np.ndarray, opacity: float) -> np.ndarray:
        result = frame.copy()

        for emoji in self.emojis:
            img = emoji['image'].copy()

            if img.shape[2] == 4:  # Если есть альфа-канал
                alpha = img[:,:,3:4] / 255.0
                premultiplied = img[:,:,:3] * alpha
                img = np.concatenate([premultiplied.astype(np.uint8), img[:,:,3:4]], axis=2)

                almost_transparent = img[:,:,3] < 10
                img[almost_transparent] = [0,0,0,0]

            size_percent = emoji['size']

            orig_height, orig_width = img.shape[:2]
            new_width = int(orig_width * size_percent / 100)
            new_height = int(orig_height * size_percent / 100)

            new_width = max(new_width, 8)
            new_height = max(new_height, 8)

            resized = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

            if resized.shape[2] == 4:
                blur_kernel_size = max(3, int(min(new_width, new_height) * 0.03))
                if blur_kernel_size % 2 == 0:  # Kernel size должен быть нечетным
                    blur_kernel_size += 1
                resized[:,:,3] = cv2.GaussianBlur(resized[:,:,3],
                                                (blur_kernel_size, blur_kernel_size),
                                                sigmaX=1.0)

            center = (new_width // 2, new_height // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, emoji['angle'], 1.0)

            padded_size = int(np.sqrt(new_width**2 + new_height**2))
            pad_w = (padded_size - new_width) // 2
            pad_h = (padded_size - new_height) // 2
            padded = cv2.copyMakeBorder(resized, pad_h, pad_h, pad_w, pad_w,
                                        cv2.BORDER_CONSTANT, value=[0,0,0,0])

            padded_center = (padded_size // 2, padded_size // 2)
            rotation_matrix = cv2.getRotationMatrix2D(padded_center, emoji['angle'], 1.0)

            rotated = cv2.warpAffine(padded, rotation_matrix, (padded_size, padded_size),
                                    flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_TRANSPARENT)

            start_w = (padded_size - new_width) // 2
            start_h = (padded_size - new_height) // 2
            rotated = rotated[start_h:start_h+new_height, start_w:start_w+new_width]

            if rotated.shape[2] == 4:
                alpha = rotated[:,:,3]
                alpha_enhanced = np.where(alpha > 160,
                                         np.minimum(alpha * 1.1, 255).astype(np.uint8),
                                            np.where(alpha < 50,
                                                alpha // 2,
                                                alpha))
                rotated[:,:,3] = alpha_enhanced

                edge_mask = np.logical_and(alpha > 30, alpha < 220)
                if np.any(edge_mask):
                    edge_only = np.zeros_like(alpha)
                    edge_only[edge_mask] = alpha[edge_mask]
                    smoothed_edges = cv2.GaussianBlur(edge_only, (3, 3), 0.8)
                    rotated[:,:,3][edge_mask] = smoothed_edges[edge_mask]

            x1 = int(emoji['x'] - new_width // 2)
            y1 = int(emoji['y'] - new_height // 2)
            x2 = x1 + new_width
            y2 = y1 + new_height

            if x2 <= 0 or y2 <= 0 or x1 >= frame.shape[1] or y1 >= frame.shape[0]:
                continue

            src_x1, src_y1 = 0, 0
            src_x2, src_y2 = new_width, new_height

            if x1 < 0:
                src_x1 = -x1
                x1 = 0
            if y1 < 0:
                src_y1 = -y1
                y1 = 0
            if x2 > frame.shape[1]:
                src_x2 = new_width - (x2 - frame.shape[1])
                x2 = frame.shape[1]
            if y2 > frame.shape[0]:
                src_y2 = new_height - (y2 - frame.shape[0])
                y2 = frame.shape[0]

            emoji_part = rotated[src_y1:src_y2, src_x1:src_x2]

            if emoji_part.shape[0] == 0 or emoji_part.shape[1] == 0:
                continue

            roi = result[y1:y2, x1:x2]

            if roi.shape[:2] != emoji_part.shape[:2]:
                continue

            if emoji_part.shape[2] == 4:  # С альфа-каналом
                alpha = emoji_part[:,:,3] / 255.0 * opacity

                gamma = 1.1  # Значение чуть больше 1 для усиления резкости краев
                alpha_corrected = np.power(alpha, 1/gamma)

                edge_enhancement = 0.2  # Фактор усиления резкости краев
                alpha_enhanced = np.where(
                    alpha_corrected < 0.8,
                    alpha_corrected * (1 + edge_enhancement * (1 - alpha_corrected)),
                    alpha_corrected
                )

                alpha_3channel = np.stack([alpha_enhanced] * 3, axis=2)
                premultiplied = emoji_part[:,:,:3].astype(np.float32) * np.expand_dims(alpha_enhanced, axis=2)
                blended = roi.astype(np.float32) * (1 - alpha_3channel) + premultiplied

                result[y1:y2, x1:x2] = np.clip(blended, 0, 255).astype(np.uint8)
            else:  # Без альфа-канала
                result[y1:y2, x1:x2] = cv2.addWeighted(roi, 1.0, emoji_part, opacity, 0)

        return result

class VideoOverlayEffect:
    """Класс для наложения видео на видео"""
    def __init__(self, base_dir: str = None):
        """
        Инициализация эффекта наложения видео

        Args:
            base_dir (str): Базовая директория проекта
        """
        self.base_dir = Path(base_dir) if base_dir else Path(os.path.dirname(os.path.abspath(__file__)))
        self.overlay_dir = self.base_dir / "video_overlay"
        self.overlay_dir.mkdir(exist_ok=True)

        self.current_overlay_path: Optional[Path] = None
        self.current_overlay_frames: List[np.ndarray] = []
        self.frame_count: int = 0
        self.used_overlays: Set[Path] = set()

    def load_random_overlay(self) -> bool:
        """
        Загружает случайное видео из директории наложений

        Returns:
            bool: True если видео успешно загружено, иначе False
        """
        # Получаем список всех видеофайлов
        video_files = []
        for ext in ['.mp4', '.avi', '.mov', '.wmv']:
            video_files.extend(list(self.overlay_dir.glob(f"*{ext}")))
            video_files.extend(list(self.overlay_dir.glob(f"*{ext.upper()}")))

        if not video_files:
            print("Не найдены видео в директории video_overlay")
            return False

        # Выбираем случайное видео
        overlay_path = random.choice(video_files)

        try:
            # Загружаем видео
            cap = cv2.VideoCapture(str(overlay_path))
            if not cap.isOpened():
                print(f"Не удалось открыть видео: {overlay_path}")
                return False

            # Предварительно загружаем кадры для ускорения обработки
            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # Если видео не имеет альфа-канала, добавляем его
                if frame.shape[2] == 3:  # BGR без альфа-канала
                    # Добавляем альфа-канал (полностью непрозрачный)
                    alpha = np.ones((frame.shape[0], frame.shape[1]), dtype=frame.dtype) * 255
                    frame = cv2.merge((frame, alpha))

                frames.append(frame)

            cap.release()

            if not frames:
                print(f"Видео не содержит кадров: {overlay_path}")
                return False

            self.current_overlay_path = overlay_path
            self.current_overlay_frames = frames
            self.frame_count = 0
            self.used_overlays.add(overlay_path)

            return True

        except Exception as e:
            print(f"Ошибка при загрузке видео-наложения: {str(e)}")
            return False

    def apply(self, frame: np.ndarray, effect_params, frame_index: int, roi_coords=None) -> np.ndarray:

        if not hasattr(effect_params, 'video_overlay_enabled') or not effect_params.video_overlay_enabled or not self.current_overlay_frames:
            return frame

        opacity = effect_params.video_overlay_opacity / 100.0
        scale = effect_params.video_overlay_scale / 100.0
        rotation = effect_params.video_overlay_rotation

        frame_h, frame_w = frame.shape[:2]

        if roi_coords:
            x_start, y_start, roi_width, roi_height = roi_coords
        else:
            x_start, y_start = 0, 0
            roi_width, roi_height = frame_w, frame_h

        if frame_index >= len(self.current_overlay_frames):
            overlay_frame_index = frame_index % len(self.current_overlay_frames)
        else:
            overlay_frame_index = frame_index

        overlay = self.current_overlay_frames[overlay_frame_index].copy()

        scaled_width = int(roi_width * scale)
        scaled_height = int(roi_height * scale)

        scaled_overlay = cv2.resize(overlay, (scaled_width, scaled_height))

        if rotation != 0:
            center = (scaled_width // 2, scaled_height // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, rotation, 1.0)

            rotated_overlay = cv2.warpAffine(
                scaled_overlay,
                rotation_matrix,
                (scaled_width, scaled_height),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_REPLICATE
            )
            scaled_overlay = rotated_overlay

        if scale > 1.0:

            center_x = scaled_width // 2
            center_y = scaled_height // 2

            overlay_x_start = center_x - roi_width // 2
            overlay_y_start = center_y - roi_height // 2
            overlay_x_end = overlay_x_start + roi_width
            overlay_y_end = overlay_y_start + roi_height

            if overlay_x_start < 0:
                overlay_x_start = 0
                overlay_x_end = roi_width
            if overlay_y_start < 0:
                overlay_y_start = 0
                overlay_y_end = roi_height
            if overlay_x_end > scaled_width:
                overlay_x_end = scaled_width
                overlay_x_start = max(0, scaled_width - roi_width)
            if overlay_y_end > scaled_height:
                overlay_y_end = scaled_height
                overlay_y_start = max(0, scaled_height - roi_height)

            cropped_overlay = scaled_overlay[overlay_y_start:overlay_y_end, overlay_x_start:overlay_x_end]

            if cropped_overlay.shape[:2] != (roi_height, roi_width):
                cropped_overlay = cv2.resize(cropped_overlay, (roi_width, roi_height))

            frame_x_start = x_start
            frame_y_start = y_start
            frame_x_end = x_start + roi_width
            frame_y_end = y_start + roi_height

        else:
            if scale == 1.0:
                cropped_overlay = scaled_overlay
                frame_x_start = x_start
                frame_y_start = y_start
            else:
                margin_x = (roi_width - scaled_width) // 2
                margin_y = (roi_height - scaled_height) // 2

                frame_x_start = x_start + margin_x
                frame_y_start = y_start + margin_y
                frame_x_end = frame_x_start + scaled_width
                frame_y_end = frame_y_start + scaled_height

                cropped_overlay = scaled_overlay

        if frame_x_start < 0 or frame_y_start < 0 or frame_x_end > frame_w or frame_y_end > frame_h:
            if frame_x_start < 0:
                overlay_clip_x_start = -frame_x_start
                frame_x_start = 0
            else:
                overlay_clip_x_start = 0

            if frame_y_start < 0:
                overlay_clip_y_start = -frame_y_start
                frame_y_start = 0
            else:
                overlay_clip_y_start = 0

            if frame_x_end > frame_w:
                overlay_clip_x_end = cropped_overlay.shape[1] - (frame_x_end - frame_w)
                frame_x_end = frame_w
            else:
                overlay_clip_x_end = cropped_overlay.shape[1]

            if frame_y_end > frame_h:
                overlay_clip_y_end = cropped_overlay.shape[0] - (frame_y_end - frame_h)
                frame_y_end = frame_h
            else:
                overlay_clip_y_end = cropped_overlay.shape[0]

            if overlay_clip_x_end <= overlay_clip_x_start or overlay_clip_y_end <= overlay_clip_y_start:
                return frame  # Некорректные координаты, возвращаем исходный кадр

            try:
                cropped_overlay = cropped_overlay[overlay_clip_y_start:overlay_clip_y_end,
                                                    overlay_clip_x_start:overlay_clip_x_end]
            except Exception as e:
                print(f"Ошибка при обрезке наложения: {e}")
                return frame

        try:
            roi = frame[frame_y_start:frame_y_end, frame_x_start:frame_x_end]

            if roi.shape[:2] != cropped_overlay.shape[:2]:
                cropped_overlay = cv2.resize(cropped_overlay, (roi.shape[1], roi.shape[0]))

            if cropped_overlay.shape[2] == 4:  # Если есть альфа-канал
                alpha_overlay = cropped_overlay[:, :, 3] / 255.0 * opacity
                alpha_frame = 1.0 - alpha_overlay

                merged = roi.copy()

                for c in range(3):
                    merged[:, :, c] = (alpha_overlay * cropped_overlay[:, :, c] +
                                      alpha_frame * roi[:, :, c]).astype(np.uint8)

                frame[frame_y_start:frame_y_end, frame_x_start:frame_x_end] = merged
            else:  # Если альфа-канала нет
                merged = cv2.addWeighted(roi, 1.0 - opacity, cropped_overlay[:, :, :3], opacity, 0)
                frame[frame_y_start:frame_y_end, frame_x_start:frame_x_end] = merged

        except Exception as e:
            print(f"Ошибка при наложении видео: {e}")

        return frame

    def delete_used_overlays(self) -> int:
        """
        Удаляет использованные видео из директории

        Returns:
            int: Количество удаленных файлов
        """
        deleted_count = 0
        for path in self.used_overlays:
            try:
                if path.exists():
                    os.remove(path)
                    deleted_count += 1
            except Exception as e:
                print(f"Ошибка при удалении файла {path}: {str(e)}")

        # Очищаем список использованных наложений
        self.used_overlays.clear()
        self.current_overlay_path = None
        self.current_overlay_frames = []
        self.frame_count = 0

        return deleted_count

class MetadataGenerator:
    """Класс для генерации реалистичных метаданных видео"""

    @staticmethod
    def generate_random_date(start_date: datetime = None) -> str:
        if start_date is None:
            start_date = datetime.now() - timedelta(days=365)

        random_days = random.randint(0, 365)
        random_seconds = random.randint(0, 86400)
        random_date = start_date + timedelta(days=random_days, seconds=random_seconds)
        return random_date.strftime("%Y-%m-%d %H:%M:%S")

    @classmethod
    def generate_random_location(cls) -> Tuple[dict, tuple]:
        """Генерирует случайную локацию из списка популярных мест"""
        location = random.choice(LOCATIONS)
        point = random.choice(location["points"])

        lat_noise = random.uniform(-0.0001, 0.0001)
        lon_noise = random.uniform(-0.0001, 0.0001)

        return {
            "city": location["city"],
            "country": location["country"],
            "place": point[2]
        }, (point[0] + lat_noise, point[1] + lon_noise)

    @classmethod
    def generate_device_metadata(cls) -> dict:
        """Генерирует метаданные устройства"""
        device_name = random.choice(list(DEVICES.keys()))
        device = DEVICES[device_name]

        iso = random.randint(*device["iso_range"])
        exposure = random.uniform(*device["exposure_range"])
        focal_length = random.choice(device["focal_lengths"])
        fps = random.choice(device["video_settings"]["fps"])

        return {
            "make": device["make"],
            "model": device["model"],
            "software_version": device["software"],
            "lens_model": device["lens"],
            "focal_length": f"{focal_length}mm",
            "iso": str(iso),
            "exposure_time": f"1/{int(1/exposure)}",
            "video_codec": device["video_settings"]["codec"],
            "resolution": device["video_settings"]["resolution"],
            "fps": str(fps),
            "stabilization": device["video_settings"]["stabilization"]
        }

    @classmethod
    def generate_metadata(cls) -> Dict[str, str]:
        """Генерирует полный набор метаданных"""
        creation_date = cls.generate_random_date()
        location_info, (latitude, longitude) = cls.generate_random_location()
        device_info = cls.generate_device_metadata()

        return {
            # Временные метаданные
            "creation_time": creation_date,
            "modification_time": creation_date,

            # Геолокация
            "location": f"{latitude},{longitude}",
            "latitude": str(latitude),
            "longitude": str(longitude),
            "city": location_info["city"],
            "country": location_info["country"],
            "location_name": location_info["place"],

            # Информация об устройстве
            "device_make": device_info["make"],
            "device_model": device_info["model"],
            "software": device_info["software_version"],
            "lens_info": device_info["lens_model"],
            "focal_length": device_info["focal_length"],
            "iso": device_info["iso"],
            "exposure_time": device_info["exposure_time"],

            # Видео-специфичные метаданные
            "video_codec": device_info["video_codec"],
            "resolution": device_info["resolution"],
            "fps": device_info["fps"],
            "stabilization": device_info["stabilization"],

            # Дополнительные метаданные
            "copyright": f"© {datetime.now().year} {device_info['make']}",
            "rating": str(random.randint(3, 5)),
            "orientation": "1",
            "handler_name": "Video Media Handler",
            "encoder": f"{device_info['make']} Video Encoder"
        }

class EffectProcessor:
    def __init__(self):
        pass

    def apply_effects_batch(self, frame: np.ndarray, params: VideoEffectParams) -> np.ndarray:
        """Apply visual effects sequentially"""

        # 1. Scaling (всегда применяется для базового размера)
        frame = cv2.resize(frame, None, fx=params.scale, fy=params.scale)

        # 2. Saturation - проверяем значение
        if params.saturation != 1.0:  # Применяем только если не равно 1.0
            frame = self.apply_saturation(frame, params.saturation)

        # 3. Transparency - проверяем значение
        if params.transparency != 1.0:  # Применяем только если не равно 1.0
            frame = cv2.addWeighted(frame, params.transparency,
                      np.zeros_like(frame), 1 - params.transparency, 0)

        # 4. Gamma correction - проверяем значение
        if params.gamma != 1.0:  # Применяем только если не равно 1.0
            frame = self.apply_gamma(frame, params.gamma)

        # 5. Blur - проверяем значение
        if params.blur_amount > 0:
            kernel_size = int(params.blur_amount * 10) | 1
            kernel_size = max(3, min(kernel_size, 31))
            frame = cv2.GaussianBlur(frame, (kernel_size, kernel_size), 0)
            if kernel_size > 3:
                edge_kernel = kernel_size // 2
                frame = cv2.edgePreservingFilter(frame, flags=1, sigma_s=edge_kernel, sigma_r=0.3)

        # 6. Brightness and contrast - проверяем значения
        if params.contrast != 1.0 or params.brightness != 1.0:
            frame = frame.astype(np.float32)
            if params.contrast != 1.0:
                frame = frame * params.contrast
            if params.brightness != 1.0:
                frame = frame + (params.brightness - 1.0) * 255.0
            frame = np.clip(frame, 0, 255).astype(np.uint8)

        # 7. White balance - проверяем значения
        if params.white_balance_temperature != 1.0 or params.white_balance_tint != 0:
            frame = self.apply_white_balance(frame, params.white_balance_temperature, params.white_balance_tint)

        # 8. Vignette effect - проверяем значение
        if params.vignette_intensity > 0 and params.vignette_radius < 1.0:
            frame = self.apply_vignette(frame, params.vignette_intensity, params.vignette_radius)

        # 9. Apply noise effect - проверяем значение
        if params.noise_intensity > 0:
            frame = self.apply_noise(frame, params.noise_intensity)

        # 10. Добавляем микромозаику
        if params.mosaic_enabled and params.mosaic_size > 1:
            frame = self.apply_micro_mosaic(frame, params)

        # 10. Микро-точки
        if params.micro_dots_enabled and params.micro_dots_density > 0 and params.micro_dots_opacity > 0:
            frame = self.apply_micro_dots(frame, params)

        return frame

    def apply_saturation(self, frame: np.ndarray, amount: float) -> np.ndarray:
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV).astype(np.float32)
        hsv[:, :, 1] = np.clip(hsv[:, :, 1] * amount, 0, 255)
        return cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2BGR)

    def apply_gamma(self, frame: np.ndarray, amount: float) -> np.ndarray:
        lut = np.array([((i / 255.0) ** amount) * 255 for i in np.arange(0, 256)]).astype(np.uint8)
        return cv2.LUT(frame, lut)

    def apply_vignette(self, frame: np.ndarray, intensity: float, radius: float) -> np.ndarray:
        """Apply vignette effect to the frame"""
        height, width = frame.shape[:2]

        # Create a vignette mask
        x = np.linspace(-1, 1, width)
        y = np.linspace(-1, 1, height)
        X, Y = np.meshgrid(x, y)

        # Calculate radial distance from center
        R = np.sqrt(X**2 + Y**2)

        # Create vignette mask with smooth transition
        mask = 1 - np.clip((R - radius) / (1 - radius), 0, 1) * intensity
        mask = mask[:, :, np.newaxis]

        # Apply the mask to the frame
        return (frame * mask).astype(np.uint8)

    def apply_noise(self, frame: np.ndarray, intensity: float) -> np.ndarray:
        """Apply subtle noise effect to the frame"""
        noise = np.random.normal(0, 255 * intensity, frame.shape).astype(np.uint8)
        return cv2.addWeighted(frame, 1 - intensity, noise, intensity, 0)

    def apply_white_balance(self, frame: np.ndarray, temperature: float, tint: float) -> np.ndarray:
        """Apply white balance adjustment to the frame"""
        # Преобразуем кадр в цветовое пространство Lab
        frame_lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)

        # Применяем коррекцию баланса белого
        frame_lab[:, :, 0] = frame_lab[:, :, 0]
        frame_lab[:, :, 1] = frame_lab[:, :, 1] + tint * 2
        frame_lab[:, :, 2] = frame_lab[:, :, 2] + (temperature - 1) * (-1) * 2

        # Преобразуем кадр обратно в BGR
        return cv2.cvtColor(frame_lab, cv2.COLOR_LAB2BGR)

    def apply_micro_mosaic(self, frame: np.ndarray, params: VideoEffectParams) -> np.ndarray:
        """
        Применяет эффект микромозаики к кадру.
        Создает едва заметный мозаичный эффект для уникализации.
        """
        if not params.mosaic_enabled or params.mosaic_size <= 1:
            return frame

        # Сохраняем оригинальный кадр
        original = frame.copy()

        # Уменьшаем размер
        small = cv2.resize(
            frame,
            (frame.shape[1]//params.mosaic_size, frame.shape[0]//params.mosaic_size),
            interpolation=cv2.INTER_LINEAR
        )

        # Возвращаем к исходному размеру
        mosaic = cv2.resize(
            small,
            (frame.shape[1], frame.shape[0]),
            interpolation=cv2.INTER_NEAREST
        )

        # Смешиваем с оригиналом для более мягкого эффекта
        return cv2.addWeighted(
            original,
            1 - params.mosaic_opacity,
            mosaic,
            params.mosaic_opacity,
            0
        )

    def apply_micro_dots(self, frame: np.ndarray, params: VideoEffectParams) -> np.ndarray:
        """
        Добавляет едва заметные точки случайного цвета.

        Args:
            frame (np.ndarray): Входной кадр
            params (VideoEffectParams): Параметры эффектов

        Returns:
            np.ndarray: Обработанный кадр
        """
        if not params.micro_dots_enabled:
            return frame

        height, width = frame.shape[:2]

        # Создаем редкую маску точек
        dots_mask = np.random.choice(
            [0, 1],
            size=(height, width),
            p=[1 - params.micro_dots_density, params.micro_dots_density]
        )

        # Создаем случайные цвета для точек
        colors = np.random.randint(0, 255, size=(height, width, 3))
        dots = dots_mask[:, :, np.newaxis] * colors

        # Накладываем с заданной прозрачностью
        return cv2.addWeighted(
            frame,
            1.0,
            dots.astype(np.uint8),
            params.micro_dots_opacity,
            0
        )

    def create_rounded_mask(self, width: int, height: int, radius: int) -> np.ndarray:
        mask = np.zeros((height, width), dtype=np.float32)
        cv2.rectangle(mask, (radius, radius), (width-radius, height-radius), 1.0, -1)
        cv2.circle(mask, (radius, radius), radius, 1.0, -1)
        cv2.circle(mask, (width-radius, radius), radius, 1.0, -1)
        cv2.circle(mask, (radius, height-radius), radius, 1.0, -1)
        cv2.circle(mask, (width-radius, height-radius), radius, 1.0, -1)
        cv2.rectangle(mask, (radius, 0), (width-radius, radius), 1.0, -1)
        cv2.rectangle(mask, (radius, height-radius), (width-radius, height), 1.0, -1)
        cv2.rectangle(mask, (0, radius), (radius, height-radius), 1.0, -1)
        cv2.rectangle(mask, (width-radius, radius), (width, height-radius), 1.0, -1)
        return mask

    def get_animated_border_radius(self, frame_count: int, fps: int, params: VideoEffectParams) -> int:
        """Calculate animated border radius for current frame"""
        time = frame_count / fps
        phase = 2 * np.pi * time * params.border_radius_speed
        radius_range = params.border_radius_max - params.border_radius_min
        current_radius = params.border_radius_min + radius_range * (np.sin(phase) + 1) / 2
        return int(current_radius)

class AudioProcessor:
    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir())
        self.sample_rate = 44100

    def _apply_frequency_shift(self, audio: np.ndarray, params: dict) -> np.ndarray:
        """Применяет частотный сдвиг к аудио сигналу"""
        stft = librosa.stft(audio)
        freqs = librosa.fft_frequencies(sr=self.sample_rate)

        freq_mask = np.logical_and(
            freqs >= params['band_limits'][0],
            freqs <= params['band_limits'][1]
        )

        shift = params['shift_amount']
        if params['direction'] == 'down':
            shift = -shift

        random_shifts = np.random.uniform(
            -params['randomization'],
            params['randomization'],
            size=len(freqs)
        )

        shift_amounts = np.zeros_like(freqs)
        shift_amounts[freq_mask] = shift + random_shifts[freq_mask]

        time_steps = np.arange(stft.shape[1])
        phase_shift = np.exp(2j * np.pi * shift_amounts[:, np.newaxis] * time_steps / self.sample_rate)

        stft_shifted = stft * phase_shift
        return librosa.istft(stft_shifted, length=len(audio))

    def _apply_phase_shift(self, audio: np.ndarray, params: dict) -> np.ndarray:
        """Применяет фазовый сдвиг к определенным частотам"""
        stft = librosa.stft(audio)
        magnitude = np.abs(stft)
        phase = np.angle(stft)

        freqs = librosa.fft_frequencies(sr=self.sample_rate)
        freq_mask = np.logical_and(
            freqs >= params['frequency_range'][0],
            freqs <= params['frequency_range'][1]
        )

        phase_shift = params['amount'] * np.pi * freq_mask[:, np.newaxis]
        modified_phase = phase + phase_shift

        stft_modified = magnitude * np.exp(1j * modified_phase)
        return librosa.istft(stft_modified, length=len(audio))

    def _apply_micro_delay(self, audio: np.ndarray, params: dict) -> np.ndarray:
        """Добавляет микро-задержки для пространственного эффекта"""
        delay_samples = int(params['time_ms'] * self.sample_rate / 1000)
        feedback = params['feedback']

        delayed = np.roll(audio, delay_samples)
        delayed *= feedback
        result = audio + delayed

        return result

    def _apply_compression(self, audio: np.ndarray, params: dict) -> np.ndarray:
        """Применяет компрессию к аудио сигналу"""
        if not params:  # если параметры не переданы или компрессия отключена
            return audio

        threshold = 10 ** (params['threshold'] / 20)
        ratio = params['ratio']
        attack_samples = int(params['attack'] * self.sample_rate / 1000)
        release_samples = int(params['release'] * self.sample_rate / 1000)

        envelope = np.abs(audio)
        gain_reduction = np.zeros_like(envelope)

        for i in range(1, len(envelope)):
            if envelope[i] > threshold:
                target_reduction = (1 - 1/ratio) * (20 * np.log10(envelope[i]/threshold))
                time_constant = attack_samples if envelope[i] > envelope[i-1] else release_samples
                gain_reduction[i] = gain_reduction[i-1] + (target_reduction - gain_reduction[i-1]) / time_constant

        gain_reduction = np.clip(gain_reduction, -80, 0)
        gain_multiplier = np.power(10.0, gain_reduction / 20.0)
        gain_multiplier = np.clip(gain_multiplier, 0.0, 1.0)

        return audio * gain_multiplier

    def _apply_stereo_manipulation(self, audio: np.ndarray, params: dict) -> np.ndarray:
        """Манипулирует стерео полем"""
        if audio.ndim == 1:
            return audio

        mid = (audio[0] + audio[1]) / 2
        side = (audio[0] - audio[1]) / 2

        side *= params['width']

        rotation = params['rotation']
        rotated_mid = mid * np.cos(rotation) - side * np.sin(rotation)
        rotated_side = mid * np.sin(rotation) + side * np.cos(rotation)

        left = rotated_mid + rotated_side
        right = rotated_mid - rotated_side

        return np.stack([left, right])

    def _generate_noise(self, length: int, noise_type: str = 'white') -> np.ndarray:
        """
        Генерирует различные типы шума для аудио

        Args:
            length (int): Длина шума в сэмплах
            noise_type (str): Тип шума ('white', 'pink', 'brown', 'blue', 'violet', 'grey', 'velvet')

        Returns:
            np.ndarray: Сгенерированный шум
        """
        if noise_type == 'white':
            # Белый шум - равномерное распределение
            noise = np.random.normal(0, 1, length)

        elif noise_type == 'pink':
            # Розовый шум - спектр 1/f
            noise = np.random.normal(0, 1, length)
            # Применяем фильтр для получения розового шума
            f = np.fft.fftfreq(len(noise))
            f = np.abs(f)
            f[0] = 1e-6  # Избегаем деления на ноль
            f_pink = 1/np.sqrt(f)
            noise_fft = np.fft.fft(noise)
            noise_fft *= f_pink
            noise = np.real(np.fft.ifft(noise_fft))

        elif noise_type == 'brown':
            # Коричневый шум - спектр 1/f^2
            noise = np.random.normal(0, 1, length)
            noise = np.cumsum(noise)  # Интегрирование для получения коричневого шума
            # Нормализация
            noise = noise - np.mean(noise)
            noise = noise / np.max(np.abs(noise))

        elif noise_type == 'blue':
            # Синий шум - спектр f^1
            noise = np.random.normal(0, 1, length)
            f = np.fft.fftfreq(len(noise))
            f = np.abs(f)
            f[0] = 1e-6
            f_blue = np.sqrt(f)
            noise_fft = np.fft.fft(noise)
            noise_fft *= f_blue
            noise = np.real(np.fft.ifft(noise_fft))

        elif noise_type == 'violet':
            # Фиолетовый шум - спектр f^2
            noise = np.random.normal(0, 1, length)
            f = np.fft.fftfreq(len(noise))
            f = np.abs(f)
            f[0] = 1e-6
            f_violet = f
            noise_fft = np.fft.fft(noise)
            noise_fft *= f_violet
            noise = np.real(np.fft.ifft(noise_fft))

        elif noise_type == 'grey':
            # Серый шум - комбинация белого и розового шума
            white = np.random.normal(0, 1, length)
            pink = self._generate_noise(length, 'pink')
            mix_factor = np.random.uniform(0.3, 0.7)
            noise = white * mix_factor + pink * (1 - mix_factor)

        elif noise_type == 'velvet':
            # Бархатный шум - специальный тип с мягким спектром
            noise = np.random.normal(0, 1, length)
            f = np.fft.fftfreq(len(noise))
            f = np.abs(f)
            f[0] = 1e-6
            # Создаем мягкий спектральный спад
            f_velvet = 1 / (1 + f)
            noise_fft = np.fft.fft(noise)
            noise_fft *= f_velvet
            noise = np.real(np.fft.ifft(noise_fft))

        else:
            # По умолчанию используем белый шум
            noise = np.random.normal(0, 1, length)

        # Нормализация
        noise = noise - np.mean(noise)
        noise = noise / np.max(np.abs(noise))

        return noise

    def _apply_noise(self, audio: np.ndarray, params: dict) -> np.ndarray:
        """
        Добавляет шум к аудио сигналу

        Args:
            audio (np.ndarray): Входной аудио сигнал
            params (dict): Параметры шума

        Returns:
            np.ndarray: Аудио сигнал с добавленным шумом
        """
        if not params:  # если параметры не переданы или шум отключен
            return audio

        # Генерируем шум для каждого канала отдельно
        noise_signals = []
        for channel in range(len(audio)):
            noise = self._generate_noise(len(audio[channel]), params['type'])
            # Нормализуем шум и умножаем на интенсивность
            noise = noise * params['intensity']
            noise_signals.append(noise)

        noise_array = np.array(noise_signals)

        # Добавляем шум к сигналу
        noisy_audio = audio + noise_array

        # Нормализуем результат чтобы избежать клиппинга
        max_val = np.max(np.abs(noisy_audio))
        if max_val > 1.0:
            noisy_audio = noisy_audio / max_val

        return noisy_audio

    def _apply_spectral_warping(self, audio: np.ndarray, params: dict) -> np.ndarray:
        """Применяет нелинейное искажение спектра"""
        stft = librosa.stft(audio)
        freqs = np.fft.fftfreq(stft.shape[0])

        # Создаем нелинейное искажение частот
        warped_freqs = freqs + params['amount'] * np.sin(2 * np.pi * freqs * params['complexity'])

        # Интерполируем STFT с новыми частотами
        warped_stft = []
        for i in range(stft.shape[1]):
            real_interp = scipy.interpolate.interp1d(freqs, stft[:, i].real,
                                                   bounds_error=False, fill_value=0)
            imag_interp = scipy.interpolate.interp1d(freqs, stft[:, i].imag,
                                                   bounds_error=False, fill_value=0)
            warped_column = real_interp(warped_freqs) + 1j * imag_interp(warped_freqs)
            warped_stft.append(warped_column)

        warped_stft = np.column_stack(warped_stft)
        return librosa.istft(warped_stft, length=len(audio))

    def process_audio(self, audio_path: str, config_params: dict) -> str:
        """Обрабатывает аудио с параметрами из конфигурации"""
        try:
            y, sr = librosa.load(audio_path, sr=None, mono=False)

            if y.ndim == 1:
                y = np.stack([y, y])

            processed_channels = []
            for channel in range(y.shape[0]):
                current_audio = y[channel].copy()

                # Применяем только включенные эффекты
                if config_params.get('frequency_shift'):
                    current_audio = self._apply_frequency_shift(current_audio, config_params['frequency_shift'])

                if config_params.get('phase_shift'):
                    current_audio = self._apply_phase_shift(current_audio, config_params['phase_shift'])

                if config_params.get('compression'):
                    current_audio = self._apply_compression(current_audio, config_params['compression'])

                if config_params.get('micro_delay'):
                    current_audio = self._apply_micro_delay(current_audio, config_params['micro_delay'])

                processed_channels.append(current_audio)

            y_processed = np.stack(processed_channels)

            if config_params.get('stereo_manipulation'):
                y_processed = self._apply_stereo_manipulation(y_processed, config_params['stereo_manipulation'])

            # Добавляем шум после всех эффектов
            if config_params.get('noise'):
                y_processed = self._apply_noise(y_processed, config_params['noise'])

            # Нормализация
            max_val = np.max(np.abs(y_processed))
            if max_val > 1.2:
                y_processed = y_processed / max_val

            output_path = str(self.temp_dir / f"processed_audio_{random.randint(1000, 9999)}.wav")
            sf.write(output_path, y_processed.T, sr)

            return output_path

        except Exception as e:
            print(f"Ошибка обработки аудио: {str(e)}")
            traceback.print_exc()
            return audio_path

    def extract_audio(self, video_path: str) -> str:
        """Извлекает аудио из видео файла или создает тишину, если аудио отсутствует"""
        try:
            # Сначала проверяем, есть ли аудиодорожка в видео
            has_audio = False
            ffprobe_command = [
                "ffprobe",
                "-v", "error",
                "-select_streams", "a",
                "-show_entries", "stream=codec_type",
                "-of", "json",
                video_path
            ]

            result = subprocess.run(ffprobe_command, capture_output=True, text=True)
            if result.returncode == 0:
                probe_data = json.loads(result.stdout)
                has_audio = 'streams' in probe_data and len(probe_data['streams']) > 0

            output_path = str(self.temp_dir / f"extracted_audio_{random.randint(1000, 9999)}.wav")

            if has_audio:
                # Если аудиодорожка есть, извлекаем её как обычно
                ffmpeg_command = [
                    "ffmpeg",
                    "-i", video_path,
                    "-vn",
                    "-acodec", "pcm_s16le",
                    "-ar", str(self.sample_rate),
                    "-ac", "2",
                    "-y",
                    output_path
                ]

                subprocess.run(ffmpeg_command, check=True, capture_output=True)
            else:
                # Если аудиодорожки нет, создаем тишину с той же длительностью, что и видео
                # Сначала получаем длительность видео
                duration_command = [
                    "ffprobe",
                    "-v", "error",
                    "-show_entries", "format=duration",
                    "-of", "default=noprint_wrappers=1:nokey=1",
                    video_path
                ]

                duration_result = subprocess.run(duration_command, capture_output=True, text=True)
                if duration_result.returncode != 0:
                    # Если не удалось получить длительность, используем 5 секунд по умолчанию
                    duration = 5
                else:
                    try:
                        duration = float(duration_result.stdout.strip())
                    except ValueError:
                        duration = 5

                # Создаем тишину
                silent_command = [
                    "ffmpeg",
                    "-f", "lavfi",
                    "-i", f"anullsrc=r={self.sample_rate}:cl=stereo",
                    "-t", str(duration),
                    "-acodec", "pcm_s16le",
                    "-ar", str(self.sample_rate),
                    "-ac", "2",
                    "-y",
                    output_path
                ]

                subprocess.run(silent_command, check=True, capture_output=True)

            return output_path

        except subprocess.CalledProcessError as e:
            print(f"Ошибка при работе с аудио: {e.stderr.decode() if hasattr(e, 'stderr') else str(e)}")
            # Создаем тихий звук минимальной длительности вместо возбуждения исключения
            silent_path = str(self.temp_dir / f"silent_audio_{random.randint(1000, 9999)}.wav")
            try:
                silent_command = [
                    "ffmpeg",
                    "-f", "lavfi",
                    "-i", f"anullsrc=r={self.sample_rate}:cl=stereo",
                    "-t", "5",  # 5 секунд тишины по умолчанию
                    "-acodec", "pcm_s16le",
                    "-ar", str(self.sample_rate),
                    "-ac", "2",
                    "-y",
                    silent_path
                ]
                subprocess.run(silent_command, check=True, capture_output=True)
                print("Создан файл тишины из-за ошибки при извлечении аудио.")
                return silent_path
            except Exception as silent_error:
                print(f"Критическая ошибка при создании тишины: {str(silent_error)}")
                raise
        except Exception as e:
            print(f"Неожиданная ошибка при извлечении аудио: {str(e)}")
            # Также создаем тишину в случае других ошибок
            silent_path = str(self.temp_dir / f"silent_audio_{random.randint(1000, 9999)}.wav")
            try:
                silent_command = [
                    "ffmpeg",
                    "-f", "lavfi",
                    "-i", "anullsrc=r=44100:cl=stereo",
                    "-t", "5",
                    "-acodec", "pcm_s16le",
                    "-ar", "44100",
                    "-ac", "2",
                    "-y",
                    silent_path
                ]
                subprocess.run(silent_command, check=True, capture_output=True)
                print("Создан файл тишины из-за неожиданной ошибки.")
                return silent_path
            except Exception:
                print("Критическая ошибка при создании тишины. Невозможно продолжить обработку.")
                raise

class MotionCoordinator:
    def __init__(self, total_frames: int, effect_params: VideoEffectParams):
        """
        Инициализация координатора движения и масштабирования

        Args:
            total_frames (int): Общее количество кадров
            effect_params (VideoEffectParams): Параметры эффектов
        """
        self.total_frames = total_frames
        self.params = effect_params
        self.current_frame = 0

        # Генерируем все траектории движения заранее
        self.movement_trajectory = self._generate_movement_trajectory()
        self.scale_trajectory = self._generate_scale_trajectory()

    def _generate_movement_trajectory(self) -> List[dict]:
        """Генерирует полную траекторию движения с улучшенным сглаживанием"""
        trajectory = []

        # Если амплитуда движения = 0, возвращаем статичную траекторию
        if self.params.movement_amplitude == 0:
            return [{'x': 0, 'y': 0} for _ in range(self.total_frames)]

        time = np.linspace(0, 3.0 * np.pi, self.total_frames)

        # Базовые параметры движения с уменьшенной амплитудой
        amplitude_x = self.params.movement_amplitude * 150
        amplitude_y = self.params.movement_amplitude * 150
        speed_x = self.params.movement_speed * 4.0
        speed_y = self.params.movement_speed * 4.0

        # Генерируем базовое движение в зависимости от паттерна
        if self.params.movement_pattern == 'circular':
            x_movement = amplitude_x * np.sin(time * speed_x)
            y_movement = amplitude_y * np.cos(time * speed_y)

        elif self.params.movement_pattern == 'figure8':
            x_movement = amplitude_x * np.sin(time * speed_x)
            y_movement = amplitude_y * np.sin(time * speed_y * 2)

        elif self.params.movement_pattern == 'spiral':
            radius = np.linspace(0, max(amplitude_x, amplitude_y), self.total_frames)
            radius *= (1 - np.power(np.linspace(0, 1, self.total_frames), 2) * 0.3)
            angle = time * speed_x
            x_movement = radius * np.cos(angle)
            y_movement = radius * np.sin(angle)

        elif self.params.movement_pattern == 'zigzag':
            x_movement = amplitude_x * np.sin(time * speed_x)
            y_movement = amplitude_y * signal.sawtooth(time * speed_y * 0.8, width=0.5)

        elif self.params.movement_pattern == 'diagonal':
            progress = (1 - np.cos(np.pi * time / time[-1])) / 2
            x_movement = amplitude_x * progress
            y_movement = amplitude_y * progress

        elif self.params.movement_pattern == 'combination':
            x_movement = (amplitude_x * 0.5 * np.sin(time * speed_x) +
                         amplitude_x * 0.3 * np.cos(time * speed_x * 1.3) +
                         amplitude_x * 0.2 * np.sin(time * speed_x * 1.7))
            y_movement = (amplitude_y * 0.5 * np.cos(time * speed_y) +
                         amplitude_y * 0.3 * np.sin(time * speed_y * 1.3) +
                         amplitude_y * 0.2 * np.cos(time * speed_y * 1.7))

        elif self.params.movement_pattern == 'random':
            x_movement = amplitude_x * np.sin(time * speed_x + np.random.uniform(0, np.pi))
            y_movement = amplitude_y * np.cos(time * speed_y + np.random.uniform(0, np.pi))

        else:  # если паттерн не распознан или движение выключено
            return [{'x': 0, 'y': 0} for _ in range(self.total_frames)]

        # Добавляем минимальный контролируемый шум
        noise = np.random.normal(0, amplitude_x * 0.05, self.total_frames)
        kernel_size = 9
        noise = np.convolve(noise, np.ones(kernel_size)/kernel_size, mode='same')

        # Двойное сглаживание движения
        for i in range(2):
            x_movement = np.convolve(x_movement, np.ones(kernel_size)/kernel_size, mode='same')
            y_movement = np.convolve(y_movement, np.ones(kernel_size)/kernel_size, mode='same')

        # Создаем плавные переходы в начале и конце
        fade_frames = int(self.total_frames * 0.1)
        fade_in = np.linspace(0, 1, fade_frames)
        fade_out = np.linspace(1, 0, fade_frames)

        fade_mask = np.ones(self.total_frames)
        fade_mask[:fade_frames] = fade_in
        fade_mask[-fade_frames:] = fade_out

        # Применяем затухание к движению
        x_movement = x_movement * fade_mask
        y_movement = y_movement * fade_mask

        # Добавляем шум с учетом затухания
        x_movement += noise * fade_mask
        y_movement += noise * fade_mask

        # Создаем траекторию движения с плавными переходами
        prev_x = 0
        prev_y = 0
        smoothing_factor = 0.7

        for i in range(self.total_frames):
            current_x = int(x_movement[i])
            current_y = int(y_movement[i])

            # Сглаживаем переход между предыдущим и текущим положением
            smooth_x = int(smoothing_factor * prev_x + (1 - smoothing_factor) * current_x)
            smooth_y = int(smoothing_factor * prev_y + (1 - smoothing_factor) * current_y)

            trajectory.append({
                'x': smooth_x,
                'y': smooth_y,
            })

            prev_x = smooth_x
            prev_y = smooth_y

        return trajectory

    def _generate_scale_trajectory(self) -> List[float]:
        """
        Генерирует траекторию масштабирования с фиксированным начальным масштабом.
        Начинаем сразу с максимального масштаба, избегая эффекта начального зума.
        """
        # Параметры масштабирования
        start_scale = max(self.params.background_start_scale, self.params.background_end_scale)
        end_scale = min(self.params.background_start_scale, self.params.background_end_scale)
        speed = self.params.background_scale_speed

        # Адаптируем параметры под длительность видео
        is_short_video = self.total_frames < 180

        # Создаем временную шкалу
        t = np.linspace(0, 1, self.total_frames)

        # Определяем точку фиксации в конце (90% видео)
        final_fixed_start = int(self.total_frames * 0.9)

        # Адаптивные параметры для волн
        if is_short_video:
            wave_speeds = [1.0, 1.3]
            wave_amplitudes = [0.01, 0.005]
            smoothing_kernels = [5, 3]
        else:
            wave_speeds = [1.0, 1.3, 1.7, 2.1]
            wave_amplitudes = [0.02, 0.01, 0.005, 0.003]
            smoothing_kernels = [15, 9, 5]

        # Создаем базовую прогрессию для масштабирования
        # Начинаем сразу с максимального масштаба
        if self.params.background_scale_pattern == 'ease-out':
            progress = 1 - np.power(t, 0.5)  # Инвертированная прогрессия
        elif self.params.background_scale_pattern == 'ease-in':
            progress = 1 - np.power(t, 2.0)  # Инвертированная прогрессия
        elif self.params.background_scale_pattern == 'ease-in-out':
            progress = 0.5 * (1 + np.cos(np.pi * t))  # Инвертированная прогрессия
        else:  # linear
            progress = 1 - t  # Линейное уменьшение от начального масштаба

        # Важно: начинаем со start_scale и постепенно уменьшаем
        base_scale = start_scale * progress + end_scale * (1 - progress)

        # Генерируем волны с уменьшенной амплитудой в начале
        waves = np.zeros_like(t)
        for amplitude, wave_speed in zip(wave_amplitudes, wave_speeds):
            wave = amplitude * np.sin(2 * np.pi * t * speed * wave_speed)
            # Уменьшаем амплитуду волн к концу
            wave_mask = np.ones_like(t)
            wave_mask[final_fixed_start:] = 0
            # Уменьшаем амплитуду волн в начале видео
            start_fade = int(self.total_frames * 0.1)  # Первые 10% видео
            wave_mask[:start_fade] = np.linspace(0, 1, start_fade)
            # Плавное затухание волн к концу
            transition_length = int(self.total_frames * 0.05)
            wave_mask[final_fixed_start-transition_length:final_fixed_start] = \
                np.linspace(1, 0, transition_length)
            waves += wave * wave_mask

        # Комбинируем базовое масштабирование с волнами
        scale = base_scale + waves

        # Сглаживание
        for kernel_size in smoothing_kernels:
            kernel = np.ones(kernel_size) / kernel_size
            scale = np.convolve(scale, kernel, mode='same')

        # Фиксируем конечный масштаб
        scale[final_fixed_start:] = end_scale

        # Плавный переход к конечному масштабу
        transition_length = int(self.total_frames * 0.05)
        transition_start = final_fixed_start - transition_length
        transition = 0.5 * (1 - np.cos(np.pi * np.linspace(0, 1, transition_length)))
        scale[transition_start:final_fixed_start] = \
            scale[transition_start] * (1 - transition) + end_scale * transition

        # Фиксируем начальный масштаб
        scale[:int(self.total_frames * 0.05)] = start_scale

        # Ограничения масштаба
        if is_short_video:
            scale = np.clip(scale,
                           min(start_scale, end_scale) * 0.99,
                           start_scale * 1.01)
        else:
            scale = np.clip(scale,
                           min(start_scale, end_scale) * 0.98,
                           start_scale * 1.02)

        return scale.tolist()

    def _smooth_step(self, x: float) -> float:
        """
        Кубическая функция сглаживания для более плавного движения
        """
        return x * x * (3 - 2 * x)

    def get_frame_parameters(self, frame_index: int) -> dict:
        """
        Получает параметры движения и масштабирования для текущего кадра
        с дополнительной проверкой для последних кадров
        """
        movement = self.movement_trajectory[frame_index]
        scale = self.scale_trajectory[frame_index]

        # Определяем, находимся ли мы в последних 10% кадров
        is_final_section = frame_index >= int(self.total_frames * 0.9)

        # Корректируем движение в зависимости от масштаба
        if is_final_section:
            # Используем фиксированный scale_factor для последних кадров
            scale_factor = self.scale_trajectory[int(self.total_frames * 0.9)] / self.params.background_start_scale
        else:
            scale_factor = scale / self.params.background_start_scale

        # Применяем плавное замедление движения для последних кадров
        if is_final_section:
            progress = (frame_index - int(self.total_frames * 0.9)) / (self.total_frames * 0.1)
            slowdown_factor = 1 - progress
            x_adjusted = int(movement['x'] * scale_factor * slowdown_factor)
            y_adjusted = int(movement['y'] * scale_factor * slowdown_factor)
        else:
            x_adjusted = int(movement['x'] * scale_factor)
            y_adjusted = int(movement['y'] * scale_factor)

        return {
            'x_offset': x_adjusted,
            'y_offset': y_adjusted,
            'scale': scale
        }

class DynamicBackground:
    def __init__(self, background: Tuple[np.ndarray, Path], total_frames: int, motion_coordinator: MotionCoordinator):
        """
        Инициализация динамического фона с координацией движения и масштабирования

        Args:
            background (Tuple[np.ndarray, Path]): Кортеж с изображением фона и путем к файлу
            total_frames (int): Общее количество кадров в видео
            motion_coordinator (MotionCoordinator): Координатор движения и масштабирования
        """
        self.background_image, self.background_path = background
        self.total_frames = total_frames
        self.motion_coordinator = motion_coordinator
        self.current_frame = 0
        self.height, self.width = self.background_image.shape[:2]

        # Параметры размытия для создания эффекта глубины
        self.start_blur = 3     # Начальное размытие
        self.end_blur = 0       # Конечное размытие

        # Предварительно масштабируем фоновое изображение
        start_scale = motion_coordinator.params.background_start_scale
        self.initial_scaled_size = (
            int(self.width * start_scale),
            int(self.height * start_scale)
        )
        self.background_image = cv2.resize(self.background_image, self.initial_scaled_size)

    def _smooth_step(self, x: float) -> float:
        """
        Функция сглаживания для более плавного масштабирования
        """
        return x * x * (3 - 2 * x)

    def get_frame(self) -> Tuple[np.ndarray, bool]:
        """
        Получает текущий кадр фона с учетом движения и масштабирования

        Returns:
            Tuple[np.ndarray, bool]: Кортеж с обработанным кадром и флагом завершения
        """
        if self.current_frame >= self.total_frames:
            return self.background_image, True

        # Получаем параметры текущего кадра от координатора
        frame_params = self.motion_coordinator.get_frame_parameters(self.current_frame)

        # Сглаживание перехода между кадрами
        if self.current_frame > 0:
            prev_params = self.motion_coordinator.get_frame_parameters(self.current_frame - 1)
            frame_params['x_offset'] = int(0.7 * prev_params['x_offset'] + 0.3 * frame_params['x_offset'])
            frame_params['y_offset'] = int(0.7 * prev_params['y_offset'] + 0.3 * frame_params['y_offset'])

        current_scale = frame_params['scale']

        # Вычисляем текущее размытие
        progress = self.current_frame / self.total_frames
        current_blur = int(self.start_blur - (self.start_blur - self.end_blur) * progress)

        # Масштабируем изображение от начального масштаба
        scaled_size = (
            int(self.width * current_scale),
            int(self.height * current_scale)
        )
        scaled_image = cv2.resize(self.background_image, scaled_size)

        # Вычисляем область обрезки с учетом движения
        start_x = (scaled_image.shape[1] - self.width) // 2
        start_y = (scaled_image.shape[0] - self.height) // 2

        # Добавляем смещение от координатора с проверкой границ
        start_x += frame_params['x_offset']
        start_y += frame_params['y_offset']

        # Проверяем и корректируем границы
        start_x = max(0, min(start_x, scaled_image.shape[1] - self.width))
        start_y = max(0, min(start_y, scaled_image.shape[0] - self.height))

        # Вырезаем нужную область
        try:
            end_x = start_x + self.width
            end_y = start_y + self.height

            # Дополнительная проверка границ
            end_x = min(end_x, scaled_image.shape[1])
            end_y = min(end_y, scaled_image.shape[0])

            cropped_image = scaled_image[start_y:end_y, start_x:end_x]

            # Если размеры не совпадают, масштабируем до нужного размера
            if cropped_image.shape[:2] != (self.height, self.width):
                cropped_image = cv2.resize(cropped_image, (self.width, self.height))

            # Применяем размытие если нужно
            if current_blur > 0:
                kernel_size = current_blur * 2 + 1
                cropped_image = cv2.GaussianBlur(cropped_image,
                                               (kernel_size, kernel_size),
                                               0)

        except Exception as e:
            print(f"Ошибка при обработке кадра: {str(e)}")
            # В случае ошибки возвращаем исходное изображение
            cropped_image = cv2.resize(self.background_image, (self.width, self.height))

        self.current_frame += 1
        return cropped_image, self.current_frame >= self.total_frames

    def get_used_backgrounds(self) -> set:
        """
        Возвращает множество использованных фоновых изображений

        Returns:
            set: Множество путей к использованным фоновым изображениям
        """
        return {self.background_path}

class VideoUniquifier:
    def __init__(self, base_dir: Optional[str] = None, config_path: str = "settings.ini"):
        self.config = VideoUniquifierConfig(config_path)
        if base_dir:
            base_path = Path(base_dir)
            self.config.base_dir = base_path
            self.config.input_dir = base_path / "input"
            self.config.output_dir = base_path / "output"
            self.config.bg_dir = base_path / "backgrounds"

        self.base_dir = Path(self.config.base_dir)
        self.input_dir = Path(self.config.input_dir)
        self.output_dir = Path(self.config.output_dir)
        self.bg_dir = Path(self.config.bg_dir)
        self.temp_dir = Path(tempfile.gettempdir())

        # Инициализация эффекта наложения PNG
        self.png_overlay_effect = PngOverlayEffect(str(self.base_dir))

        self.current_bg_index = 0
        self.current_bg_path = None
        self.snow_effect = None
        self.original_backgrounds = []
        self.progress_callback = None
        self.shutdown_flag = threading.Event()
        self.active_processes = set()

        # Регистрируем обработчики сигналов только в главном потоке
        try:
            self.original_sigint = signal.getsignal(signal.SIGINT)
            self.original_sigterm = signal.getsignal(signal.SIGTERM)
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        except ValueError:
            # Сигналы можно устанавливать только в главном потоке
            # В веб-интерфейсе это не критично
            self.original_sigint = None
            self.original_sigterm = None

        self.active_resources = {
            'video_captures': set(),
            'video_writers': set(),
            'temp_files': set(),
            'thread_pool': None
        }

        self._create_directories()
        self._cache_original_backgrounds()
        self.backgrounds = self._load_backgrounds()
        self.effect_processor = EffectProcessor()
        self.audio_processor = AudioProcessor()

        # Проверка доступности GPU
        self.gpu_available = cv2.cuda.getCudaEnabledDeviceCount() > 0
        self.use_gpu = False  # По умолчанию используем CPU

        if self.gpu_available:
            # Получаем информацию о доступных GPU
            self.gpu_info = []
            for i in range(cv2.cuda.getCudaEnabledDeviceCount()):
                gpu = cv2.cuda.getDevice(i)
                name = cv2.cuda.getDeviceName(i)
                memory = cv2.cuda.DeviceInfo().totalMemory()
                self.gpu_info.append({
                    'index': i,
                    'name': name,
                    'memory': memory
                })

    def _signal_handler(self, signum, frame):
        """Расширенный обработчик сигналов с принудительным завершением процессов"""
        print("\nПолучен сигнал завершения. Выполняется принудительное завершение...")

        try:
            # Устанавливаем флаг завершения
            self.shutdown_flag.set()

            # Завершаем все активные процессы
            for process in self.active_processes:
                try:
                    if process.poll() is None:  # Если процесс все еще работает
                        process.kill()  # Принудительно завершаем процесс
                except Exception:
                    pass

            # Очищаем видео ресурсы
            for cap in self.active_resources['video_captures']:
                try:
                    if cap and cap.isOpened():
                        cap.release()
                except Exception:
                    pass

            for writer in self.active_resources['video_writers']:
                try:
                    if writer:
                        writer.release()
                except Exception:
                    pass

            # Принудительно завершаем thread pool
            if self.active_resources['thread_pool']:
                try:
                    self.active_resources['thread_pool'].shutdown(wait=False)
                    for thread in threading.enumerate():
                        if thread != threading.current_thread():
                            try:
                                thread._stop()
                            except Exception:
                                pass
                except Exception:
                    pass

            # Очищаем временные файлы
            for temp_file in self.active_resources['temp_files']:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                except Exception:
                    pass

            # Безопасное освобождение окон OpenCV
            try:
                cv2.destroyAllWindows()
            except Exception:
                pass

            # Восстанавливаем оригинальные обработчики сигналов, если они были установлены
            if self.original_sigint is not None:
                signal.signal(signal.SIGINT, self.original_sigint)
            if self.original_sigterm is not None:
                signal.signal(signal.SIGTERM, self.original_sigterm)

            # Принудительно завершаем процесс
            pid = os.getpid()
            if os.name == 'nt':  # Windows
                os.system(f'taskkill /F /PID {pid}')
            else:  # Unix/Linux
                os.kill(pid, signal.SIGKILL)

        except Exception as e:
            print(f"Ошибка при завершении: {str(e)}")
            # В случае критической ошибки используем самый жесткий способ завершения
            os._exit(1)

    def _cleanup_temp_files(self, temp_files: List[str]):
        """Clean up temporary files"""
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    # Добавляем небольшую задержку перед удалением
                    time.sleep(0.1)
                    os.remove(temp_file)
            except Exception as e:
                print(f"Ошибка при удалении временного файла {temp_file}: {str(e)}")

    def _create_directories(self):
        for dir_path in [self.input_dir, self.output_dir, self.bg_dir, self.base_dir / "emoji"]:
            dir_path.mkdir(parents=True, exist_ok=True)

    def _cache_original_backgrounds(self):
        """Кэширует список всех доступных фоновых изображений"""

        # Используем блокировку для безопасного доступа к файловой системе
        with _global_background_lock:
            # Получаем все изображения с поддерживаемыми расширениями
            jpg_files = list(self.bg_dir.glob("*.jpg"))
            jpeg_files = list(self.bg_dir.glob("*.jpeg"))
            png_files = list(self.bg_dir.glob("*.png"))
            jfif_files = list(self.bg_dir.glob("*.jfif"))

            # Объединяем все файлы в один список
            all_files = jpg_files + jpeg_files + png_files + jfif_files

            # Перемешиваем список для случайного порядка
            random.shuffle(all_files)

            # Сохраняем список в экземпляре класса
            self.original_backgrounds = all_files

        if not self.original_backgrounds:
            raise RuntimeError("Нет доступных фоновых изображений в директории")

        #print(f"Кэшировано {len(self.original_backgrounds)} фоновых изображений")

    def _load_backgrounds(self) -> List[Tuple[np.ndarray, Path]]:
        """Load single background"""
        if not self.original_backgrounds:
            raise RuntimeError("Нет доступных фоновых изображений")

        bg_path = self.original_backgrounds[0]  # Берем только один фон
        try:
            with open(bg_path, 'rb') as f:
                img_array = np.frombuffer(f.read(), np.uint8)
                bg = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

            if bg is not None:
                bg = cv2.resize(bg, (1080, 1920))
                return [(bg, bg_path)]

        except Exception as e:
            print(f"Ошибка при загрузке фона {bg_path.name}: {str(e)}")

        raise RuntimeError("Не удалось загрузить фон")

    def set_processing_device(self, use_gpu: bool = False):
        """
        Установка устройства для обработки

        Args:
            use_gpu (bool): True для использования GPU, False для CPU
        """
        if use_gpu and not self.gpu_available:
            self.use_gpu = False
        else:
            self.use_gpu = use_gpu

    def process_frame_gpu(self, frame: np.ndarray, effect_params: VideoEffectParams) -> np.ndarray:
        """
        Обработка кадра на GPU
        """
        try:
            # Загружаем кадр на GPU
            gpu_frame = cv2.cuda_GpuMat()
            gpu_frame.upload(frame)

            # Масштабирование
            if effect_params.scale != 1.0:
                scaled_size = (int(frame.shape[1] * effect_params.scale),
                             int(frame.shape[0] * effect_params.scale))
                gpu_frame = cv2.cuda.resize(gpu_frame, scaled_size)

            # Применение размытия
            if effect_params.blur_amount > 0:
                kernel_size = int(effect_params.blur_amount * 10) | 1
                kernel_size = max(3, min(kernel_size, 31))
                gpu_frame = cv2.cuda.blur(gpu_frame, (kernel_size, kernel_size))

            # Яркость и контраст
            if effect_params.contrast != 1.0 or effect_params.brightness != 1.0:
                gpu_frame = cv2.cuda.multiply(gpu_frame, effect_params.contrast)
                gpu_frame = cv2.cuda.add(gpu_frame, (effect_params.brightness - 1.0) * 255.0)

            # Загружаем результат обратно в CPU память
            result = gpu_frame.download()

            # Эффекты, которые пока не реализованы на GPU, применяем на CPU
            if effect_params.saturation != 1.0:
                result = self.effect_processor.apply_saturation(result, effect_params.saturation)

            if effect_params.gamma != 1.0:
                result = self.effect_processor.apply_gamma(result, effect_params.gamma)

            return result

        except cv2.error as e:
            return self.effect_processor.apply_effects_batch(frame, effect_params)

    def refresh_backgrounds(self):
        """Загружает уникальный фон для каждого видео с использованием глобальной синхронизации"""
        global _used_backgrounds, _global_background_lock

        if not self.original_backgrounds:
            raise RuntimeError("Нет доступных фоновых изображений")

        # Очистим текущий список фонов
        self.backgrounds = []

        # Используем блокировку для безопасного доступа к общему ресурсу
        with _global_background_lock:
            # Сначала выделим все еще не использованные фоны
            unused_backgrounds = [
                bg for bg in self.original_backgrounds
                if str(bg) not in _used_backgrounds
            ]

            # Если все фоны были использованы, сбрасываем список использованных
            if not unused_backgrounds:
                print("Все фоны уже были использованы, сбрасываем список")
                _used_backgrounds.clear()
                unused_backgrounds = self.original_backgrounds.copy()

            # Выбираем случайный фон из неиспользованных
            bg_path = random.choice(unused_backgrounds)

            # Отмечаем фон как использованный
            _used_backgrounds.add(str(bg_path))

            #print(f"Выбран фон: {bg_path.name} (осталось {len(unused_backgrounds) - 1} неиспользованных)")

        try:
            # Загружаем выбранный фон
            with open(bg_path, 'rb') as f:
                img_array = np.frombuffer(f.read(), np.uint8)
                bg = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

            if bg is not None:
                bg = cv2.resize(bg, (1080, 1920))
                self.backgrounds.append((bg, bg_path))
                #print(f"Загружен уникальный фон: {bg_path.name}")
                return True

        except Exception as e:
            # При ошибке загрузки освобождаем фон для повторного использования
            with _global_background_lock:
                _used_backgrounds.discard(str(bg_path))
            print(f"Ошибка при загрузке фона {bg_path.name}: {str(e)}")
            return False

    def get_next_background(self) -> np.ndarray:
        """Получает следующий фон по порядку"""
        if not self.backgrounds:
            raise RuntimeError("Нет доступных фоновых изображений")

        background, bg_path = self.backgrounds[self.current_bg_index]  # Properly unpack the tuple
        self.current_bg_path = bg_path  # Now bg_path is defined
        self.current_bg_index = (self.current_bg_index + 1) % len(self.backgrounds)

        return background

    def _generate_smooth_movement(self, total_frames: int, effect_params: VideoEffectParams) -> Tuple[List[int], List[int]]:
        """Generates smoothed movement trajectories"""
        time = np.linspace(0, 3.0 * np.pi, total_frames)

        # Улучшенные множители для плавности
        amplitude_multiplier = np.sin(np.linspace(0, np.pi, total_frames)) * 0.5 + 0.5
        speed_multiplier = np.sin(np.linspace(0, np.pi, total_frames) + np.pi/2) * 0.5 + 0.5

        amplitude_x = effect_params.movement_amplitude * 300 * amplitude_multiplier
        amplitude_y = effect_params.movement_amplitude * 300 * amplitude_multiplier
        speed_x = effect_params.movement_speed * 4.0 * speed_multiplier
        speed_y = effect_params.movement_speed * 4.0 * speed_multiplier

        if effect_params.movement_pattern == 'circular':
            x_movement = amplitude_x * np.sin(time * speed_x)
            y_movement = amplitude_y * np.cos(time * speed_y)

        elif effect_params.movement_pattern == 'figure8':
            x_movement = amplitude_x * np.sin(time * speed_x)
            y_movement = amplitude_y * np.sin(time * speed_y * 2)

        elif effect_params.movement_pattern == 'diagonal':
            # Добавляем сглаживание для диагонального движения
            progress = (1 - np.cos(np.pi * time / time[-1])) / 2  # Сглаженный прогресс
            x_movement = amplitude_x * progress
            y_movement = amplitude_y * progress

        elif effect_params.movement_pattern == 'zigzag':
            from scipy.signal import sawtooth  # Добавляем корректный импорт
            zigzag_amplitude = effect_params.movement_amplitude * 100
            x_movement = amplitude_x * np.sin(time * speed_x)
            # Сглаживаем зигзагообразное движение
            raw_sawtooth = sawtooth(time * speed_y * 0.8, width=0.5)
            kernel_size = 5
            smoothed_sawtooth = np.convolve(raw_sawtooth, np.ones(kernel_size)/kernel_size, mode='same')
            y_movement = zigzag_amplitude * smoothed_sawtooth

        elif effect_params.movement_pattern == 'spiral':
            # Корректируем вычисление радиуса
            max_amplitude = np.maximum(amplitude_x, amplitude_y)
            radius = np.linspace(0, max_amplitude, total_frames)
            # Добавляем плавное замедление в конце
            radius *= (1 - np.power(np.linspace(0, 1, total_frames), 2) * 0.3)
            angle = time * speed_x
            x_movement = radius * np.cos(angle)
            y_movement = radius * np.sin(angle)

        elif effect_params.movement_pattern == 'combination':
            x_movement = (
                (amplitude_x * 0.5 * np.sin(time * speed_x)) +
                (amplitude_x * 0.3 * np.cos(time * speed_x * 1.3)) +
                (amplitude_x * 0.2 * np.sin(time * speed_x * 1.7))
            )
            y_movement = (
                (amplitude_y * 0.5 * np.cos(time * speed_y)) +
                (amplitude_y * 0.3 * np.sin(time * speed_y * 1.3)) +
                (amplitude_y * 0.2 * np.cos(time * speed_y * 1.7))
            )

        else:  # random pattern
            x_movement = amplitude_x * np.sin(time * speed_x + np.random.uniform(0, np.pi))
            y_movement = amplitude_y * np.cos(time * speed_y + np.random.uniform(0, np.pi))

        # Добавляем плавное затухание в начале и конце для всех паттернов
        fade_length = total_frames // 10
        fade_in = np.linspace(0, 1, fade_length)
        fade_out = np.linspace(1, 0, fade_length)
        fade_mask = np.ones(total_frames)
        fade_mask[:fade_length] = fade_in
        fade_mask[-fade_length:] = fade_out

        x_movement *= fade_mask
        y_movement *= fade_mask

        # Финальное сглаживание
        kernel_size = 5
        x_movement = np.convolve(x_movement, np.ones(kernel_size)/kernel_size, mode='same')
        y_movement = np.convolve(y_movement, np.ones(kernel_size)/kernel_size, mode='same')

        return x_movement.astype(int).tolist(), y_movement.astype(int).tolist()

    def _apply_metadata(self, temp_output: str, output_file: str, metadata: Dict[str, str]):
        """
        Применяет метаданные к видео с учетом настроек удаления и генерации метаданных

        Args:
            temp_output (str): Путь к временному файлу
            output_file (str): Путь к выходному файлу
            metadata (Dict[str, str]): Словарь с новыми метаданными
        """
        try:
            # Формируем базовую команду ffmpeg
            ffmpeg_command = [
                "ffmpeg",
                "-i", temp_output
            ]

            # Управление метаданными на основе настроек
            if self.config.metadata_remove_original:  # если enabled=True
                # Удаляем все существующие метаданные
                ffmpeg_command.extend(["-map_metadata", "-1"])

                # Добавляем новые метаданные только если generate=True
                if self.config.metadata_generate_new:
                    for key, value in metadata.items():
                        # Экранируем специальные символы в значениях метаданных
                        escaped_value = str(value).replace('"', '\\"')
                        ffmpeg_command.extend(["-metadata", f"{key}={escaped_value}"])
            else:
                # Если enabled=False, сохраняем существующие метаданные
                ffmpeg_command.extend(["-map_metadata", "0"])

            # Добавляем остальные параметры кодирования
            ffmpeg_command.extend([
                "-c", "copy",  # Копируем потоки без перекодирования
                "-movflags", "+use_metadata_tags",  # Включаем поддержку метаданных
                "-write_xing", "0",  # Предотвращаем удаление некоторых метаданных MP4
                "-map", "0",  # Копируем все потоки
                "-y",  # Перезаписываем существующий файл
                output_file
            ])

            # Запускаем процесс ffmpeg
            result = subprocess.run(
                ffmpeg_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                check=True
            )

            # Проверяем метаданные только если включена их генерация
            if self.config.metadata_generate_new:
                ffprobe_command = [
                    "ffprobe",
                    "-v", "quiet",
                    "-print_format", "json",
                    "-show_format",
                    output_file
                ]

                probe_result = subprocess.run(
                    ffprobe_command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )

                if probe_result.returncode == 0:
                    probe_data = json.loads(probe_result.stdout)
                    output_metadata = probe_data.get('format', {}).get('tags', {})

                    # Проверяем наличие всех метаданных
                    missing_metadata = []
                    for key, value in metadata.items():
                        if key not in output_metadata:
                            missing_metadata.append(key)

                    if missing_metadata:
                        print(f"Предупреждение: следующие метаданные не были записаны: {', '.join(missing_metadata)}")
                else:
                    print("Предупреждение: не удалось проверить метаданные")

        except subprocess.CalledProcessError as e:
            print(f"Ошибка при применении метаданных: {e.stderr}")
            print("Копирование файла без метаданных...")
            shutil.copy2(temp_output, output_file)
        except Exception as e:
            print(f"Неожиданная ошибка при применении метаданных: {str(e)}")
            print("Копирование файла без метаданных...")
            shutil.copy2(temp_output, output_file)

    def _delete_used_background(self):
        """Удаляет использованный фоновый файл"""
        try:
            if self.current_bg_path and self.current_bg_path.exists():
                os.remove(self.current_bg_path)
                #print(f"Удален использованный фон: {self.current_bg_path.name}")
                # Remove the background from the cached list
                self.backgrounds = [(bg, path) for bg, path in self.backgrounds if path != self.current_bg_path]
                if self.current_bg_index >= len(self.backgrounds):
                    self.current_bg_index = 0
        except Exception as e:
            print(f"Ошибка при удалении фона: {str(e)}")

    def _randomly_remove_frames(self, input_file: str, output_file: str, removal_percentage: float) -> bool:
        """
        Случайно удаляет заданный процент кадров, не нарушая целостности видео.

        Args:
            input_file (str): Путь к входному видео
            output_file (str): Путь к выходному видео
            removal_percentage (float): Процент кадров для удаления (0.5 - 3.0)

        Returns:
            bool: Успешность операции
        """
        try:
            # Открываем входное видео
            cap = cv2.VideoCapture(input_file)
            if not cap.isOpened():
                raise ValueError("Не удалось открыть видео")

            # Получаем параметры видео
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # Вычисляем количество кадров для удаления
            frames_to_remove = int(total_frames * removal_percentage / 100)

            # Генерируем случайные индексы кадров для удаления
            # Исключаем первые и последние 5% кадров для сохранения структуры
            safe_start = int(total_frames * 0.05)
            safe_end = int(total_frames * 0.95)
            removable_frames = list(range(safe_start, safe_end))

            # Случайно выбираем кадры для удаления
            frames_to_remove_indices = random.sample(removable_frames, min(frames_to_remove, len(removable_frames)))
            frames_to_remove_indices.sort(reverse=True)  # Сортируем в обратном порядке

            # Подготавливаем writer для выходного видео
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_file, fourcc, fps, (width, height))

            # Перебираем кадры
            for frame_index in range(total_frames):
                ret, frame = cap.read()
                if not ret:
                    break

                # Пропускаем кадры, отмеченные для удаления
                if frame_index in frames_to_remove_indices:
                    continue

                out.write(frame)

            cap.release()
            out.release()

            return True

        except Exception as e:
            print(f"Ошибка при удалении кадров: {str(e)}")
            return False

    def process_video(self, input_file: str, effect_params: Optional[VideoEffectParams] = None,
                    encoding_params: Optional[EncodingParams] = None, iteration: int = 1):
        temp_files = []
        process_id = None
        try:
            # Инициализируем менеджер процессов если еще не создан
            if not hasattr(self, 'uniquification_manager'):
                self.uniquification_manager = UniquificationManager()

            # Регистрируем процесс в менеджере, добавляем os.getpid()
            process_id = self.uniquification_manager.register_process(
                video_path=input_file,
                total_iterations=self.config.iterations or 1,
                pid=os.getpid()  # Добавляем текущий PID процесса
            )

            #print(f"Обработка видео #{iteration}")

            if not os.path.exists(input_file):
                raise FileNotFoundError(f"Входной файл не найден: {input_file}")

            # Check file size
            file_size = os.path.getsize(input_file) / (1024 * 1024)  # Convert to MB
            if file_size > 100:
                raise ValueError(f"Размер файла ({file_size:.1f} МБ) превышает лимит в 100 МБ")

            effect_params = effect_params or self.config.generate_effect_params()
            encoding_params = encoding_params or EncodingParams()

            # Настройка параметров сжатия в зависимости от выбранного уровня
            if hasattr(effect_params, 'compression_level'):
                if effect_params.compression_level == "none":
                    # Без сжатия - сохраняем оригинальное качество
                    encoding_params.preset = "ultrafast"
                    encoding_params.crf = 0
                    encoding_params.maxrate = "0"
                    encoding_params.bufsize = "0"
                    encoding_params.codec = "libx264rgb"  # Кодек без потери качества

                elif effect_params.compression_level == "high":
                    # Сильное сжатие
                    encoding_params.preset = "veryslow"
                    encoding_params.crf = 35
                    encoding_params.maxrate = "1M"
                    encoding_params.bufsize = "2M"

            # Загружаем изображение для наложения, если эффект включен
            if effect_params.png_overlay_enabled:
                success = self.png_overlay_effect.load_random_overlay()
                if not success:
                    print("Предупреждение: не удалось загрузить изображение для наложения")
                    effect_params.png_overlay_enabled = False  # Отключаем эффект, если изображение не загружено

            self.video_overlay_effect = VideoOverlayEffect(str(self.base_dir))
            # Загружаем видео для наложения, если эффект включен
            if effect_params.video_overlay_enabled:
                success = self.video_overlay_effect.load_random_overlay()
                if not success:
                    print("Предупреждение: не удалось загрузить видео для наложения")
                    effect_params.video_overlay_enabled = False

            # Создаем временные файлы
            random_suffix = random.randint(1000, 9999)

            # Добавляем файл для предварительно сжатого видео
            compressed_input = str(self.temp_dir / f"compressed_input_{random_suffix}.mp4")
            temp_video = str(self.temp_dir / f"temp_video_{random_suffix}.mp4")
            temp_audio = str(self.temp_dir / f"temp_audio_{random_suffix}.wav")
            processed_audio = str(self.temp_dir / f"processed_audio_{random_suffix}.wav")
            final_temp = str(self.temp_dir / f"final_{random_suffix}.mp4")
            output_file = str(self.output_dir / f"IMG_{random_suffix}.mp4")

            temp_files.extend([compressed_input, temp_video, temp_audio, processed_audio, final_temp])

            # Проверяем флаг остановки
            if self.uniquification_manager.stop_flag.is_set():
                return False

            # Изменяем 1/6 этап - Предварительное сжатие видео
            print("1/6 | Предварительное сжатие видео...")
            self.uniquification_manager.update_progress(process_id, iteration, 5)

            # Используем ffmpeg для предварительного сжатия видео с учетом уровня сжатия
            if hasattr(effect_params, 'compression_level') and effect_params.compression_level == "none":
                # Без сжатия - просто копируем файл с исходным качеством
                shutil.copy2(input_file, compressed_input)
            else:
                # Используем настроенные параметры сжатия
                compress_command = [
                    "ffmpeg", "-i", input_file,
                    "-c:v", encoding_params.codec,
                    "-preset", encoding_params.preset
                ]

                if encoding_params.crf > 0:
                    compress_command.extend(["-crf", str(encoding_params.crf)])

                if encoding_params.maxrate != "0":
                    compress_command.extend([
                        "-maxrate", encoding_params.maxrate,
                        "-bufsize", encoding_params.bufsize
                    ])

                compress_command.extend([
                    "-tune", encoding_params.tune,
                    "-profile:v", "high", "-level", "4.1",
                    "-g", "48", "-keyint_min", "48",
                    "-sc_threshold", "0", "-bf", "3",
                    # Копируем аудио без перекодирования на этом этапе
                    "-c:a", "copy",
                    "-y", compressed_input
                ])

                subprocess.run(compress_command, check=True, capture_output=True)

            # Теперь используем сжатое видео как входное для дальнейшей обработки
            input_file = compressed_input

            # Инициализация очередей
            frame_queue = queue.Queue(maxsize=30)
            preprocessing_queue = queue.Queue(maxsize=30)
            processing_queue = queue.Queue(maxsize=30)
            result_queue = queue.Queue(maxsize=30)

            # 2/6 | Извлечение аудио из предварительно сжатого видео
            print("2/6 | Извлечение аудио...")
            self.uniquification_manager.update_progress(process_id, iteration, 15)
            temp_audio = self.audio_processor.extract_audio(input_file)

            # Асинхронная обработка аудио
            with ThreadPoolExecutor(max_workers=1) as executor:
                audio_future = executor.submit(self.audio_processor.process_audio, temp_audio)

            # 3/6 | Обработка видео...
            print("3/6 | Обработка видео...")
            self.uniquification_manager.update_progress(process_id, iteration, 25)
            cap = cv2.VideoCapture(str(input_file))
            if not cap.isOpened():
                raise ValueError("Не удалось открыть входное видео")

            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            target_width, target_height = 1080, 1920

            # Настройка видео writer
            fourcc = cv2.VideoWriter_fourcc(*'avc1') if self.gpu_available else cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(temp_video, fourcc, fps, (target_width, target_height))

            # Создаем координатор движения
            motion_coordinator = MotionCoordinator(total_frames, effect_params)

            # Инициализируем фон с координатором
            background = self.get_next_background()
            background = cv2.resize(background, (target_width, target_height))
            background_manager = DynamicBackground((background, self.current_bg_path),
                                                    total_frames,
                                                    motion_coordinator)
            wave_distortion_effect = WaveDistortionEffect(target_width, target_height)
            snow_effect = SnowEffect(target_width, target_height)

            puzzle_effect = PuzzleEffect(target_width, target_height)
            if effect_params.puzzle_enabled:
                puzzle_effect.initialize(
                    rows=effect_params.puzzle_rows,
                    pattern=effect_params.puzzle_pattern,
                    total_frames=total_frames,
                    cycle_speed=effect_params.puzzle_cycle_speed,
                    visible_percent=effect_params.puzzle_visible_percent
                )


            if effect_params.snowfall_intensity > 0:
                snow_effect.current_snowfall_intensity = effect_params.snowfall_intensity
                snow_effect.snowflake_size = effect_params.snowflake_size
                snow_effect.snowflake_opacity = effect_params.snowflake_opacity
                snow_effect.current_wind_strength = effect_params.wind_strength

            # Вычисление размеров кадра
            ret, first_frame = cap.read()
            if not ret:
                raise ValueError("Не удалось прочитать первый кадр")

            original_height, original_width = first_frame.shape[:2]
            aspect_ratio = original_width / original_height

            if aspect_ratio > (target_width / target_height):
                scaled_width = int(target_width * effect_params.scale)
                scaled_height = int(scaled_width / aspect_ratio)
            else:
                scaled_height = int(target_height * effect_params.scale)
                scaled_width = int(scaled_height * aspect_ratio)

            scaled_width = min(scaled_width, target_width)
            scaled_height = min(scaled_height, target_height)

            frame_size = (scaled_width, scaled_height)
            base_x_offset = (target_width - scaled_width) // 2
            base_y_offset = (target_height - scaled_height) // 2

            # Инициализация эффекта линий
            lines_effect = LinesEffect(scaled_width, scaled_height)
            lines_effect.initialize(effect_params)

            emoji_effect = None
            if effect_params.emoji_enabled:
                emoji_dir = self.base_dir / "emoji"
                # Проверяем, есть ли в папке изображения
                emoji_files = list(emoji_dir.glob("*.png")) + list(emoji_dir.glob("*.jpg")) + list(emoji_dir.glob("*.jpeg"))

                if not emoji_files:
                    print("Предупреждение: папка emoji пуста, эффект не будет применен")
                else:
                    emoji_effect = EmojiEffect(target_width, target_height)
                    emoji_effect.load_emojis(str(emoji_dir))
                    emoji_effect.initialize(
                        count=effect_params.emoji_count,
                        size=effect_params.emoji_size,
                        speed=effect_params.emoji_speed,
                        rotation_speed=effect_params.emoji_rotation_speed
                    )

            # Определение рабочих функций
            def frame_reader():
                frame_count = 0
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                while cap.isOpened() and not self.shutdown_flag.is_set() and not self.uniquification_manager.stop_flag.is_set():
                    ret, frame = cap.read()
                    if not ret:
                        break
                    frame_queue.put((frame_count, frame))
                    frame_count += 1
                frame_queue.put(None)

            def frame_preprocessor():
                while not self.shutdown_flag.is_set() and not self.uniquification_manager.stop_flag.is_set():
                    item = frame_queue.get()
                    if item is None:
                        preprocessing_queue.put(None)
                        break
                    idx, frame = item
                    frame = cv2.resize(frame, frame_size)
                    preprocessing_queue.put((idx, frame))

            def frame_processor():
                while not self.shutdown_flag.is_set() and not self.uniquification_manager.stop_flag.is_set():
                    item = preprocessing_queue.get()
                    if item is None:
                        processing_queue.put(None)
                        break

                    idx, frame = item

                    try:
                        if self.use_gpu:
                            processed_frame = self.process_frame_gpu(frame, effect_params)
                        else:
                            processed_frame = self.effect_processor.apply_effects_batch(frame, effect_params)

                        if processed_frame is not None:
                            processing_queue.put((idx, processed_frame))
                        else:
                            print(f"Ошибка при обработке кадра {idx}")
                            processed_frame = self.effect_processor.apply_effects_batch(frame, effect_params)
                            processing_queue.put((idx, processed_frame))

                    except Exception as e:
                        print(f"Ошибка при обработке кадра {idx}: {str(e)}")
                        try:
                            processed_frame = self.effect_processor.apply_effects_batch(frame, effect_params)
                            processing_queue.put((idx, processed_frame))
                        except Exception as e:
                            print(f"Критическая ошибка при обработке кадра {idx}: {str(e)}")
                            continue

            # Найдите в методе process_video класса VideoUniquifier часть, где происходит обработка кадра
            # и примерно такой код в функции frame_postprocessor():

            def frame_postprocessor():
                with tqdm(total=total_frames, desc="Обработка видео", unit="кадр",
                          bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]") as pbar:
                    processed_frames = 0
                    while not self.shutdown_flag.is_set() and not self.uniquification_manager.stop_flag.is_set():
                        item = processing_queue.get()
                        if item is None:
                            result_queue.put(None)
                            break

                        idx, processed_frame = item
                        background, is_finished = background_manager.get_frame()
                        result = background.copy()

                        try:
                            # Получаем параметры движения
                            frame_params = motion_coordinator.get_frame_parameters(idx)
                            x_offset = base_x_offset + frame_params['x_offset']
                            y_offset = base_y_offset + frame_params['y_offset']

                            # Проверяем границы
                            x_offset = max(0, min(x_offset, target_width - scaled_width))
                            y_offset = max(0, min(y_offset, target_height - scaled_height))

                            processed_height, processed_width = processed_frame.shape[:2]
                            base_mask = np.ones((processed_height, processed_width), dtype=np.float32)

                            # Создаем маски
                            video_mask = np.ones((processed_height, processed_width), dtype=np.uint8) * 255

                            gradient_width = int(min(processed_width, processed_height) * 0.1)
                            kernel_size = (gradient_width * 2 + 1, gradient_width * 2 + 1)
                            edge_gradient = cv2.GaussianBlur(base_mask, kernel_size, gradient_width)

                            current_radius = self.effect_processor.get_animated_border_radius(idx, fps, effect_params)
                            corner_mask = self.effect_processor.create_rounded_mask(processed_width, processed_height, current_radius)

                            final_mask = base_mask * effect_params.transparency * edge_gradient * corner_mask
                            final_mask = final_mask[:, :, np.newaxis]

                            if effect_params.lines_enabled:
                                processed_frame = lines_effect.apply(processed_frame, video_mask, effect_params)

                            # Используем метод apply для наложения PNG-изображения
                            if effect_params.png_overlay_enabled and hasattr(self, 'png_overlay_effect'):
                                processed_frame = self.png_overlay_effect.apply(processed_frame, effect_params)

                            if effect_params.puzzle_enabled:
                                # Применяем эффект пазла и получаем маску прозрачности
                                processed_frame, puzzle_mask = puzzle_effect.apply(processed_frame, idx)

                                # Обновляем маску с учетом маски пазла
                                if len(processed_frame.shape) == 3 and processed_frame.shape[2] == 3:
                                    # Преобразуем маску в трехканальную, если это цветное видео
                                    if len(puzzle_mask.shape) == 2:
                                        puzzle_mask = np.expand_dims(puzzle_mask, axis=2)
                                    if puzzle_mask.shape[2] == 1:
                                        puzzle_mask = np.repeat(puzzle_mask, 3, axis=2)

                                # Применяем маску к final_mask
                                # Если есть переменная final_mask, используем её
                                if 'final_mask' in locals():
                                    final_mask = final_mask * puzzle_mask
                                # В противном случае создаём новую
                                else:
                                    final_mask = puzzle_mask

                            roi = result[y_offset:y_offset + processed_height,
                                  x_offset:x_offset + processed_width]

                            if roi.shape != processed_frame.shape:
                                continue

                            processed_frame = processed_frame * corner_mask[:, :, np.newaxis]

                            result[y_offset:y_offset + processed_height,
                            x_offset:x_offset + processed_width] = \
                                (roi * (1 - final_mask) + processed_frame * final_mask)

                            # Применяем наложение видео если эффект включен
                            if effect_params.video_overlay_enabled and hasattr(self, 'video_overlay_effect'):
                                result = self.video_overlay_effect.apply(
                                    result,
                                    effect_params,
                                    idx,
                                    roi_coords=(x_offset, y_offset, processed_width, processed_height)
                                )

                            if emoji_effect is not None:
                                emoji_effect.update()
                                result = emoji_effect.apply(result, effect_params.emoji_opacity)

                            if effect_params.snowfall_intensity > 0:
                                result = snow_effect.apply(result.astype(np.uint8), idx)

                            if effect_params.wave_distortion_enabled:
                                result = wave_distortion_effect.apply(result, effect_params)

                            result_queue.put((idx, result))

                            processed_frames += 1
                            progress = (processed_frames / total_frames) * 40 + 25
                            self.uniquification_manager.update_progress(process_id, iteration, progress)
                            pbar.update(1)

                        except Exception as e:
                            print(f"Ошибка при постобработке кадра {idx}: {str(e)}")
                            continue

            # Запуск обработки с ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=5) as executor:
                # Получаем параметры аудио из конфигурации
                audio_params = self.config.get_audio_params()

                # Запускаем все процессы
                reader_future = executor.submit(frame_reader)
                preprocessor_future = executor.submit(frame_preprocessor)
                processor_future = executor.submit(frame_processor)
                postprocessor_future = executor.submit(frame_postprocessor)
                audio_future = executor.submit(self.audio_processor.process_audio, temp_audio, audio_params)

                while not self.shutdown_flag.is_set() and not self.uniquification_manager.stop_flag.is_set():
                    try:
                        result = result_queue.get(timeout=1)
                        if result is None:
                            break

                        idx, processed_frame = result
                        out.write(processed_frame)

                    except queue.Empty:
                        continue

                if not self.shutdown_flag.is_set() and not self.uniquification_manager.stop_flag.is_set():
                    processed_audio = audio_future.result()
                    reader_future.result()
                    preprocessor_future.result()
                    processor_future.result()
                    postprocessor_future.result()

            cap.release()
            out.release()

            if self.shutdown_flag.is_set() or self.uniquification_manager.stop_flag.is_set():
                return False

            # 4/6 | Финальная обработка
            print("4/6 | Финальная обработка...")
            self.uniquification_manager.update_progress(process_id, iteration, 70)
            if random.random() < effect_params.frame_removal_probability:
                temp_video_with_removed = str(self.temp_dir / f"temp_video_removed_{random_suffix}.mp4")
                temp_files.append(temp_video_with_removed)
                if self._randomly_remove_frames(temp_video, temp_video_with_removed, effect_params.frame_removal_percentage):
                    print(f"Удалено {effect_params.frame_removal_percentage:.1f}% кадров")
                    temp_video = temp_video_with_removed

            # 5/6 | Объединение видео и аудио
            print("5/6 | Объединение видео и аудио...")
            self.uniquification_manager.update_progress(process_id, iteration, 85)
            ffmpeg_command = [
                "ffmpeg", "-i", temp_video, "-i", processed_audio,
                "-c:v", "copy",  # Копируем видео без повторного сжатия
                "-c:a", "aac",
                "-b:a", encoding_params.audio_bitrate,
                "-ac", str(encoding_params.audio_channels),
                "-ar", str(encoding_params.audio_sample_rate),
                "-map", "0:v:0", "-map", "1:a:0",
                "-shortest", "-y", final_temp
            ]

            subprocess.run(ffmpeg_command, check=True, capture_output=True)

            # 6/6 | Применение метаданных
            print("6/6 | Применение метаданных...")
            self.uniquification_manager.update_progress(process_id, iteration, 95)
            metadata = MetadataGenerator.generate_metadata()
            self._apply_metadata(final_temp, output_file, metadata)

            # Удаляем использованные изображения, если нужно
            if effect_params.png_overlay_enabled and hasattr(self.config, 'png_overlay_delete_used') and self.config.png_overlay_delete_used:
                deleted_count = self.png_overlay_effect.delete_used_overlays()
                if deleted_count > 0:
                    print(f"Удалено {deleted_count} использованных изображений наложения")

            # Удаляем использованные видео, если нужно
            if effect_params.video_overlay_enabled and hasattr(self.config, 'video_overlay_delete_used') and self.config.video_overlay_delete_used:
                deleted_count = self.video_overlay_effect.delete_used_overlays()
                if deleted_count > 0:
                    print(f"Удалено {deleted_count} использованных видео наложения")

            # После успешной обработки удаляем использованные фоны
            used_backgrounds = background_manager.get_used_backgrounds()
            for bg_path in used_backgrounds:
                try:
                    if bg_path.exists():
                        os.remove(bg_path)
                        #print(f"Удален использованный фон: {bg_path.name}")
                        # Удаляем фон из списка backgrounds
                        self.backgrounds = [(bg, path) for bg, path in self.backgrounds if path != bg_path]
                except Exception as e:
                    print(f"Ошибка при удалении фона {bg_path}: {str(e)}")

            # Завершение обработки
            self.uniquification_manager.update_progress(process_id, iteration, 100)

            print(f"✓ Готово: {output_file}")
            return True

        except Exception as e:
            print(f"\n❌ Ошибка: {str(e)}")
            print_exc()
            return False

        finally:
            self._cleanup_temp_files(temp_files)
            # Очищаем информацию о процессе
            if process_id and hasattr(self, 'uniquification_manager'):
                self.uniquification_manager.update_progress(
                    process_id=process_id,
                    iteration=iteration,
                    progress=100.0  # Помечаем как завершенный
                )


