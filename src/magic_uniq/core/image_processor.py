import os
import random
from PIL import Image, ImageFilter, ImageDraw, ImageChops
import math
import string
import multiprocessing
from threading import Thread
import time
import sys
import argparse
from PIL import ExifTags
from PIL.ExifTags import TAGS
import piexif
from datetime import datetime, timedelta
import numpy as np
import json
import colorsys
from numpy.fft import fft2, ifft2, fftshift, ifftshift
import os
import cv2


class UniqualizerConfig:
    def __init__(self):
        # Используем папку data/ для всех файлов приложения
        from pathlib import Path
        self.base_dir = Path.cwd() / "data"

        # Создаем базовую директорию data, если она не существует
        self.base_dir.mkdir(exist_ok=True)

        # Добавляем атрибуты для совместимости с GUI, но не используем для создания папок
        self.input_dir = self.base_dir / "input_images"  # для совместимости
        self.output_dir = self.base_dir / "output_images"  # для совместимости
        self.emoji_dir = self.base_dir / "emojis"

        # Определяем директорию для пнг фото
        self.overlay_dir = self.base_dir / "overlay_images"

        # Добавляем директорию для конфигураций
        self.config_dir = self.base_dir / "configs"

        # Директория для выходных видео
        self.video_dir = self.base_dir / "output_videos"
        self.video_dir.mkdir(exist_ok=True)

        # Создаем необходимые директории при инициализации
        os.makedirs(self.emoji_dir, exist_ok=True)
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.overlay_dir, exist_ok=True)  # Создаем директорию для PNG оверлеев

        # Для поддержки папок input1-3 и output1-3
        for i in range(1, 4):
            input_folder = os.path.join(self.base_dir, f"input_{i}")
            output_folder = os.path.join(self.base_dir, f"output_{i}")
            os.makedirs(input_folder, exist_ok=True)
            os.makedirs(output_folder, exist_ok=True)

        # Метаданные
        self.metadata_enabled = False
        self.remove_original_metadata = True
        self.add_custom_metadata = True

        # Эмодзи
        self.emoji_enabled = False
        self.emoji_count_range = (5, 10)  # эмодзи
        self.emoji_opacity_range = (50, 80)  # прозрачность
        self.emoji_size_range = (1, 3)  # размер

        # Прозрачность
        self.opacity_enabled = False
        self.opacity_range = (50, 70)  # прозрачность

        # Фон
        self.background_enabled = False
        self.blur_range = (1, 2)  #  размытие
        self.background_opacity_range = (70, 90)  #  прозрачность
        self.background_size_range = (10, 20)  #  отступ

        # Смещение изображения
        self.shift_enabled = False
        self.vertical_shift_range = (-10, 10)    #  смещение
        self.horizontal_shift_range = (-10, 10)  #  смещение

        # Поворот изображения
        self.rotation_enabled = False
        self.rotation_angle_range = (-1, 1)

        # Скругление углов
        self.corner_rounding_enabled = False
        self.corner_radius_range = (3, 5)  #  радиус

        # Шум
        self.noise_enabled = False
        self.noise_level_range = (1, 2)
        self.colored_noise = False

        # Наложение линий
        self.lines_enabled = False
        self.lines_count_range = (1, 3)  # линий
        self.line_thickness_range = (0.1, 0.5)  #  линии
        self.line_opacity_range = (20, 50)  #  прозрачность
        self.line_color_range = ((0, 255), (0, 255), (0, 255))

        # RGB Shift
        self.rgb_shift_enabled = False
        self.rgb_shift_range = (-0.5, 0.5)  # смещение
        self.rgb_shift_opacity = (30, 50)  # прозрачность

        # Блики
        self.flare_enabled = False
        self.flare_count_range = (1, 2)  # бликов
        self.flare_size_range = (3, 8)  # размер
        self.flare_opacity_range = (40, 60)  # прозрачность

        # Фрактальные узоры
        self.fractal_enabled = False
        self.fractal_opacity_range = (10, 30)  # прозрачность

        # Чередование чёрных и белых частиц
        self.particles_enabled = False
        self.particles_count_range = (10, 20)  # частицы
        self.particle_size_range = (5, 8)  # размер
        self.particle_opacity_range = (50, 70)  #  прозрачность

        # Гауссовский шум
        self.gaussian_noise_enabled = False
        self.gaussian_noise_level_range = (10, 15)  #  интенсивность
        self.gaussian_colored_noise = False

        # Цветная мозаика
        self.mosaic_enabled = False
        self.mosaic_size_range = (5, 15)  # размер
        self.mosaic_opacity_range = (20, 50)  #  прозрачность
        self.mosaic_saturation_range = (30, 70)  #  насыщенность
        self.mosaic_brightness_range = (30, 70)  # яркость
        self.mosaic_pattern_types = ['random', 'gradient', 'chess']
        self.mosaic_shape_type = 'square'

        # Фурье-шум
        self.fourier_noise_enabled = False
        self.fourier_noise_amplitude_range = (0.05, 0.2)  # Меньшая амплитуда
        self.fourier_frequency_range = (3.0, 8.0)  # Меньшая частота
        self.fourier_pattern_type = 'random'
        self.fourier_patterns = ['random', 'rings', 'sectors', 'directional', 'fractal']

        # Перестановка пикселей
        self.pixel_shuffle_enabled = False
        self.pixel_shuffle_block_range = (2, 4)  # размер блока
        self.pixel_shuffle_probability_range = (0.05, 0.2)  # вероятность

        # Шум Халтона
        self.halton_noise_enabled = False
        self.halton_noise_points = (500, 1000)  # количество точек
        self.halton_noise_size = (1, 2)  #  размер
        self.halton_noise_opacity = (20, 40)  # прозрачность
        self.halton_noise_color_mode = 'random'

        # Манипуляция битовыми плоскостями
        self.bit_plane_enabled = False
        self.bit_plane_intensity_range = (2, 4)  # интенсивность
        self.bit_manipulation_mode = 'random'

        # Рамка
        self.frame_enabled = False
        self.frame_width_range = (5, 15)  # ширина рамки в пикселях
        self.frame_color = 'random'  # Теперь храним название цвета вместо диапазона RGB
        self.frame_opacity_range = (50, 100)  # прозрачность рамки (1-100%)
        self.frame_colors = ['random', 'black', 'white', 'gray', 'silver', 'red', 'maroon', 'crimson', 'green', 'olive', 'lime', 'teal', 'blue', 'navy', 'indigo', 'purple', 'violet', 'yellow', 'gold', 'khaki', 'orange', 'brown', 'pink', 'magenta', 'cyan', 'turquoise']

        #параметр формата изображения
        self.output_format = "JPEG"  #По умолчанию JPEG

        # Добавляем новое поле для имени пресета
        self.preset_name = "Стандартный пресет"

        # Наложение PNG изображений
        self.overlay_enabled = False
        self.overlay_dir = os.path.join(self.base_dir, "overlay_images")
        self.overlay_size_range = (-50, 100)
        self.overlay_only_mode = False
        self.overlay_y_offset_range = (-50, 50)

        #РАзмер
        self.image_aspect_ratio = "9:16"

        # Параметры для видео
        self.slideshow_photo_duration = 1

    def to_dict(self):
        """Преобразует конфигурацию в словарь для сохранения"""
        return {
            # Основные параметры - преобразуем Path объекты в строки
            'base_dir': str(self.base_dir),
            'input_dir': str(self.input_dir),
            'output_dir': str(self.output_dir),
            'emoji_dir': str(self.emoji_dir),
            'config_dir': str(self.config_dir),

            # Добавляем имя пресета
            'preset_name': self.preset_name,

            # Метаданные
            'metadata_enabled': self.metadata_enabled,
            'remove_original_metadata': self.remove_original_metadata,
            'add_custom_metadata': self.add_custom_metadata,

            # Эмодзи
            'emoji_enabled': self.emoji_enabled,
            'emoji_count_range': list(self.emoji_count_range),  # Преобразуем в список
            'emoji_opacity_range': list(self.emoji_opacity_range),
            'emoji_size_range': list(self.emoji_size_range),

            # Прозрачность
            'opacity_enabled': self.opacity_enabled,
            'opacity_range': list(self.opacity_range),

            # Фон
            'background_enabled': self.background_enabled,
            'blur_range': list(self.blur_range),
            'background_opacity_range': list(self.background_opacity_range),
            'background_size_range': list(self.background_size_range),

            # Смещение
            'shift_enabled': self.shift_enabled,
            'vertical_shift_range': list(self.vertical_shift_range),
            'horizontal_shift_range': list(self.horizontal_shift_range),

            # Поворот
            'rotation_enabled': self.rotation_enabled,
            'rotation_angle_range': list(self.rotation_angle_range),

            # Углы
            'corner_rounding_enabled': self.corner_rounding_enabled,
            'corner_radius_range': list(self.corner_radius_range),

            # Шум
            'noise_enabled': self.noise_enabled,
            'noise_level_range': list(self.noise_level_range),
            'colored_noise': self.colored_noise,

            # Линии
            'lines_enabled': self.lines_enabled,
            'lines_count_range': list(self.lines_count_range),
            'line_thickness_range': list(self.line_thickness_range),
            'line_opacity_range': list(self.line_opacity_range),
            'line_color_range': [list(x) for x in self.line_color_range],

            # RGB смещение
            'rgb_shift_enabled': self.rgb_shift_enabled,
            'rgb_shift_range': list(self.rgb_shift_range),
            'rgb_shift_opacity': list(self.rgb_shift_opacity),

            # Блики
            'flare_enabled': self.flare_enabled,
            'flare_count_range': list(self.flare_count_range),
            'flare_size_range': list(self.flare_size_range),
            'flare_opacity_range': list(self.flare_opacity_range),

            # Фракталы
            'fractal_enabled': self.fractal_enabled,
            'fractal_opacity_range': list(self.fractal_opacity_range),

            # Чередование чёрных и белых частиц
            'particles_enabled': self.particles_enabled,
            'particles_count_range': list(self.particles_count_range),
            'particle_size_range': list(self.particle_size_range),
            'particle_opacity_range': list(self.particle_opacity_range),

            # Гауссовский шум
            'gaussian_noise_enabled': self.gaussian_noise_enabled,
            'gaussian_noise_level_range': list(self.gaussian_noise_level_range),
            'gaussian_colored_noise': self.gaussian_colored_noise,

            # Цветная мозаика
            'mosaic_enabled': self.mosaic_enabled,
            'mosaic_size_range': list(self.mosaic_size_range),
            'mosaic_opacity_range': list(self.mosaic_opacity_range),
            'mosaic_saturation_range': list(self.mosaic_saturation_range),
            'mosaic_brightness_range': list(self.mosaic_brightness_range),
            'mosaic_pattern_types': self.mosaic_pattern_types,
            'mosaic_shape_type': self.mosaic_shape_type,

            # Фурье шум
            'fourier_noise_enabled': self.fourier_noise_enabled,
            'fourier_noise_amplitude_range': list(self.fourier_noise_amplitude_range),
            'fourier_frequency_range': list(self.fourier_frequency_range),
            'fourier_pattern_type': self.fourier_pattern_type,
            'fourier_patterns': self.fourier_patterns,
            'output_format': self.output_format,

            # Перестановка пикселей
            'pixel_shuffle_enabled': self.pixel_shuffle_enabled,
            'pixel_shuffle_block_range': list(self.pixel_shuffle_block_range),
            'pixel_shuffle_probability_range': list(self.pixel_shuffle_probability_range),

            # Шум Халтона
            'halton_noise_enabled': self.halton_noise_enabled,
            'halton_noise_points': list(self.halton_noise_points),
            'halton_noise_size': list(self.halton_noise_size),
            'halton_noise_opacity': list(self.halton_noise_opacity),
            'halton_noise_color_mode': self.halton_noise_color_mode,

            # Рамка
            'frame_enabled': self.frame_enabled,
            'frame_width_range': list(self.frame_width_range),
            'frame_color': self.frame_color,
            'frame_opacity_range': list(self.frame_opacity_range),
            'frame_colors': self.frame_colors,

            # Манипуляции битовыми плоскостями
            'bit_plane_enabled': self.bit_plane_enabled,
            'bit_plane_intensity_range': list(self.bit_plane_intensity_range),
            'bit_manipulation_mode': self.bit_manipulation_mode,

            # Наложение PNG изображений
            'overlay_enabled': self.overlay_enabled,
            'overlay_dir': self.overlay_dir,
            'overlay_size_range': list(self.overlay_size_range),
            'overlay_only_mode': self.overlay_only_mode,
            'overlay_y_offset_range': list(self.overlay_y_offset_range),

            #РАзмер
            'image_aspect_ratio': self.image_aspect_ratio,

            #Длительность видео
            'slideshow_photo_duration': self.slideshow_photo_duration,

        }

    def from_dict(self, config_dict):
        """Загружает конфигурацию из словаря"""
        from pathlib import Path
        for key, value in config_dict.items():
            if hasattr(self, key):
                # Преобразуем строковые пути обратно в Path объекты
                if key.endswith('_dir') and isinstance(value, str):
                    value = Path(value)
                # Преобразуем списки обратно в кортежи для диапазонов
                elif '_range' in key and isinstance(value, list):
                    value = tuple(value)
                # Преобразуем список списков обратно в кортеж кортежей для цветов
                elif key == 'line_color_range' and isinstance(value, list):
                    value = tuple(tuple(x) for x in value)
                setattr(self, key, value)

        # Если имя пресета присутствует в словаре, устанавливаем его
        if 'preset_name' in config_dict:
            self.preset_name = config_dict['preset_name']
        else:
            self.preset_name = "Импортированный пресет"

        if 'output_format' in config_dict:
            self.output_format = config_dict['output_format']

def generate_unique_image_name(output_dir, num_digits=5):

    millis = int(time.time() * 1000) % 1000

    random.seed(millis)

    # Пробуем до 100 раз, чтобы найти уникальное имя
    for attempt in range(100):
        # Генерируем случайное число с нужным количеством цифр
        random_number = random.randint(0, 10**num_digits - 1)
        img_name = f"IMG_{random_number:0{num_digits}d}"

        # Проверяем оба возможных расширения
        if not os.path.exists(os.path.join(output_dir, f"{img_name}.jpg")) and \
            not os.path.exists(os.path.join(output_dir, f"{img_name}.png")):
            return img_name

        # Если имя уже существует, меняем seed для следующей попытки
        random.seed(millis + attempt)

    timestamp = int(time.time() * 1000)
    return f"IMG_{timestamp % (10**num_digits):0{num_digits}d}"

def add_random_lines(image, config):
    """Добавляет случайные линии на изображение с толщиной в процентах от размера"""
    if not config.lines_enabled:
        return image

    result = image.copy()
    width, height = result.size
    min_dimension = min(width, height)

    line_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(line_layer)

    num_lines = random.randint(*config.lines_count_range)

    for _ in range(num_lines):
        x1 = random.randint(0, width)
        y1 = random.randint(0, height)
        x2 = random.randint(0, width)
        y2 = random.randint(0, height)

        # Толщина линии в процентах от меньшей стороны
        thickness_percent = random.uniform(*config.line_thickness_range)
        thickness = int(min_dimension * thickness_percent / 100)
        thickness = max(1, thickness)  # Минимальная толщина 1 пиксель

        color = (
            random.randint(*config.line_color_range[0]),
            random.randint(*config.line_color_range[1]),
            random.randint(*config.line_color_range[2]),
            random.randint(*config.line_opacity_range)
        )

        draw.line([(x1, y1), (x2, y2)], fill=color, width=thickness)

    result = Image.alpha_composite(result, line_layer)

    return result

def apply_rgb_shift(image, config):
    """Применяет RGB shift эффект с смещением в процентах от ширины"""
    if not config.rgb_shift_enabled:
        return image

    result = image.copy()
    width, height = result.size

    r, g, b, a = result.split()

    # Конвертируем проценты в пиксели
    max_shift_pixels = int(width * max(abs(config.rgb_shift_range[0]), abs(config.rgb_shift_range[1])) / 100)
    r_shift = random.randint(-max_shift_pixels, max_shift_pixels)
    g_shift = random.randint(-max_shift_pixels, max_shift_pixels)
    b_shift = random.randint(-max_shift_pixels, max_shift_pixels)

    r = ImageChops.offset(r, r_shift, 0)
    g = ImageChops.offset(g, g_shift, 0)
    b = ImageChops.offset(b, b_shift, 0)

    shifted = Image.merge('RGBA', (r, g, b, a))
    opacity = random.randint(*config.rgb_shift_opacity) / 100
    result = Image.blend(image, shifted, opacity)

    return result

def add_lens_flare(image, config):
    """Добавляет эффект бликов на изображение"""
    if not config.flare_enabled:
        return image

    width, height = image.size
    min_dimension = min(width, height)
    flare_count = random.randint(*config.flare_count_range)
    result = image.copy()

    for _ in range(flare_count):
        # Создаем блик
        flare_size_percent = random.randint(*config.flare_size_range)
        flare_size = int(min_dimension * flare_size_percent / 100)
        flare = Image.new('RGBA', (flare_size, flare_size))
        draw = ImageDraw.Draw(flare)

        # Градиент для блика
        for i in range(flare_size):
            for j in range(flare_size):
                center_x = flare_size / 2
                center_y = flare_size / 2
                distance = math.sqrt((i - center_x) ** 2 + (j - center_y) ** 2)
                intensity = max(0, 1 - (distance / (flare_size / 2)))
                opacity = int(random.randint(*config.flare_opacity_range) * intensity)
                draw.point((i, j), fill=(255, 255, 255, opacity))

        # Размещаем блик
        x = random.randint(0, width - flare_size)
        y = random.randint(0, height - flare_size)
        result.paste(flare, (x, y), flare)

    return result

def add_noise_to_image(image, config):
    """Добавляет обычный случайный шум к изображению"""
    if not config.noise_enabled:
        return image

    # Конвертируем изображение в numpy массив
    img_array = np.array(image)
    height, width, channels = img_array.shape

    # Генерируем равномерный случайный шум
    # Умножаем noise_level на коэффициент 10 для более заметного эффекта
    noise_level = random.uniform(*config.noise_level_range) * 10

    if config.colored_noise:
        # Цветной шум (разный для каждого канала RGB)
        noise = np.random.uniform(-noise_level, noise_level, (height, width, channels))
    else:
        # Монохромный шум (одинаковый для всех каналов)
        noise = np.random.uniform(-noise_level, noise_level, (height, width, 1))
        noise = np.repeat(noise, channels, axis=2)

    # Добавляем шум к изображению
    noisy_img_array = np.clip(img_array + noise, 0, 255).astype(np.uint8)
    return Image.fromarray(noisy_img_array)

def generate_julia_set(width, height, c_real=-0.4, c_imag=0.6, max_iter=100):
    """Генерирует фрактал множества Жюлиа"""
    x = np.linspace(-1.5, 1.5, width)
    y = np.linspace(-1.5, 1.5, height)
    X, Y = np.meshgrid(x, y)
    Z = X + Y*1j
    C = complex(c_real, c_imag)

    # Матрица для хранения результатов
    fractal = np.zeros((height, width))

    # Итеративное вычисление
    for i in range(max_iter):
        mask = np.abs(Z) <= 2
        fractal[mask] += 1
        Z[mask] = Z[mask]**2 + C

    # Нормализация и конвертация в изображение
    fractal = (fractal/max_iter * 255).astype(np.uint8)
    return Image.fromarray(fractal)

def add_fractal_pattern(image, config):
    """Добавляет фрактальный узор на изображение"""
    if not config.fractal_enabled:
        return image

    width, height = image.size

    # Генерируем фрактал
    fractal = generate_julia_set(width, height)

    # Создаем цветной фрактал
    colored_fractal = Image.new('RGBA', (width, height))
    for x in range(width):
        for y in range(height):
            value = fractal.getpixel((x, y))
            colored_fractal.putpixel((x, y),
                (value, value, value,
                 random.randint(*config.fractal_opacity_range)))

    # Накладываем на изображение
    return Image.alpha_composite(image, colored_fractal)

def add_particles(image, config):
    """Добавляет чередующиеся черные и белые частицы на изображение"""
    if not config.particles_enabled:
        return image

    result = image.copy()
    width, height = result.size
    draw = ImageDraw.Draw(result)

    num_particles = random.randint(*config.particles_count_range)

    for i in range(num_particles):
        # Чередуем цвета частиц
        color = (255, 255, 255) if i % 2 == 0 else (0, 0, 0)

        # Случайное положение
        x = random.randint(0, width)
        y = random.randint(0, height)

        # Случайный размер
        size = random.randint(*config.particle_size_range)

        # Случайная прозрачность
        opacity = random.randint(*config.particle_opacity_range)

        # Создаем частицу с прозрачностью
        particle_color = color + (opacity,)

        # Рисуем круглую частицу
        draw.ellipse([x, y, x + size, y + size], fill=particle_color)

    return result

def add_gaussian_noise(image, config):
    """Добавляет Гауссовский шум к изображению"""
    if not config.gaussian_noise_enabled:
        return image

    # Конвертируем изображение в numpy массив
    img_array = np.array(image)

    # Генерируем Гауссовский шум
    noise_level = random.uniform(*config.gaussian_noise_level_range)
    height, width, channels = img_array.shape

    if config.gaussian_colored_noise:
        # Цветной шум (разный для каждого канала RGB)
        noise = np.random.normal(0, noise_level, (height, width, channels))
    else:
        # Монохромный шум (одинаковый для всех каналов)
        noise = np.random.normal(0, noise_level, (height, width, 1))
        noise = np.repeat(noise, channels, axis=2)

    # Добавляем шум к изображению и ограничиваем значения в диапазоне 0-255
    noisy_img_array = np.clip(img_array + noise, 0, 255).astype(np.uint8)

    # Конвертируем обратно в PIL Image
    noisy_image = Image.fromarray(noisy_img_array)

    return noisy_image

def round_image_corners(image, config):
    """Скругляет углы изображения с радиусом в процентах от меньшей стороны"""
    if not config.corner_rounding_enabled or not config.background_enabled:
        return image

    width, height = image.size
    min_dimension = min(width, height)

    # Конвертируем процент в пиксели
    radius_percent = random.randint(*config.corner_radius_range)
    radius = int(min_dimension * radius_percent / 100)

    mask = Image.new("L", (width, height), 0)
    draw = ImageDraw.Draw(mask)
    draw.rounded_rectangle([(0, 0), (width, height)], radius=radius, fill=255)

    rounded_image = Image.new("RGBA", (width, height))
    rounded_image.paste(image, (0, 0), mask)

    return rounded_image

def create_color_mosaic(image, config):
    """Создает эффект цветной мозаики на изображении с плотным расположением фигур"""
    if not config.mosaic_enabled:
        return image

    width, height = image.size
    result = image.copy()
    mosaic_layer = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(mosaic_layer)

    # Базовый размер элемента мозаики
    tile_size = random.randint(*config.mosaic_size_range)

    # Выбираем тип формы для мозаики
    shape_type = config.mosaic_shape_type

    # Если выбран тип "случайный", выбираем случайный тип из доступных
    if shape_type == 'random':
        shape_type = random.choice(['square', 'hexagon', 'triangle', 'polygon'])

    if shape_type == 'hexagon':
        # Для шестиугольников используем сотовую структуру
        create_honeycomb_pattern(draw, width, height, tile_size, config)
    elif shape_type == 'triangle':
        # Для треугольников используем плотную треугольную сетку
        create_triangle_pattern(draw, width, height, tile_size, config)
    elif shape_type == 'polygon':
        # Для многоугольников используем модифицированную сетку
        create_polygon_pattern(draw, width, height, tile_size, config)
    else:
        # Оригинальная квадратная сетка
        create_square_pattern(draw, width, height, tile_size, config)

    result = Image.alpha_composite(result, mosaic_layer)
    return result

def create_honeycomb_pattern(draw, width, height, tile_size, config):
    """Создает плотную сотовую структуру из шестиугольников"""
    # Вычисляем параметры сетки шестиугольников
    a = tile_size  # длина стороны шестиугольника
    h = a * math.sqrt(3) / 2  # высота от центра до середины стороны

    # Вычисляем размеры шестиугольника
    hex_width = 2 * a  # ширина шестиугольника
    hex_height = 2 * h  # высота шестиугольника

    # Вычисляем смещения для плотной укладки
    x_offset = 3 * a / 2  # горизонтальное смещение между центрами
    y_offset = h  # вертикальное смещение между центрами

    # Расширяем область отрисовки для покрытия краев
    start_x = -hex_width
    start_y = -hex_height
    end_x = width + hex_width
    end_y = height + hex_height

    # Создаем сетку шестиугольников
    row = 0
    y = start_y
    while y < end_y:
        # Смещение для четных/нечетных рядов
        x_start = start_x + (a + a/2 if row % 2 else 0)
        x = x_start

        while x < end_x:
            # Генерируем цвет
            hue = random.random()
            saturation = random.randint(*config.mosaic_saturation_range) / 100
            brightness = random.randint(*config.mosaic_brightness_range) / 100
            rgb = colorsys.hsv_to_rgb(hue, saturation, brightness)
            color = tuple(int(c * 255) for c in rgb)
            opacity = random.randint(*config.mosaic_opacity_range)
            color = color + (opacity,)

            # Создаем точки для шестиугольника
            points = []
            for i in range(6):
                angle = math.pi / 3 * i
                px = x + a * math.cos(angle)
                py = y + a * math.sin(angle)
                points.extend([px, py])

            draw.polygon(points, fill=color)

            x += x_offset * 2

        y += y_offset
        row += 1

def create_triangle_pattern(draw, width, height, tile_size, config):
    """Создает плотную треугольную сетку"""
    # Вычисляем высоту равностороннего треугольника
    h = tile_size * math.sqrt(3) / 2

    # Расширяем область отрисовки для покрытия краев
    start_x = -tile_size
    start_y = -h
    end_x = width + tile_size
    end_y = height + h

    row = 0
    y = start_y

    while y < end_y:
        x = start_x + (tile_size/2 if row % 2 else 0)

        while x < end_x:
            # Генерируем цвет
            hue = random.random()
            saturation = random.randint(*config.mosaic_saturation_range) / 100
            brightness = random.randint(*config.mosaic_brightness_range) / 100
            rgb = colorsys.hsv_to_rgb(hue, saturation, brightness)
            color = tuple(int(c * 255) for c in rgb)
            opacity = random.randint(*config.mosaic_opacity_range)
            color = color + (opacity,)

            # Создаем два треугольника (вверх и вниз)
            # Треугольник вершиной вверх
            points_up = [
                x, y + h,  # нижняя левая точка
                x + tile_size, y + h,  # нижняя правая точка
                x + tile_size/2, y  # верхняя точка
            ]
            draw.polygon(points_up, fill=color)

            # Треугольник вершиной вниз (со следующим цветом)
            hue = (hue + 0.1) % 1.0  # слегка изменяем цвет
            rgb = colorsys.hsv_to_rgb(hue, saturation, brightness)
            color = tuple(int(c * 255) for c in rgb) + (opacity,)

            points_down = [
                x, y + h,  # верхняя левая точка
                x + tile_size, y + h,  # верхняя правая точка
                x + tile_size/2, y + 2*h  # нижняя точка
            ]
            draw.polygon(points_down, fill=color)

            x += tile_size

        y += h
        row += 1

def create_square_pattern(draw, width, height, tile_size, config):
    """Создает классическую квадратную мозаику"""
    for y in range(0, height, tile_size):
        for x in range(0, width, tile_size):
            # Генерируем цвет для квадрата
            hue = random.random()
            saturation = random.randint(*config.mosaic_saturation_range) / 100
            brightness = random.randint(*config.mosaic_brightness_range) / 100
            rgb = colorsys.hsv_to_rgb(hue, saturation, brightness)
            color = tuple(int(c * 255) for c in rgb)
            opacity = random.randint(*config.mosaic_opacity_range)
            color = color + (opacity,)

            # Рисуем квадрат
            draw.rectangle([x, y, x + tile_size - 1, y + tile_size - 1], fill=color)

            # Добавляем случайный блик
            if random.random() < 0.1:
                highlight_size = tile_size // 4
                highlight_pos = (
                    x + random.randint(0, tile_size - highlight_size),
                    y + random.randint(0, tile_size - highlight_size)
                )
                draw.ellipse(
                    [highlight_pos[0], highlight_pos[1],
                     highlight_pos[0] + highlight_size, highlight_pos[1] + highlight_size],
                    fill=(255, 255, 255, opacity // 2)
                )

def create_polygon_pattern(draw, width, height, tile_size, config):
    """Создает плотную сетку из случайных многоугольников"""
    # Используем сетку Вороного для создания случайной но плотной структуры
    cells_x = width // tile_size + 2
    cells_y = height // tile_size + 2

    # Создаем точки для сетки Вороного с небольшим случайным смещением
    points = []
    for y in range(-1, cells_y + 1):
        for x in range(-1, cells_x + 1):
            base_x = x * tile_size
            base_y = y * tile_size
            # Меньшее случайное смещение для более плотной укладки
            jitter = tile_size // 4
            point_x = base_x + random.randint(-jitter, jitter)
            point_y = base_y + random.randint(-jitter, jitter)
            points.append((point_x, point_y))

    # Создаем многоугольники вокруг каждой точки
    for center_x, center_y in points:
        if -tile_size <= center_x <= width + tile_size and -tile_size <= center_y <= height + tile_size:
            vertices = []
            num_vertices = random.randint(5, 7)

            for i in range(num_vertices):
                angle = (i * 2 * math.pi / num_vertices) + random.uniform(-0.1, 0.1)
                radius = tile_size * random.uniform(0.9, 1.1)
                vertex_x = center_x + radius * math.cos(angle)
                vertex_y = center_y + radius * math.sin(angle)
                vertices.extend([vertex_x, vertex_y])

            # Генерируем цвет
            hue = random.random()
            saturation = random.randint(*config.mosaic_saturation_range) / 100
            brightness = random.randint(*config.mosaic_brightness_range) / 100
            rgb = colorsys.hsv_to_rgb(hue, saturation, brightness)
            color = tuple(int(c * 255) for c in rgb)
            opacity = random.randint(*config.mosaic_opacity_range)
            color = color + (opacity,)

            draw.polygon(vertices, fill=color)

def create_hexagon(x, y, size):
    """Создает точки для правильного шестиугольника"""
    points = []
    for i in range(6):
        angle_deg = 60 * i - 30
        angle_rad = math.pi / 180 * angle_deg
        point_x = x + size * math.cos(angle_rad)
        point_y = y + size * math.sin(angle_rad)
        points.extend([point_x, point_y])
    return points

def rotate_image(image, config):
    if not config.rotation_enabled:
        return image

    rotation_angle = random.randint(*config.rotation_angle_range)
    return image.rotate(rotation_angle, expand=True)

def add_fourier_noise(image, config):
    """Добавляет шум в частотной области используя преобразование Фурье с разными паттернами"""
    if not config.fourier_noise_enabled:
        return image

    img_array = np.array(image)
    channels = []

    def create_pattern_mask(rows, cols, pattern_type):
        y = np.linspace(-0.5, 0.5, rows)
        x = np.linspace(-0.5, 0.5, cols)
        X, Y = np.meshgrid(x, y)
        R = np.sqrt(X**2 + Y**2)

        if pattern_type == 'rings':
            # Кольцевой паттерн
            frequency = random.uniform(*config.fourier_frequency_range)
            return np.sin(2 * np.pi * frequency * R)**2

        elif pattern_type == 'sectors':
            # Секторный паттерн
            angles = np.arctan2(Y, X)
            num_sectors = int(random.uniform(4, 12))
            return np.abs(np.sin(num_sectors * angles))**0.5

        elif pattern_type == 'directional':
            # Направленный паттерн
            angle = random.uniform(0, 2*np.pi)
            X_rot = X * np.cos(angle) + Y * np.sin(angle)
            return np.exp(-(X_rot**2) / (2 * (0.05)**2))

        elif pattern_type == 'fractal':
            # Улучшенный фрактальный паттерн
            beta = random.uniform(1.5, 3.0)  # Случайная фрактальная размерность
            # Создаем базовый фрактальный шум
            base_mask = np.where(R > 0, R**(-beta/2), 1)

            # Добавляем турбулентность
            angles = np.arctan2(Y, X)
            turbulence = np.sin(angles * random.randint(3, 8))

            # Комбинируем базовую маску с турбулентностью
            mask = base_mask * (1 + 0.3 * turbulence)

            # Нормализация
            mask = (mask - mask.min()) / (mask.max() - mask.min())
            return mask

        else:  # 'random'
            # Случайный паттерн (оригинальная реализация)
            return np.exp(-((R * random.uniform(*config.fourier_frequency_range)) ** 2))

    for channel in range(3):  # RGB каналы
        img_channel = img_array[:,:,channel].astype(float)
        f_transform = fftshift(fft2(img_channel))

        rows, cols = img_channel.shape
        noise_amplitude = random.uniform(*config.fourier_noise_amplitude_range)

        # Создаем маску в соответствии с выбранным паттерном
        pattern_mask = create_pattern_mask(rows, cols, config.fourier_pattern_type)

        # Применяем шум с выбранным паттерном
        noise = pattern_mask * np.exp(1j * 2 * np.pi * np.random.random((rows, cols)))
        f_transform_noisy = f_transform * (1 + noise * noise_amplitude)

        img_noisy = np.real(ifft2(ifftshift(f_transform_noisy)))
        img_noisy = np.clip(img_noisy, 0, 255).astype(np.uint8)
        channels.append(img_noisy)

    noisy_image = np.stack(channels, axis=2)

    if img_array.shape[2] == 4:  # Сохраняем альфа-канал
        noisy_image = np.dstack((noisy_image, img_array[:,:,3]))

    return Image.fromarray(noisy_image)

def shuffle_pixels(image, config):
    """Применяет эффект перестановки пикселей к изображению с рандомной вероятностью из диапазона

    Args:
        image (PIL.Image): Исходное изображение
        config (UniqualizerConfig): Конфигурация

    Returns:
        PIL.Image: Изображение с переставленными пикселями
    """
    if not config.pixel_shuffle_enabled:
        return image

    # Конвертируем в numpy массив для более эффективной работы
    img_array = np.array(image)
    height, width = img_array.shape[:2]

    # Определяем размер блока пикселей для перестановки
    block_size = random.randint(*config.pixel_shuffle_block_range)

    # Выбираем случайную вероятность из заданного диапазона
    random_probability = random.uniform(*config.pixel_shuffle_probability_range)

    # Создаем сетку блоков
    for y in range(0, height - block_size + 1, block_size):
        for x in range(0, width - block_size + 1, block_size):
            # С определенной вероятностью переставляем блок
            if random.random() < random_probability:
                # Выбираем случайную позицию для обмена
                target_y = random.randint(0, height - block_size)
                target_x = random.randint(0, width - block_size)

                # Сохраняем текущий блок
                temp_block = img_array[y:y+block_size, x:x+block_size].copy()

                # Копируем целевой блок на текущую позицию
                img_array[y:y+block_size, x:x+block_size] = \
                    img_array[target_y:target_y+block_size, target_x:target_x+block_size]

                # Помещаем сохраненный блок на целевую позицию
                img_array[target_y:target_y+block_size, target_x:target_x+block_size] = temp_block

    return Image.fromarray(img_array)

def halton_sequence(n, base):
    """Генерирует n-ое число в последовательности Халтона для заданной базы"""
    h = 0
    f = 1 / base
    i = n
    while i > 0:
        h += f * (i % base)
        i = i // base
        f = f / base
    return h

def add_halton_noise(image, config):
    """Добавляет шум Халтона на изображение"""
    if not config.halton_noise_enabled:
        return image

    try:
        result = image.copy()
        width, height = result.size
        draw = ImageDraw.Draw(result)

        # Проверяем корректность диапазонов
        if config.halton_noise_points[0] >= config.halton_noise_points[1]:
            raise ValueError("Некорректный диапазон количества точек")
        if config.halton_noise_size[0] >= config.halton_noise_size[1]:
            raise ValueError("Некорректный диапазон размера точек")
        if config.halton_noise_opacity[0] >= config.halton_noise_opacity[1]:
            raise ValueError("Некорректный диапазон прозрачности")

        # Случайное начало последовательности
        offset = random.randint(0, 1000)

        # Выбираем случайные простые числа для базы
        base_options = [2, 3, 5, 7, 11, 13]
        base_x = random.choice(base_options)
        base_y = random.choice([b for b in base_options if b != base_x])

        # Количество точек и их размер
        num_points = random.randint(*config.halton_noise_points)
        point_size = max(1, random.randint(*config.halton_noise_size))

        for i in range(num_points):
            # Добавляем случайное смещение к координатам
            x = int((halton_sequence(i + offset, base_x) + random.uniform(-0.1, 0.1)) * width)
            y = int((halton_sequence(i + offset, base_y) + random.uniform(-0.1, 0.1)) * height)

            # Ограничиваем координаты границами изображения
            x = max(0, min(x, width - point_size))
            y = max(0, min(y, height - point_size))

            # Определяем цвет точки в зависимости от режима
            if config.halton_noise_color_mode == 'monochrome':
                # Для монохрома используем градацию серого в зависимости от позиции
                gray = int((x + y) / (width + height) * 255)
                color = (gray, gray, gray)
            elif config.halton_noise_color_mode == 'gradient':
                # Для градиента используем полноценный цветовой спектр
                angle = math.atan2(y - height/2, x - width/2)  # Угол относительно центра
                distance = math.sqrt((x - width/2)**2 + (y - height/2)**2)  # Расстояние от центра

                # Нормализуем значения
                hue = (angle + math.pi) / (2 * math.pi)  # 0-1 для цветового тона
                saturation = min(distance / (math.sqrt(width**2 + height**2) / 2), 1.0)  # 0-1 для насыщенности

                rgb = colorsys.hsv_to_rgb(hue, saturation, 0.9)
                color = tuple(int(c * 255) for c in rgb)
            else:  # 'random'
                color = (
                    random.randint(0, 255),
                    random.randint(0, 255),
                    random.randint(0, 255)
                )

            # Добавляем прозрачность с проверкой диапазона
            opacity = max(0, min(255, random.randint(*config.halton_noise_opacity)))
            color = color + (opacity,)

            # Рисуем точку с проверкой границ
            x_end = min(width, x + point_size)
            y_end = min(height, y + point_size)

            # Случайно выбираем между кругом и ромбом для разнообразия
            if random.random() < 0.5:
                draw.ellipse([x, y, x_end, y_end], fill=color)
            else:
                # Создаем ромб
                diamond_points = [
                    (x + point_size//2, y),  # верх
                    (x_end, y + point_size//2),  # право
                    (x + point_size//2, y_end),  # низ
                    (x, y + point_size//2)  # лево
                ]
                draw.polygon(diamond_points, fill=color)

        return result

    except Exception as e:
        print(f"Ошибка в add_halton_noise: {str(e)}")
        return image

def apply_bit_plane_manipulation(image, config):
    if not config.bit_plane_enabled:
        return image

    # Конвертируем в numpy массив
    img_array = np.array(image.convert('RGB'), dtype=np.uint8)

    # Выбираем случайное количество нетронутых битов из заданного диапазона
    intensity = random.randint(*config.bit_plane_intensity_range)

    # Создаем маски
    preserve_mask = (255 << (8 - intensity)) & 255  # Сохраняем старшие биты
    change_mask = ~preserve_mask & 255  # Изменяем младшие биты

    # Копируем оригинальный массив
    modified = img_array.copy()

    if config.bit_manipulation_mode == 'invert':
        # Инверсия младших битов
        modified = (modified & preserve_mask) | (~modified & change_mask)
    elif config.bit_manipulation_mode == 'noise':
        # Случайный шум в младших битах
        noise = np.random.randint(0, 256, size=img_array.shape, dtype=np.uint8)
        modified = (modified & preserve_mask) | (noise & change_mask)
    else:  # 'random'
        # Генерируем случайные значения для младших битов сразу для всех позиций
        random_bits = np.random.randint(0, 1 << (8 - intensity),
                                      size=img_array.shape, dtype=np.uint8)
        modified = (modified & preserve_mask) | (random_bits & change_mask)

    # Конвертируем обратно в изображение
    modified_image = Image.fromarray(modified, 'RGB')

    # Восстанавливаем альфа-канал, если есть
    if image.mode == 'RGBA':
        modified_image.putalpha(image.getchannel('A'))

    return modified_image

def add_frame_to_image(image, config):
    """
    Добавляет рамку вокруг изображения с настраиваемой шириной, цветом и прозрачностью.
    Поддерживает скругленные углы.
    """
    if not config.frame_enabled:
        return image

    try:
        # Получаем размеры изображения
        width, height = image.size

        # Убедимся, что изображение в режиме RGBA
        if image.mode != 'RGBA':
            image = image.convert('RGBA')

        # Получаем параметры рамки из конфигурации
        frame_width = random.randint(*config.frame_width_range)

        # Предустановленные цвета
        color_map = {
            'black': (0, 0, 0),
            'white': (255, 255, 255),
            'gray': (128, 128, 128),
            'silver': (192, 192, 192),
            'red': (255, 0, 0),
            'maroon': (128, 0, 0),
            'crimson': (220, 20, 60),
            'green': (0, 255, 0),
            'olive': (128, 128, 0),
            'lime': (50, 205, 50),
            'teal': (0, 128, 128),
            'blue': (0, 0, 255),
            'navy': (0, 0, 128),
            'indigo': (75, 0, 130),
            'purple': (128, 0, 128),
            'violet': (238, 130, 238),
            'yellow': (255, 255, 0),
            'gold': (255, 215, 0),
            'khaki': (240, 230, 140),
            'orange': (255, 165, 0),
            'brown': (165, 42, 42),
            'pink': (255, 105, 180),
            'magenta': (255, 0, 255),
            'cyan': (0, 255, 255),
            'turquoise': (64, 224, 208)
        }

        if config.frame_color == 'random':
            # Выбираем случайный цвет из предопределенных цветов
            color_key = random.choice(list(color_map.keys()))
            frame_rgb = color_map[color_key]
        else:
            # Берем предопределенный цвет по ключу
            frame_rgb = color_map.get(config.frame_color, (0, 0, 0))

        # Получаем прозрачность
        opacity = random.randint(*config.frame_opacity_range)
        alpha_value = int(opacity * 255 / 100)  # Преобразуем проценты в значение альфа-канала

        # Итоговый цвет с альфа-каналом
        frame_color = frame_rgb + (alpha_value,)

        # Определяем радиус скругления
        min_dimension = min(width, height)
        if config.corner_rounding_enabled:
            radius_percent = random.randint(*config.corner_radius_range)
            radius = int(min_dimension * radius_percent / 100)
        else:
            radius = 0

        # Создаем результирующее изображение
        result = image.copy()

        # Создаем маску для рамки с такими же скругленными углами
        mask_outer = Image.new("L", (width, height), 0)
        mask_inner = Image.new("L", (width, height), 0)

        draw_outer = ImageDraw.Draw(mask_outer)
        draw_inner = ImageDraw.Draw(mask_inner)

        # Рисуем внешний прямоугольник с скругленными углами
        draw_outer.rounded_rectangle([(0, 0), (width-1, height-1)], radius=radius, fill=255)

        # Рисуем внутренний прямоугольник с скругленными углами (меньше на толщину рамки)
        inner_rect = [(frame_width, frame_width),
                      (width-1-frame_width, height-1-frame_width)]
        draw_inner.rounded_rectangle(inner_rect, radius=max(0, radius-frame_width), fill=255)

        # Создаем маску рамки путем вычитания внутренней области из внешней
        frame_mask = ImageChops.subtract(mask_outer, mask_inner)

        # Создаем изображение рамки
        frame_img = Image.new("RGBA", (width, height), frame_color)

        # Применяем маску к рамке
        frame_img.putalpha(frame_mask)

        # Накладываем рамку на изображение
        result = Image.alpha_composite(result, frame_img)

        return result

    except Exception as e:
        print(f"Ошибка при добавлении рамки: {str(e)}")
        import traceback
        traceback.print_exc()
        return image

def load_overlay_images(config):
    """Загружает PNG изображения для наложения"""
    overlays = []
    try:
        for file_path in config.overlay_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() == '.png':
                try:
                    overlay = Image.open(file_path).convert("RGBA")
                    overlays.append(overlay)
                except Exception as e:
                    print(f"Warning: Could not load overlay image {file_path.name}: {str(e)}")
    except FileNotFoundError:
        print(f"Warning: Overlay directory not found at {config.overlay_dir}")
    except Exception as e:
        print(f"Warning: Error loading overlay images: {str(e)}")

    return overlays

def add_overlay_to_image(image, overlays, config):
    """Добавляет PNG изображение с относительным размером и смещением по оси Y"""
    if not config.overlay_enabled or not overlays:
        return image

    width, height = image.size

    # Выбираем случайное изображение для наложения
    overlay = random.choice(overlays)

    # Получаем оригинальные размеры наложения
    orig_width, orig_height = overlay.size

    # Выбираем случайное значение изменения размера в процентах от оригинала
    size_percent_change = random.randint(*config.overlay_size_range)

    scale_factor = 1.0 + (size_percent_change / 100.0)

    # Вычисляем новые размеры с сохранением пропорций
    new_width = int(orig_width * scale_factor)
    new_height = int(orig_height * scale_factor)

    # Дополнительная проверка, чтобы размер не был слишком маленьким
    min_size = 10  # минимум 10 пикселей
    new_width = max(min_size, new_width)
    new_height = max(min_size, new_height)

    max_width = width * 2  # позволяем быть до 2 раз больше фона
    max_height = height * 2
    new_width = min(max_width, new_width)
    new_height = min(max_height, new_height)

    overlay_resized = overlay.resize((new_width, new_height), Image.LANCZOS)

    x = (width - new_width) // 2

    y_offset_percent = random.randint(*config.overlay_y_offset_range)
    y_offset_pixels = int(height * y_offset_percent / 100)
    y = (height - new_height) // 2 + y_offset_pixels

    # Накладываем изображение на фон
    result = image.copy()
    result.paste(overlay_resized, (x, y), overlay_resized)

    return result

def load_backgrounds(config):
    """Оптимизированная версия загрузки фонов"""
    from pathlib import Path
    background_dir = config.base_dir / "backgrounds"

    try:
        if not background_dir.exists():
            raise NoBackgroundsError(f"Директория с фонами не найдена: {background_dir}")

        # Получаем только список путей к файлам
        files = [f.name for f in background_dir.iterdir()
                if f.is_file() and f.suffix.lower() in ('.jpg', '.png', '.jpeg')]

        if not files:
            raise NoBackgroundsError("В директории backgrounds нет изображений")

        # Выбираем случайный файл и загружаем только его
        random_file = random.choice(files)
        background_path = background_dir / random_file

        try:
            background = Image.open(background_path).convert("RGBA")
            return [background], [str(background_path)]
        except Exception as e:
            print(f"Warning: Could not load background {random_file}: {str(e)}")
            return load_backgrounds(config)  # Рекурсивно пробуем другой файл

    except Exception as e:
        raise NoBackgroundsError(f"Ошибка при загрузке фонов: {str(e)}")

def add_background_to_image(image, backgrounds_info, config):
    if not config.background_enabled:
        return image, None

    backgrounds, background_paths = backgrounds_info
    if not backgrounds:
        return image, None

    # Случайно выбираем индекс фона
    bg_index = random.randrange(len(backgrounds))
    background = backgrounds[bg_index]
    bg_path = background_paths[bg_index]

    width, height = image.size
    bg_increase = random.randint(*config.background_size_range) / 100.0
    bg_width = int(width * (1 + bg_increase))
    bg_height = int(height * (1 + bg_increase))

    if background.size[0] < bg_width or background.size[1] < bg_height:
        background = background.resize((bg_width, bg_height), Image.LANCZOS)

    blur_amount = random.randint(*config.blur_range)
    background = background.filter(ImageFilter.GaussianBlur(blur_amount))

    opacity = config.background_opacity_range[0] / 100.0
    alpha = Image.new("L", background.size, int(opacity * 255))
    background.putalpha(alpha)

    return background, bg_path

def delete_used_background(bg_path):
    """Удаляет использованный фон"""
    if bg_path and os.path.exists(bg_path):
        try:
            os.remove(bg_path)
            print(f"Background deleted: {bg_path}")
        except Exception as e:
            print(f"Warning: Could not delete background {bg_path}: {str(e)}")

def apply_image_shift(image, background, config):
    if not config.shift_enabled:
        return image

    width, height = image.size
    bg_width, bg_height = background.size

    # Проверяем, помещается ли изображение в фон
    if width > bg_width or height > bg_height:
        # Если изображение больше фона, уменьшаем его до размеров фона
        ratio = min(bg_width / width, bg_height / height)
        width = int(width * ratio * 0.95)  # Оставляем небольшой запас по краям
        height = int(height * ratio * 0.95)
        image = image.resize((width, height), Image.LANCZOS)

    # Конвертируем проценты в пиксели
    vertical_shift_pixels = int(height * random.randint(*config.vertical_shift_range) / 100)
    horizontal_shift_pixels = int(width * random.randint(*config.horizontal_shift_range) / 100)

    x_offset = (bg_width - width) // 2 + horizontal_shift_pixels
    y_offset = (bg_height - height) // 2 + vertical_shift_pixels

    # Гарантируем, что изображение не выйдет за границы фона
    x_offset = max(0, min(x_offset, bg_width - width))
    y_offset = max(0, min(y_offset, bg_height - height))

    final_image = background.copy()
    final_image.paste(image, (x_offset, y_offset), image)

    return final_image

def load_emojis(config):
    emojis = []
    try:
        for file_path in config.emoji_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() == '.png':
                try:
                    emoji = Image.open(file_path).convert("RGBA")
                    emojis.append(emoji)
                except Exception as e:
                    print(f"Warning: Could not load emoji {file_path.name}: {str(e)}")
    except FileNotFoundError:
        print(f"Warning: Emoji directory not found at {config.emoji_dir}")
    except Exception as e:
        print(f"Warning: Error loading emojis: {str(e)}")

    return emojis

def add_emojis_to_image(image, emojis, config):
    """Добавляет эмодзи с размером в процентах от размера изображения"""
    if not config.emoji_enabled or not emojis:
        return image

    width, height = image.size
    min_dimension = min(width, height)
    emoji_count = random.randint(*config.emoji_count_range)

    for _ in range(emoji_count):
        emoji = random.choice(emojis)

        # Размер эмодзи в процентах от меньшей стороны изображения
        emoji_size_percent = random.randint(*config.emoji_size_range)
        emoji_size = int(min_dimension * emoji_size_percent / 100)
        emoji = emoji.resize((emoji_size, emoji_size), Image.LANCZOS)

        x = random.randint(0, width - emoji_size)
        y = random.randint(0, height - emoji_size)

        image.paste(emoji, (x, y), emoji)

    return image

def create_slideshow_from_photos(config, output_folder_num):

    try:
        # Проверяем что номер папки корректный
        if output_folder_num not in [1, 2, 3]:
            print(f"Ошибка: Некорректный номер папки output_{output_folder_num}")
            return None

        # Определяем папку с фотографиями
        output_folder = os.path.join(config.base_dir, f"output_{output_folder_num}")
        if not os.path.exists(output_folder):
            print(f"Ошибка: Папка {output_folder} не существует")
            return None

        # Получаем список фотографий
        photos = [f for f in os.listdir(output_folder)
                    if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

        if not photos:
            print(f"В папке {output_folder} нет фотографий")
            return None

        # Создаем имя выходного файла
        # Исправленная строка - используем правильный импорт datetime
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        video_name = f"slideshow_{output_folder_num}_{timestamp}.mp4"
        video_path = os.path.join(config.video_dir, video_name)

        print(f"Начинаю создание видео из {len(photos)} фотографий...")

        # Открываем первое изображение, чтобы получить размер
        first_img_path = os.path.join(output_folder, photos[0])
        first_img = cv2.imread(first_img_path)
        if first_img is None:
            print(f"Ошибка при чтении изображения: {first_img_path}")
            return None

        height, width, _ = first_img.shape

        # Создаем видеозапись
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # кодек для mp4
        # Используем fps из конфигурации (количество кадров, которое нужно показать для одной фотографии)
        fps = 1  # базовый fps
        photo_duration = getattr(config, 'slideshow_photo_duration', 1)  # получаем длительность из конфигурации
        video_writer = cv2.VideoWriter(video_path, fourcc, fps, (width, height))

        # Список для хранения путей использованных фотографий (для удаления)
        used_photos = []

        # Добавляем каждое фото в видео
        for photo in photos:
            img_path = os.path.join(output_folder, photo)
            img = cv2.imread(img_path)

            if img is None:
                print(f"Пропускаю поврежденное изображение: {img_path}")
                continue

            # Проверяем размер и при необходимости изменяем
            if img.shape[0] != height or img.shape[1] != width:
                img = cv2.resize(img, (width, height))

            # Добавляем изображение в видео несколько раз, в зависимости от желаемой длительности показа
            for _ in range(photo_duration):
                video_writer.write(img)

            used_photos.append(img_path)

        # Закрываем видеозапись
        video_writer.release()

        # Удаляем использованные фотографии
        for photo_path in used_photos:
            try:
                os.remove(photo_path)
                print(f"Удален файл: {photo_path}")
            except Exception as e:
                print(f"Не удалось удалить файл {photo_path}: {str(e)}")

        print(f"Видео создано успешно: {video_path}")
        return video_path

    except Exception as e:
        print(f"Ошибка при создании видео: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def adjust_image_opacity(image, config):
    if not config.opacity_enabled:
        return image

    opacity = config.opacity_range[0] / 100.0
    width, height = image.size

    alpha = Image.new("L", (width, height), int(opacity * 255))
    image_with_alpha = Image.new("RGBA", (width, height), (0, 0, 0, 0))
    image_with_alpha.paste(image, (0, 0), alpha)

    return image_with_alpha

def process_image(input_image_path, output_image_path, config):

    try:
        # Проверяем режим overlay_only_mode и наличие входного файла
        is_overlay_only_mode = getattr(config, 'overlay_only_mode', False)
        bg_path = None

        # Если включен фон и режим overlay_only_mode, сначала загружаем фон
        if is_overlay_only_mode and config.background_enabled:
            try:
                print("Используется режим только с фоном и PNG (без входного изображения)")
                backgrounds_info = load_backgrounds(config)
                if backgrounds_info:
                    background, bg_path = backgrounds_info[0][0], backgrounds_info[1][0]

                    # Применяем настройки фона
                    # Выбираем размеры согласно выбранному формату
                    if config.image_aspect_ratio == "1:1":
                        width, height = (1080, 1080)  # Квадратный формат
                    else:  # "9:16" или по умолчанию
                        width, height = (1080, 1920)  # Вертикальный формат

                    bg_increase = random.randint(*config.background_size_range) / 100.0
                    bg_width = int(width * (1 + bg_increase))
                    bg_height = int(height * (1 + bg_increase))

                    if background.size[0] < bg_width or background.size[1] < bg_height:
                        background = background.resize((bg_width, bg_height), Image.LANCZOS)

                    # Размытие и прозрачность
                    blur_amount = random.randint(*config.blur_range)
                    background = background.filter(ImageFilter.GaussianBlur(blur_amount))

                    opacity = config.background_opacity_range[0] / 100.0
                    alpha = Image.new("L", background.size, int(opacity * 255))
                    background.putalpha(alpha)

                    # Используем фон как основное изображение
                    image = background
                    # Отмечаем, что фон уже применен
                    backgrounds_info = None
                else:
                    raise NoBackgroundsError("Не удалось загрузить фон")
            except NoBackgroundsError as e:
                print(f"Ошибка загрузки фонов: {str(e)}")
                raise
        elif not is_overlay_only_mode:
            # Стандартный режим - требуется входной файл
            if input_image_path is None:
                raise FileNotFoundError("Входной файл не указан")

            if not os.path.exists(input_image_path):
                raise FileNotFoundError(f"Входной файл не найден: {input_image_path}")

            # Открываем и проверяем входное изображение
            try:
                image = Image.open(input_image_path).convert("RGBA")
            except Exception as e:
                raise ValueError(f"Ошибка при открытии изображения: {str(e)}")

            # Если включен фон, проверяем наличие фонов
            backgrounds_info = None
            if config.background_enabled:
                try:
                    backgrounds_info = load_backgrounds(config)
                except NoBackgroundsError as e:
                    print(f"Ошибка загрузки фонов: {str(e)}")
                    raise
        else:
            # Обычный режим overlay_only_mode без фона - создаем пустое изображение
            print("Используется режим только с PNG (без фона)")
            # Выбираем размеры согласно выбранному формату
            if config.image_aspect_ratio == "1:1":
                image = Image.new("RGBA", (1080, 1080), (0, 0, 0, 0))
            else:  # "9:16" или по умолчанию
                image = Image.new("RGBA", (1080, 1920), (0, 0, 0, 0))
            backgrounds_info = None

        metadata_manager = MetadataManager(config)

        # Установка целевых размеров в зависимости от выбранного формата
        if config.image_aspect_ratio == "1:1":
            target_width = 1080
            target_height = 1080
        else:  # "9:16" или по умолчанию
            target_width = 1080
            target_height = 1920

        # Если мы не в режиме overlay_only_mode или фон не был применен, изменяем размер входного изображения
        if not is_overlay_only_mode and not (is_overlay_only_mode and config.background_enabled):
            # Изменение размера с сохранением пропорций
            original_width, original_height = image.size
            original_ratio = original_width / original_height
            target_ratio = target_width / target_height

            if original_ratio > target_ratio:
                new_width = target_width
                new_height = int(target_width / original_ratio)
            else:
                new_height = target_height
                new_width = int(target_height * original_ratio)

            image = image.resize((new_width, new_height), Image.LANCZOS)

            # Создаем холст целевого размера и размещаем изображение по центру
            final_image = Image.new("RGBA", (target_width, target_height), (0, 0, 0, 0))
            x_offset = (target_width - new_width) // 2
            y_offset = (target_height - new_height) // 2
            final_image.paste(image, (x_offset, y_offset))
            image = final_image

        try:
            # Манипуляции с битами
            if config.bit_plane_enabled:
                image = apply_bit_plane_manipulation(image, config)

            # шум Халтона
            if config.halton_noise_enabled:
                image = add_halton_noise(image, config)

            # Process image with effects
            if config.metadata_enabled and config.remove_original_metadata:
                image = metadata_manager.remove_metadata(image)

            if config.rgb_shift_enabled:
                image = apply_rgb_shift(image, config)

            if config.lines_enabled:
                image = add_random_lines(image, config)

            if config.emoji_enabled:
                emojis = load_emojis(config)
                image = add_emojis_to_image(image, emojis, config)

            if config.overlay_enabled:
                overlays = load_overlay_images(config)
                if not overlays and is_overlay_only_mode:
                    raise ValueError("В режиме 'Только с фоном и PNG' необходимы PNG файлы в папке overlay_images")
                image = add_overlay_to_image(image, overlays, config)

            if config.noise_enabled:
                image = add_noise_to_image(image, config)

            if config.fourier_noise_enabled:
                image = add_fourier_noise(image, config)

            if config.opacity_enabled:
                image = adjust_image_opacity(image, config)

            if config.fractal_enabled:
                image = add_fractal_pattern(image, config)

            if config.particles_enabled:
                image = add_particles(image, config)

            if config.gaussian_noise_enabled:
                image = add_gaussian_noise(image, config)

            if config.mosaic_enabled:
                image = create_color_mosaic(image, config)

            if config.pixel_shuffle_enabled:
                image = shuffle_pixels(image, config)

            if config.frame_enabled:
                image = add_frame_to_image(image, config)

            # Проверяем, что изображение всё ещё валидно после всех эффектов
            if image.size[0] <= 0 or image.size[1] <= 0:
                raise ValueError("Некорректный размер изображения после обработки эффектов")

            # Обработка фона и связанных эффектов (если фон еще не был применен ранее)
            if config.background_enabled and backgrounds_info:
                background, bg_path = add_background_to_image(image, backgrounds_info, config)

                if background:
                    # Обычный режим - скругляем углы, если нужно
                    if config.corner_rounding_enabled:
                        image = round_image_corners(image, config)

                    if config.rotation_enabled and config.background_enabled:
                        image = rotate_image(image, config)

                    # Наложение изображения на фон
                    if config.shift_enabled:
                        image = apply_image_shift(image, background, config)
                    else:
                        final_image = background.copy()
                        final_image.paste(
                            image,
                            ((background.size[0] - image.size[0]) // 2,
                             (background.size[1] - image.size[1]) // 2),
                            image
                        )
                        image = final_image

                    if config.flare_enabled:
                        image = add_lens_flare(image, config)

                    # Очистка фона после использования
                    if bg_path:
                        delete_used_background(bg_path)
                else:
                    raise NoBackgroundsError("Не удалось создать фон")

            # Проверяем финальное изображение
            if image.size[0] <= 0 or image.size[1] <= 0:
                raise ValueError("Некорректный размер финального изображения")

            # Сохранение результата
            os.makedirs(os.path.dirname(output_image_path), exist_ok=True)

            # Обновляем расширение файла в соответствии с выбранным форматом
            base_path = os.path.splitext(output_image_path)[0]
            if config.output_format.upper() == "PNG":
                final_output_path = base_path + ".png"
                image.save(final_output_path, "PNG")
            else:  # JPEG
                final_output_path = base_path + ".jpg"
                image = image.convert("RGB")
                image.save(final_output_path, "JPEG", quality=80, optimize=True)

            if config.metadata_enabled and config.add_custom_metadata:
                metadata_manager.add_metadata(final_output_path, image)

            # Удаляем фон, если он был загружен в начале и не был удален ранее
            if bg_path and os.path.exists(bg_path):
                delete_used_background(bg_path)

            return True

        except Exception as e:
            print(f"Ошибка при применении эффектов: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    except NoBackgroundsError:
        # Пробрасываем исключение дальше для правильной обработки
        raise
    except Exception as e:
        print(f"Критическая ошибка при обработке изображения: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Очистка ресурсов
        if 'image' in locals():
            image.close()
        if 'backgrounds_info' in locals() and backgrounds_info and backgrounds_info[0]:
            for bg in backgrounds_info[0]:
                bg.close()

def process_image_thread(input_image_path, config, thread_id):
    start_time = time.time()

    base_name = generate_unique_image_name(config.output_dir)

    extension = ".png" if config.output_format.upper() == "PNG" else ".jpg"
    output_image_path = os.path.join(
        config.output_dir,
        f"{base_name}{extension}"
    )

    result = process_image(input_image_path, output_image_path, config)

    if result:
        image_time = time.time() - start_time
        print(f"Image {thread_id} saved as {output_image_path} | Generation time: {image_time:.2f} seconds")
        return True
    else:
        print(f"Failed to process image {thread_id}")
        return False

def thread_worker(input_image_path, config, start_id, num_threads=4):
    threads = []

    for i in range(num_threads):
        thread = Thread(target=process_image_thread, args=(input_image_path, config, start_id + i))
        threads.append(thread)
        thread.start()

    for t in threads:
        t.join()

def process_worker(input_image_path, config, start_id, num_images_per_process):
    """Изменённая функция process_worker, которая генерирует точное количество изображений"""
    for i in range(num_images_per_process):
        process_image_thread(input_image_path, config, start_id + i)

def get_input_file():
    """Получаем первый файл из входной директории."""
    input_dir = "C:\\uniqualizer\\input_images"

    try:
        # Получаем список файлов из входной директории
        files = [f for f in os.listdir(input_dir)
                if os.path.isfile(os.path.join(input_dir, f))
                and f.lower().endswith(('.png', '.jpg', '.jpeg'))]

        if not files:
            print(f"Ошибка: Нет изображений в директории {input_dir}")
            print("Пожалуйста, добавьте изображения (PNG или JPEG) в эту папку.")
            return None

        # Берем первый файл
        input_file = os.path.join(input_dir, files[0])
        print(f"Найдено входное изображение: {input_file}")
        return input_file

    except Exception as e:
        print(f"Ошибка при поиске входного файла: {str(e)}")
        return None

class NoBackgroundsError(Exception):
    """Исключение, возникающее когда заканчиваются фоны"""
    pass

def load_backgrounds(config):
    """Оптимизированная версия загрузки фонов с проверкой наличия фонов до начала работы"""
    from pathlib import Path
    background_dir = config.base_dir / "backgrounds"

    if not background_dir.exists():
        raise NoBackgroundsError(f"Директория с фонами не найдена: {background_dir}")

    files = [f.name for f in background_dir.iterdir()
            if f.is_file() and f.suffix.lower() in ('.jpg', '.png', '.jpeg')]

    if not files:
        raise NoBackgroundsError("В директории backgrounds нет изображений")

    random_file = random.choice(files)
    background_path = os.path.join(background_dir, random_file)

    try:
        background = Image.open(background_path).convert("RGBA")
        return [background], [background_path]
    except Exception as e:
        print(f"Warning: Could not load background {random_file}: {str(e)}")
        raise NoBackgroundsError(f"Не удалось загрузить фон {random_file}: {str(e)}")

def delete_used_background(bg_path):
    """Удаляет использованный фон"""
    if bg_path and os.path.exists(bg_path):
        try:
            os.remove(bg_path)
            print(f"Background deleted: {bg_path}")
        except Exception as e:
            print(f"Warning: Could not delete background {bg_path}: {str(e)}")

def add_background_to_image(image, backgrounds_info, config):
    if not config.background_enabled:
        return image, None

    backgrounds, background_paths = backgrounds_info
    if not backgrounds:
        return image, None

    # Случайно выбираем индекс фона
    bg_index = random.randrange(len(backgrounds))
    background = backgrounds[bg_index]
    bg_path = background_paths[bg_index]

    width, height = image.size
    bg_increase = random.randint(*config.background_size_range) / 100.0
    bg_width = int(width * (1 + bg_increase))
    bg_height = int(height * (1 + bg_increase))

    if background.size[0] < bg_width or background.size[1] < bg_height:
        background = background.resize((bg_width, bg_height), Image.LANCZOS)
    else:
        # Обрезаем фон до нужных размеров, если он больше чем нужно
        x_offset = (background.size[0] - bg_width) // 2
        y_offset = (background.size[1] - bg_height) // 2
        background = background.crop((x_offset, y_offset, x_offset + bg_width, y_offset + bg_height))

    blur_amount = random.randint(*config.blur_range)
    background = background.filter(ImageFilter.GaussianBlur(blur_amount))

    opacity = config.background_opacity_range[0] / 100.0
    alpha = Image.new("L", background.size, int(opacity * 255))
    background.putalpha(alpha)

    return background, bg_path

class MetadataManager:
    def __init__(self, config):
        self.config = config

    def _get_random_string(self, length=16):
        letters = string.ascii_letters + string.digits
        return ''.join(random.choice(letters) for _ in range(length))

    def _generate_camera_settings(self):
        """Generates realistic camera settings"""
        return {
            "iso": random.choice([100, 200, 400, 800, 1600, 3200]),
            "shutter_speed": random.choice(['1/125', '1/250', '1/500', '1/1000', '1/2000']),
            "aperture": random.choice(['f/1.8', 'f/2.8', 'f/4.0', 'f/5.6', 'f/8.0']),
            "focal_length": random.randint(24, 200),
            "white_balance": random.choice(['Auto', 'Daylight', 'Cloudy', 'Tungsten']),
            "exposure_compensation": random.choice([-2.0, -1.0, 0, 1.0, 2.0])
        }

    def _generate_geotags(self, date=None):
        current_date = date if date else datetime.now()
        locations = [
            (40.7128, -74.0060),   # New York
            (51.5074, -0.1278),    # London
            (48.8566, 2.3522),     # Paris
            (35.6762, 139.6503),   # Tokyo
            (55.7558, 37.6173),    # Moscow
            (-33.8688, 151.2093),  # Sydney
            (34.0522, -118.2437),  # Los Angeles
            (41.8781, -87.6298),   # Chicago
            (29.7604, -95.3698),   # Houston
            (43.6532, -79.3832),   # Toronto
            (19.4326, -99.1332),   # Mexico City
            (-22.9068, -43.1729),  # Rio de Janeiro
            (-34.6037, -58.3816),  # Buenos Aires
            (40.4168, -3.7038),    # Madrid
            (41.9028, 12.4964),    # Rome
            (52.5200, 13.4050),    # Berlin
            (52.3676, 4.9041),     # Amsterdam
            (37.9838, 23.7275),    # Athens
            (30.0444, 31.2357),    # Cairo
            (31.7683, 35.2137),    # Jerusalem
            (25.2048, 55.2708),    # Dubai
            (19.0760, 72.8777),    # Mumbai
            (28.6139, 77.2090),    # Delhi
            (13.7563, 100.5018),   # Bangkok
            (1.3521, 103.8198),    # Singapore
            (39.9042, 116.4074),   # Beijing
            (37.5665, 126.9780),   # Seoul
            (31.2304, 121.4737),   # Shanghai
            (22.3193, 114.1694),   # Hong Kong
            (-37.8136, 144.9631)   # Melbourne
        ]
        lat, lon = random.choice(locations)
        lat += random.uniform(-0.1, 0.1)
        lon += random.uniform(-0.1, 0.1)

        return {
        "latitude": round(lat, 6),
        "longitude": round(lon, 6),
        "altitude": random.randint(0, 1000),
        "gps_timestamp": datetime.now().strftime("%H:%M:%S"),
        "gps_datestamp": datetime.now().strftime("%Y:%m:%d")
    }

    def remove_metadata(self, image):
        """Removes all metadata from image"""
        try:
            data = list(image.getdata())
            image_without_exif = Image.new(image.mode, image.size)
            image_without_exif.putdata(data)
            return image_without_exif
        except Exception as e:
            print(f"Warning: Could not remove metadata: {str(e)}")
            return image

    def _convert_to_exif(self, metadata):
        exif_dict = {"0th":{}, "Exif":{}, "GPS":{}, "1st":{}, "thumbnail":None}

        # Basic info
        exif_dict["0th"][piexif.ImageIFD.DateTime] = metadata["creation_date"].encode('utf-8')
        exif_dict["0th"][piexif.ImageIFD.Make] = metadata["device_info"]["manufacturer"].encode('utf-8')
        exif_dict["0th"][piexif.ImageIFD.Model] = metadata["device_info"]["model"].encode('utf-8')

        # Camera settings
        camera_settings = metadata["camera_settings"]
        exif_dict["Exif"][piexif.ExifIFD.ISOSpeedRatings] = camera_settings["iso"]
        exif_dict["Exif"][piexif.ExifIFD.ExposureTime] = self._fraction_to_tuple(camera_settings["shutter_speed"])
        exif_dict["Exif"][piexif.ExifIFD.FNumber] = self._fraction_to_tuple(camera_settings["aperture"].replace('f/', ''))
        exif_dict["Exif"][piexif.ExifIFD.FocalLength] = (camera_settings["focal_length"], 1)
        exif_dict["Exif"][piexif.ExifIFD.WhiteBalance] = 0 if camera_settings["white_balance"] == "Auto" else 1

        # Geotags
        geotags = metadata["geotags"]
        exif_dict["GPS"][piexif.GPSIFD.GPSLatitude] = self._decimal_to_dms(abs(geotags["latitude"]))
        exif_dict["GPS"][piexif.GPSIFD.GPSLatitudeRef] = 'N'.encode('utf-8') if geotags["latitude"] >= 0 else 'S'.encode('utf-8')
        exif_dict["GPS"][piexif.GPSIFD.GPSLongitude] = self._decimal_to_dms(abs(geotags["longitude"]))
        exif_dict["GPS"][piexif.GPSIFD.GPSLongitudeRef] = 'E'.encode('utf-8') if geotags["longitude"] >= 0 else 'W'.encode('utf-8')
        exif_dict["GPS"][piexif.GPSIFD.GPSAltitude] = (geotags["altitude"], 1)

        time_parts = [int(x) for x in geotags["gps_timestamp"].split(':')]
        exif_dict["GPS"][piexif.GPSIFD.GPSTimeStamp] = tuple((x, 1) for x in time_parts)

        return exif_dict

    def _generate_random_date(self):
        """Генерирует случайную дату с 01.01.2024 по 19.03.2025"""

        start_date = datetime(2024, 1, 1, 0, 0, 0)

        end_date = datetime(2025, 3, 19, 23, 59, 59)

        delta_seconds = int((end_date - start_date).total_seconds())

        random_seconds = random.randint(0, delta_seconds)

        random_date = start_date + timedelta(seconds=random_seconds)

        return random_date

    def _fraction_to_tuple(self, fraction_str):
        """Converts fraction string to tuple for EXIF"""
        if '/' in fraction_str:
            num, denom = map(int, fraction_str.split('/'))
            return (num, denom)
        return (int(float(fraction_str) * 10000), 10000)

    def _decimal_to_dms(self, decimal):
        """Converts decimal degrees to degrees, minutes, seconds for EXIF"""
        degrees = int(decimal)
        minutes = int((decimal - degrees) * 60)
        seconds = int(((decimal - degrees) * 60 - minutes) * 60 * 100)
        return ((degrees, 1), (minutes, 1), (seconds, 100))

    def _get_device_info(self):
        """Генерирует правдоподобную информацию об устройстве"""
        devices = [
            # iPhones
            ("Apple", "iPhone 16 Pro Max", "18.1.0"),
            ("Apple", "iPhone 16 Pro", "18.1.0"),
            ("Apple", "iPhone 16 Plus", "18.1.0"),
            ("Apple", "iPhone 16", "18.1.0"),
            ("Apple", "iPhone 15 Pro Max", "17.1.1"),
            ("Apple", "iPhone 15 Pro", "17.1.1"),
            ("Apple", "iPhone 15 Plus", "17.1.1"),
            ("Apple", "iPhone 15", "17.1.1"),
            ("Apple", "iPhone 14 Pro Max", "17.1.1"),
            ("Apple", "iPhone 14 Pro", "17.1.1"),
            ("Apple", "iPhone 14 Plus", "17.1.1"),
            ("Apple", "iPhone 14", "17.1.1"),
            ("Apple", "iPhone 13 Pro Max", "17.1.1"),
            ("Apple", "iPhone 13 Pro", "17.1.1"),
            ("Apple", "iPhone 13", "17.1.1"),
            ("Apple", "iPhone 13 mini", "17.1.1"),
            ("Apple", "iPhone 12 Pro Max", "17.1.1"),
            ("Apple", "iPhone 12 Pro", "17.1.1"),
            ("Apple", "iPhone 12", "17.1.1"),
            ("Apple", "iPhone 12 mini", "17.1.1"),
            ("Apple", "iPhone 11 Pro Max", "17.1.1"),
            ("Apple", "iPhone 11 Pro", "17.1.1"),
            ("Apple", "iPhone 11", "17.1.1"),
            ("Apple", "iPhone XS Max", "16.7.2"),
            ("Apple", "iPhone XS", "16.7.2"),
            ("Apple", "iPhone XR", "16.7.2"),

            # Samsung phones
            ("Samsung", "Galaxy S24 Ultra", "Android 14"),
            ("Samsung", "Galaxy S24+", "Android 14"),
            ("Samsung", "Galaxy S24", "Android 14"),
            ("Samsung", "Galaxy S23 Ultra", "Android 14"),
            ("Samsung", "Galaxy S23+", "Android 14"),
            ("Samsung", "Galaxy S23", "Android 14"),
            ("Samsung", "Galaxy Z Fold5", "Android 14"),
            ("Samsung", "Galaxy Z Flip5", "Android 14"),

            # Google phones
            ("Google", "Pixel 8 Pro", "Android 14"),
            ("Google", "Pixel 8", "Android 14"),
            ("Google", "Pixel 7 Pro", "Android 14"),
            ("Google", "Pixel 7", "Android 14"),
            ("Google", "Pixel 7a", "Android 14"),

            # Professional cameras
            ("Canon", "EOS R5", "1.9.0"),
            ("Canon", "EOS R6 Mark II", "1.3.0"),
            ("Canon", "EOS R3", "1.5.0"),
            ("Sony", "A7R V", "1.1.0"),
            ("Sony", "A7 IV", "2.0.0"),
            ("Sony", "A1", "1.4.0"),
            ("Nikon", "Z9", "4.01"),
            ("Nikon", "Z8", "1.01"),
            ("Nikon", "Z7 II", "1.40"),
            ("Fujifilm", "X-T5", "2.01"),
            ("Fujifilm", "X-H2S", "3.00"),
            ("Fujifilm", "GFX 100S", "4.01")
        ]
        manufacturer, model, software = random.choice(devices)
        return {
            "manufacturer": manufacturer,
            "model": model,
            "software": software
        }

    def _get_technical_info(self, image):
        """Получает техническую информацию об изображении"""
        width, height = image.size
        return {
            "width": width,
            "height": height,
            "color_mode": image.mode,
            "format": image.format or "JPEG"
        }

    def _create_custom_metadata(self, image):
        device_info = self._get_device_info()
        technical_info = self._get_technical_info(image)
        camera_settings = self._generate_camera_settings()

        # Генерируем случайную дату для метаданных
        random_date = self._generate_random_date()
        date_string = random_date.strftime("%Y:%m:%d %H:%M:%S")

        # Используем эту же дату для геотегов
        geotags = self._generate_geotags(random_date)

        metadata = {
            "unique_id": self._get_random_string(),
            "creation_date": date_string,  # Используем случайную дату
            "device_info": device_info,
            "technical_info": technical_info,
            "camera_settings": camera_settings,
            "geotags": geotags
        }

        # Добавляем processing_info только если настройка включена
        if getattr(self.config, 'add_processing_info', False):
            processing_info = self._get_processing_info()
            metadata["processing_info"] = processing_info

        return metadata

    def add_metadata(self, image_path, image):
        """Добавляет метаданные в изображение"""
        try:
            metadata = self._create_custom_metadata(image)
            exif_dict = self._convert_to_exif(metadata)
            exif_bytes = piexif.dump(exif_dict)
            piexif.insert(exif_bytes, image_path)
            print(f"Metadata added successfully to {image_path}")
            return True
        except Exception as e:
            print(f"Warning: Could not add metadata: {str(e)}")
            return False

    def read_metadata(self, image_path):
        """Читает и возвращает метаданные изображения"""
        try:
            exif_dict = piexif.load(image_path)
            metadata = {}

            if piexif.ImageIFD.Software in exif_dict["0th"]:
                metadata["software"] = exif_dict["0th"][piexif.ImageIFD.Software].decode('utf-8')

            if piexif.ImageIFD.DateTime in exif_dict["0th"]:
                metadata["datetime"] = exif_dict["0th"][piexif.ImageIFD.DateTime].decode('utf-8')

            if piexif.ImageIFD.Make in exif_dict["0th"]:
                metadata["manufacturer"] = exif_dict["0th"][piexif.ImageIFD.Make].decode('utf-8')

            if piexif.ImageIFD.Model in exif_dict["0th"]:
                metadata["model"] = exif_dict["0th"][piexif.ImageIFD.Model].decode('utf-8')

            # Camera settings
            if piexif.ExifIFD.ISOSpeedRatings in exif_dict["Exif"]:
                metadata["iso"] = exif_dict["Exif"][piexif.ExifIFD.ISOSpeedRatings]

            if piexif.ExifIFD.ExposureTime in exif_dict["Exif"]:
                num, den = exif_dict["Exif"][piexif.ExifIFD.ExposureTime]
                metadata["shutter_speed"] = f"{num}/{den}"

            if piexif.ExifIFD.FNumber in exif_dict["Exif"]:
                num, den = exif_dict["Exif"][piexif.ExifIFD.FNumber]
                metadata["aperture"] = f"f/{num/den:.1f}"

            # GPS data
            if "GPS" in exif_dict and exif_dict["GPS"]:
                metadata["gps"] = {}
                if piexif.GPSIFD.GPSLatitude in exif_dict["GPS"]:
                    lat = self._dms_to_decimal(exif_dict["GPS"][piexif.GPSIFD.GPSLatitude])
                    if piexif.GPSIFD.GPSLatitudeRef in exif_dict["GPS"]:
                        if exif_dict["GPS"][piexif.GPSIFD.GPSLatitudeRef] == b'S':
                            lat = -lat
                    metadata["gps"]["latitude"] = lat

                if piexif.GPSIFD.GPSLongitude in exif_dict["GPS"]:
                    lon = self._dms_to_decimal(exif_dict["GPS"][piexif.GPSIFD.GPSLongitude])
                    if piexif.GPSIFD.GPSLongitudeRef in exif_dict["GPS"]:
                        if exif_dict["GPS"][piexif.GPSIFD.GPSLongitudeRef] == b'W':
                            lon = -lon
                    metadata["gps"]["longitude"] = lon

            return metadata

        except Exception as e:
            print(f"Warning: Could not read metadata: {str(e)}")
            return None

    def _dms_to_decimal(self, dms):
        """Converts EXIF DMS format to decimal degrees"""
        degrees = dms[0][0] / dms[0][1]
        minutes = dms[1][0] / dms[1][1] / 60.0
        seconds = dms[2][0] / dms[2][1] / 3600.0
        return degrees + minutes + seconds
def main():
    # Инициализация конфигурации
    config = UniqualizerConfig()

    # Создаем необходимые директории
    for directory in [config.input_dir, config.output_dir, config.emoji_dir, config.config_dir]:
        try:
            os.makedirs(directory, exist_ok=True)
        except Exception as e:
            print(f"Внимание: Не удалось создать директорию {directory}: {str(e)}")

    try:
        # Получаем входной файл
        input_image_path = get_input_file()

        if not input_image_path:
            print("Входной файл не найден. Завершение работы.")
            return

        # Запрашиваем количество изображений
        while True:
            try:
                num_images = int(input("Сколько изображений сгенерировать? "))
                if num_images > 0:
                    break
                print("Пожалуйста, введите положительное число.")
            except ValueError:
                print("Пожалуйста, введите корректное число.")

        # 25 потоков для обработки
        num_threads = 25
        images_per_thread = num_images // num_threads
        extra_images = num_images % num_threads

        print(f"\nНачинаем генерацию {num_images} изображений...")
        print(f"Используется потоков: {num_threads}")
        print(f"Входное изображение: {input_image_path}")
        print(f"Выходная директория: {config.output_dir}\n")

        start_time = time.time()
        threads = []

        try:
            for i in range(num_threads):
                num_images_this_thread = images_per_thread + (1 if i < extra_images else 0)
                start_id = i * images_per_thread + min(i, extra_images)
                thread = Thread(
                    target=process_worker,
                    args=(input_image_path, config, start_id, num_images_this_thread)
                )
                threads.append(thread)
                thread.start()

            for thread in threads:
                thread.join()

            total_time = time.time() - start_time
            images_per_minute = (num_images / total_time) * 60

            print(f"\nВсе изображения сгенерированы успешно!")
            print(f"Общее время генерации: {total_time:.2f} секунд")
            print(f"Скорость генерации: {images_per_minute:.2f} изображений в минуту")
            print(f"Директория с результатами: {config.output_dir}")

        except KeyboardInterrupt:
            print("\nОперация отменена пользователем. Завершение...")
            sys.exit(1)

    except Exception as e:
        print(f"\nПроизошла ошибка: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
     main()
