"""
Внешние сервисы MagicUniq.

Содержит реализации различных сервисов:
- Сервер изображений
- Генератор контента
"""

# Импортируем полные реализации
try:
    from .image_server import ImageServer
    IMAGE_SERVER_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Image server not available: {e}")
    ImageServer = None
    IMAGE_SERVER_AVAILABLE = False

try:
    from .content_generator import CombinedGenerator as ContentGenerator
    CONTENT_GENERATOR_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Content generator not available: {e}")
    ContentGenerator = None
    CONTENT_GENERATOR_AVAILABLE = False

__all__ = []

if IMAGE_SERVER_AVAILABLE:
    __all__.append("ImageServer")

if CONTENT_GENERATOR_AVAILABLE:
    __all__.append("ContentGenerator")
