from aiohttp import web
import os
import random
import time
import asyncio
import mimetypes
from collections import defaultdict
import traceback
import json
from datetime import datetime
from pathlib import Path
import aiofiles

# Configuration
WEBSERVER_PORT = 80
SERVER_IP = 'server.magicuniq.space'
MAX_IMAGES = 10000

# Global variables for image tracking
image_usage_tracking = defaultdict(set)  # track {image_name: set of tokens}
image_last_used = {}  # track when image was last used
REUSE_TIMEOUT = 300  # 5 minutes timeout before image can be reused
file_usage_counter = {}
file_lock = asyncio.Lock()

class ImageServer:
    def __init__(self):
        self.background_generator = None
        self.overlay_generator = None
        self.is_running = True

    async def initialize_generators(self):
        # Список аккаунтов
        accounts = [
            {
                "api_key": "********************************",
                "secret_key": "CB9EB2D550EA66D708601D2C0EE97ED4"
            },
            {
                "api_key": "B99D6F9FA87820336880A337AFD42BD9",
                "secret_key": "80EF97EEAB92902FA28D43EA5AD2B981"
            },
            {
                "api_key": "0F4C95D11DF72B20EB148E3CE83C642D",
                "secret_key": "F749531F9E0D603A6AF692A53E968BFE"
            },
            {
                "api_key": "194DF8C7B84BDE6A22B152DCFAB328AD",
                "secret_key": "2764C94E25ECE5984558A61BC91D6826"
            },
            {
                "api_key": "5478E6DEF3D5BE0CA77535E544049E55",
                "secret_key": "339AA2D557EE5430088B9AB6A56DB5F6"
            },
            {
                "api_key": "7750EDFD36971CD7F6EFC4BE1DA845B3",
                "secret_key": "7C9F3BB7D2E19D277F7F433DFD02464B"
            },
            {
                "api_key": "15306067641844C1E7284BFE3786041B",
                "secret_key": "38B626D266D924E812CBCE38BEC83FBB"
            },
            {
                "api_key": "B7CC3DAFE5E1A2C01A8D67D81BB1C496",
                "secret_key": "CE9CA7053004C687D09ADE81466E995C"
            },
            {
                "api_key": "23A0010BC9D720DB3694602EDE02CEC7",
                "secret_key": "56A004539C4ACB5468853F06722C731A"
            },
            {
                "api_key": "E3B6803C8160844B9F706024666B31AB",
                "secret_key": "75A3178A7C4A8D3A78D051F941C06A99"
            },
            {
                "api_key": "3CBE43A828FEBFD4469CC5FD45595199",
                "secret_key": "58B3CDAB99E6B0BFA7D8ADF00FEA484D"
            },
            {
                "api_key": "FD6BF32737D506243DD84A7599876675",
                "secret_key": "4F3BC044B5C58C111694D8FF7EE96008"
            },
            {
                "api_key": "D0F80563652AC37E234BF046D01E5EC4",
                "secret_key": "6EDD90617FF9A128B9DC8D5F5B63E122"
            },
            {
                "api_key": "3C7BC2F6663EE7A129EF5B3A95A8113E",
                "secret_key": "E6A99687A056BF1E519B1A331E8FD03D"
            },
            {
                "api_key": "A5A6E7047BB44370C841B0A008E25B90",
                "secret_key": "513CB77D6BB34092D66A4BA541AB0972"
            },
            {
                "api_key": "C26F79DE58DAD026A8475EB81757DEF2",
                "secret_key": "A921E13CF67EA38D151C0404213D82F1"
            },
            {
                "api_key": "B30DE7160055BCFE5ADAEFA412742766",
                "secret_key": "A3B151C43D90DB64B084766D3F07E2B0"
            },
            {
                "api_key": "C24C6BCB805E4C5762A1EC5E554B3660",
                "secret_key": "DB0876EAA286434A305553DF3245EE69"
            },
            {
                "api_key": "85807F1B78E73014A947BA820DC5C86F",
                "secret_key": "4B2467E519884856CC5EDB0398C66380"
            },
            {
                "api_key": "A442EA2728CAA9BCAE12BB631D6BF2C6",
                "secret_key": "FDA66EC85BD3A26715691EE635952F45"
            },
            {
                "api_key": "DA3AC820C32A9C10A1EF49ED015BBA27",
                "secret_key": "419B21E3598A87E2D7CBB27396088D24"
            },
            {
                "api_key": "5EADC73D5EB5EF2374C70159D699D58C",
                "secret_key": "C5C0CDCE345AD10BBBFA5F10EA0D7106"
            },
            {
                "api_key": "1BE0C243AC9894766BDAE6E06637D219",
                "secret_key": "6BB830897B1B02443BD04C955F00427E"
            },
            {
                "api_key": "B1701C9CDE00B976CF6B9DA634D98DCB",
                "secret_key": "84515F2C3C16E1DA7162A8BA74C1708A"
            },
            {
                "api_key": "798697B7B7E9F38A612703F7D5AAD8D1",
                "secret_key": "FB1A0C0A5571F488BC04E22C1D6AD45C"
            },
            {
                "api_key": "48EE6ADA1302AC6001685092E670D405",
                "secret_key": "5F7FBFDE62AE589357A92ED3BABC3CB0"
            },
            {
                "api_key": "104E2B5F03925ED699640E14C34675A2",
                "secret_key": "0178B2636DEC8300CC1117C3D08FA88A"
            },
            {
                "api_key": "57AEFF467BD712BEE01A02AB00CE14B0",
                "secret_key": "E9790A204CFE30E171D37F8C4F49AB18"
            },
            {
                "api_key": "543A930E0469B75A5564EE53474E72B1",
                "secret_key": "0274B082D371020882A7EDD484931EEB"
            },
            {
                "api_key": "F6E1AADBEEA7A47F341AA163260267C5",
                "secret_key": "543035B50AF1C1A9A359BF6D4B425086"
            },
            {
                "api_key": "09422C8B9C7AC715287B00ED07EC5993",
                "secret_key": "C5E2D7356E6D04EB6CCB59C4BB082F66"
            },
            {
                "api_key": "170768AD01115B3380EADDE459C4DC8F",
                "secret_key": "58811D14506120DD0BC24C6CEBA51EA8"
            },
            {
                "api_key": "77C12602D0A506B883A264803D48AF48",
                "secret_key": "2E06484D58B002ED3F365B8EF2D70000"
            },
            {
                "api_key": "276FC1B33985CF2129842F329893A357",
                "secret_key": "E6CE60320E055B959908F556F40A8460"
            },
            {
                "api_key": "3400386AA7349DE674BEB061C92A9BAF",
                "secret_key": "9F297437BCC26605059B3ADFBA49B730"
            },
            {
                "api_key": "E5FD4893107AB0B71452B5E6F022C7A7",
                "secret_key": "AC3960F3A714D36BE92BB8994022267F"
            },
            {
                "api_key": "028F8CF8DAB18095F31F98279942CF01",
                "secret_key": "8ABA2CDF9D91BDA8C2533EE6B49C3947"
            },
            {
                "api_key": "DE14D7F60AF2C6794FA32C24C57E6EEB",
                "secret_key": "20526642A885A2C985E8EC61048385E4B"
            },
            {
                "api_key": "48D988C3FBA45F91D8DCB4F083E3F257",
                "secret_key": "15D118612D1BFAFA36C3D35789216B1A"
            },
            {
                "api_key": "A6B8AA725EFCB0944D45FDBE670F0CCB",
                "secret_key": "2EA84C83A0E98FBAB787923403B92D25"
            }
        ]
    # Промпты для фонов (из первого скрипта)
        bg_prompts = [
            "Тихий городской парк ранним утром, мягкий свет сквозь деревья, пустые скамейки, нейтральные тона",
            "Минималистичный интерьер офиса, большие окна, мягкий дневной свет, деревянный стол и стул",
            "Пустынная дорога через лес, легкий туман, рассветное освещение, спокойные зеленые и серые тона",
            "Старый мост через реку, мягкий свет от заката, едва заметные облака на горизонте",
            "Интерьер современной квартиры, минималистичная мебель, нейтральная цветовая гамма, мягкое освещение",
            "Городская улица под дождем, мокрый асфальт, отражение фонарей, приглушенные тона",
            "Берег озера в сумерках, спокойная водная гладь, едва заметные силуэты деревьев",
            "Промышленный цех с большими окнами, мягкий свет, металлические конструкции, нейтральные тона",
            "Пустая автостоянка вечером, длинные тени от фонарей, легкая дымка в воздухе",
            "Старая библиотека с высокими полками, мягкий свет из окон, деревянные детали интерьера",
            "Дорога через поле, окруженное травой, пасмурное небо, легкий ветер, землистые тона",
            "Кофейня с деревянными столами, мягкий свет от ламп, чашки на стойке, приглушенные коричневые оттенки",
            "Пустынный вокзал ночью, тусклый свет от ламп, силуэт поезда вдалеке",
            "Горный пейзаж на рассвете, приглушенные пастельные тона, туман между вершинами",
            "Современный лофт с кирпичными стенами, бетонный пол, металлические балки, нейтральная палитра",
            "Пустынная набережная в сумерках, мягкий свет фонарей, едва заметные волны на воде",
            "Лесная тропинка в тумане, мягкий свет сквозь деревья, приглушенные зеленые и серые тона",
            "Интерьер спортзала, минималистичное освещение, оборудование из металла, нейтральные тона",
            "Пустая терраса кафе, мягкий свет от фонарей, вид на городские огни вдали",
            "Дорога через пустыню, рассветное освещение, едва заметные облака на горизонте",
            "Старый порт, мягкий свет от фонарей, силуэты лодок вдалеке, приглушенные тона",
            "Пустынная автозаправка ночью, тусклый свет от ламп, легкая дымка в воздухе",
            "Минималистичный интерьер гостиной, большие окна, мягкий свет, нейтральная цветовая гамма",
            "Городской парк зимой, мягкий свет от фонарей, едва заметные следы на снегу",
            "Пустынная дорога через холмы, пасмурное небо, легкий туман, землистые тона",
            "Старый маяк на берегу, мягкий свет от заката, едва заметные волны на воде",
            "Реалистичный горный пейзаж на рассвете, мягкие пастельные тона, туман, медленно поднимающийся над долиной, вдали виднеются заснеженные вершины, атмосфера умиротворения и спокойствия",
            "Уютный камин в деревянном доме, теплый свет огня, темный интерьер с мягкими тенями, на полу ковер, на стенах картины в деревянных рамках, атмосфера домашнего уюта",
            "Тихое озеро в лесу, отражение деревьев в воде, мягкие зеленые и синие оттенки, на поверхности воды легкая рябь, вдали виднеются горы, атмосфера гармонии с природой",
            "Городской пейзаж ночью, огни небоскребов, дождь на асфальте, мокрые тротуары, отражения фонарей в лужах, атмосфера мегаполиса в дождливый вечер",
            "Пустыня с песчаными дюнами, закатное солнце, длинные тени, песок переливается золотистыми оттенками, вдали виднеется одинокое дерево, атмосфера уединения",
            "Старый каменный мост через реку, осенние деревья, желтые и оранжевые листья, легкий туман над водой, атмосфера осеннего утра",
            "Каменный пляж с волнами, серое небо, вода бьется о камни, вдали виднеются скалы, атмосфера спокойствия и силы природы",
            "Лесная тропа, окруженная высокими деревьями, солнечные лучи пробиваются сквозь листву, мягкий зеленый свет, атмосфера утренней прогулки",
            "Поле с дикими цветами, закатное солнце, длинные тени, легкий ветер колышет траву, атмосфера летнего вечера",
            "Старый замок на холме, туман вокруг, серое небо, каменные стены покрыты мхом, атмосфера загадочности и истории",
            "Дорога через горы, извилистый серпантин, облака ниже уровня дороги, вдали виднеются снежные вершины, атмосфера путешествия",
            "Тропический пляж с пальмами, бирюзовая вода, белый песок, легкие облака на небе, атмосфера расслабления и отдыха",
            "Заснеженный лес, снег на ветках деревьев, тишина, легкий туман, атмосфера зимнего утра",
            "Портовый город, лодки в гавани, старые здания, мягкий свет заката, атмосфера морского приключения",
            "Деревянная хижина в горах, дым из трубы, вокруг заснеженные вершины, атмосфера уединения и тепла",
            "Поле подсолнухов, яркое солнце, желтые цветы на фоне голубого неба, атмосфера летнего дня",
            "Каменный каньон, высокие стены, свет солнца пробивается сквозь узкое ущелье, атмосфера величия природы",
            "Деревенский пейзаж, старый сарай, поле с высокой травой, легкий ветер, атмосфера спокойной сельской жизни",
            "Городской парк осенью, скамейки, опавшие листья, легкий туман, атмосфера уединения в городе",
            "Водопад в джунглях, густая зелень, брызги воды, легкий туман, атмосфера дикой природы",
            "Старый маяк на скале, бурное море, волны разбиваются о камни, атмосфера силы и стойкости",
            "Дорога через пустыню, бесконечный горизонт, редкие кактусы, атмосфера путешествия в неизвестность",
            "Лесной ручей, прозрачная вода, камни на дне, солнечные лучи пробиваются сквозь деревья, атмосфера свежести",
            "Городская улица в дождь, мокрый асфальт, отражения фонарей, люди под зонтами, атмосфера вечернего мегаполиса",
            "Зимний лес, заснеженные деревья, тишина, легкий туман, атмосфера уединения и покоя",
            "Пляж с камнями, волны, серое небо, вдали виднеются скалы, атмосфера спокойствия и силы",
            "Горная река, быстрый поток, камни в воде, вокруг густой лес, атмосфера дикой природы",
            "Старый железнодорожный мост, ржавые рельсы, вокруг густая зелень, атмосфера заброшенности и истории",
            "Поле с лавандой, фиолетовые цветы, голубое небо, легкий ветер, атмосфера летнего дня",
            "Городская набережная, велосипеды, скамейки, река, атмосфера спокойного вечера",
            "Лесная поляна, грибы, мох, солнечные лучи пробиваются сквозь деревья, атмосфера сказки",
            "Дорога через поле, бесконечный горизонт, облака на небе, атмосфера свободы",
            "Старый корабль на берегу, ржавый корпус, песок вокруг, атмосфера заброшенности и истории",
            "Горное озеро, кристально чистая вода, вокруг заснеженные вершины, атмосфера уединения",
            "Деревенский дом, дым из трубы, заснеженные поля, атмосфера зимнего уюта",
            "Поле с маками, красные цветы, зеленые травы, голубое небо, атмосфера летнего дня",
            "Городской мост ночью, огни города, отражения в воде, атмосфера романтики",
            "Лесная дорога, туман, высокие деревья, атмосфера загадочности",
            "Пляж с ракушками, волны, легкий ветер, атмосфера спокойствия",
            "Горная тропа, извилистая дорога, вокруг скалы, атмосфера приключения"
        ]

    # Промпты для оверлеев (из второго скрипта)
        ol_prompts = [
            "Размытое изображение облаков с нежными оттенками серого и белого, легкое и воздушное, без четких контуров",
            "Спокойная водная поверхность с легкими волнами и приглушенными отражениями в серо-голубых тонах, размытый эффект",
            "Легкий дым с мягкими переходами и приглушенными серыми тонами, абстрактная дымка",
            "Легкий туман с мягкими переходами и приглушенными серыми тонами, абстрактная дымка",
            "Мягкое изображение леса в густом тумане, с приглушёнными зелёными и серыми оттенками, без чётких деталей, создающее ощущение дымки и спокойствия",
            "Плавные очертания гор, окутанных густыми облаками, с нейтральными серыми и белыми тонами, размытые линии вершин, напоминающие дым",
            "Спокойное море с мягким туманом над поверхностью, приглушённые синие и серые оттенки, размытый горизонт, похожий на облака или дым",
            "Размытое изображение водопада, где падающая вода сливается с туманом, мягкие белые и серые тона, создающие эффект дымного движения",
            "Лес с верхушками деревьев, скрытыми в низких облаках, приглушённые зелёные и белые оттенки, размытые контуры, напоминающие туман или дым",
            "Туманные абстрактные потоки с плавными переходами серого и белого, образующие легкую дымку, словно раннее утро в горах, когда туман стелется по долине, создавая мягкие, размытые силуэты, без резких контрастов и чётких линий",
            "Монохромные акварельные разводы в оттенках серого и белого, с легкими размытыми границами и плавными переходами, создающие эффект туманной дымки без ярких цветов",
            "Нежные акварельные пятна в приглушенных бежевых и светло-серых тонах, с мягкими размытыми краями и легкими переливами, образующие спокойный, нейтральный фон",
            "Светлые дымчатые текстуры с едва заметными переходами от белого к светло-серому, создающие воздушный эффект без насыщенных оттенков, словно утренний туман над водой",
            "Минималистичные акварельные разводы в пастельных нейтральных тонах, преимущественно белый, серый и светло-бежевый, с размытыми границами и мягкими переходами",
            "Тонкие слои прозрачной краски в оттенках слоновой кости и светло-серого, с едва заметными переходами и легкой текстурой, напоминающей старую бумагу",
            "Приглушенные разводы в монохромной гамме, имитирующие легкий дым или туман, с мягкими градиентами от белого к светло-серому, без ярких акцентов",
            "Нейтральные акварельные текстуры в оттенках светлого кофе с молоком и серого, с плавными переходами и размытыми контурами, создающие спокойный фон без насыщенных цветов",
            "Светлые мраморные текстуры с едва различимыми прожилками в тонах слоновой кости и светлого песка, с мягкими переходами и нежными оттенками без яркости",
            "Минималистичные разводы в оттенках холодного серого и белого, создающие ощущение легкой туманности и воздушности, с плавными переходами и отсутствием резких контрастов",
            "Нежная текстура старинной бумаги с легкими потертостями и мягкими тональными переходами в светло-бежевых и кремовых оттенках, без ярких цветовых акцентов",
            "Легкие абстрактные мазки в светло-серой и белой гамме, напоминающие размытое отражение в воде, с мягкими, расплывчатыми переходами и без интенсивных цветов",
            "Приглушенная текстура белого мрамора с тонкими серыми прожилками, мягкими переходами и нежными оттенками, создающая элегантный фон без насыщенных цветов",
            "Легкие акварельные разводы в мягких оттенках серого и белого, создающие воздушную текстуру с плавными переходами без ярких акцентов",
            "Нежные дымчатые потоки приглушенных бежевых тонов, размытые и полупрозрачные, словно утренний туман над песчаным пляжем",
            "Минималистичная текстура светлого мрамора с едва заметными серыми прожилками и мягкими переходами между оттенками",
            "Монохромные абстрактные пятна с размытыми краями в тонах слоновой кости, создающие спокойный нейтральный фон",
            "Акварельные разводы в приглушенных оттенках светло-серого, с мягкими границами и легкой текстурой старой бумаги",
            "Светлые облачные формации в нейтральных тонах, размытые и воздушные, словно белый дым на сером небе",
            "Нежные абстрактные потеки в оттенках светлого кофе с молоком, с плавными переходами и размытыми краями",
            "Мягкая текстура состаренной бумаги с легкими неровностями и теплыми кремовыми оттенками без яркого цветового контраста",
            "Светлые туманные вуали в холодных серых тонах, создающие впечатление утреннего марева над спокойной водой",
            "Приглушенные мраморные разводы в оттенках белого и светло-бежевого, с нежными переходами и элегантной текстурой",
            "Легкая песчаная текстура с мягкими тональными переходами в светло-бежевых оттенках, напоминающая спокойную пустыню",
            "Бледные акварельные мазки в оттенках холодного серого, создающие ощущение легкого тумана без ярких цветовых пятен",
            "Мягкие дымчатые струи в монохромных светло-серых тонах, плавно переходящие от одного оттенка к другому",
            "Нейтральные абстрактные формы с размытыми границами в оттенках светлого песка, создающие спокойный, гармоничный фон",
            "Легкая текстура потрескавшегося гипса в белых и кремовых тонах, с мягкими переходами и приглушенными контрастами",
            "Воздушные акварельные пятна в оттенках светлой умбры и серого, создающие мягкий, нейтральный фоновый эффект",
            "Нежные разводы в светло-кофейных тонах, напоминающие легкую дымку над поверхностью теплого молока с размытыми контурами",
            "Мягкие абстрактные текстуры состаренного пергамента с едва заметными потертостями и теплыми бежевыми оттенками",
            "Монохромные водяные разводы в оттенках графита и белого, создающие эффект размытых отражений на мокром асфальте",
            "Светлые мраморные текстуры с тонкими серыми прожилками и мягкими, едва заметными переходами между нейтральными оттенками",
            "Приглушенные акварельные мазки в тонах слоновой кости, создающие эффект старинной, выцветшей на солнце бумаги",
            "Легкие разводы пастельных серо-бежевых оттенков, размытые и нежные, словно утренний туман над песчаными дюнами",
            "Минималистичные текстуры светлого бетона с мягкими переходами и легкими потертостями в нейтральных серых тонах",
            "Нежные абстрактные формы в оттенках пепельно-белого, создающие воздушный, полупрозрачный фон без ярких акцентов",
            "Светлые песочные текстуры с тонкими разводами в тёплых нейтральных тонах, словно след волны на мокром песке",
            "Приглушенные акварельные потеки в холодных серых оттенках, размытые и лёгкие, напоминающие зимнее утреннее небо",
            "Мягкие мраморные разводы в оттенках белого и светло-серого с плавными переходами и нежной элегантной текстурой",
            "Воздушные абстрактные формы в тонах слоновой кости, создающие ощущение легкости и мягкости без заметных контрастов",
            "Нейтральные акварельные разводы молочно-белых и светло-песочных оттенков с размытыми краями и мягкими переходами",
            "Светлые дымчатые полупрозрачные текстуры в оттенках холодного серого, создающие ощущение тумана над утренним озером",
            "Перистые облака в светло-серых тонах, тонкие и полупрозрачные, с мягкими размытыми краями и плавными переходами",
            "Легкая облачная дымка в нейтральных оттенках белого и серого, создающая воздушную текстуру без резких контрастов",
            "Нежные кучевые облака на светло-сером фоне, размытые и объемные, словно пушистые подушки в утреннем тумане",
            "Мягкие высокие облака с размытыми очертаниями в пастельных серых тонах, создающие ощущение легкости и простора",
            "Облачные массы в монохромной гамме, напоминающие дымчатые завихрения с плавными переходами и нежными границами",
            "Рассеянные перистые облака в бледно-бежевых оттенках, создающие нейтральный фон с легкими текстурными элементами",
            "Воздушные слоистые облака в оттенках слоновой кости, мягкие и размытые, словно укрытые утренней дымкой",
            "Высокие перистые облака в холодных серо-белых тонах, тонкие и изящные, с нежными размытыми краями",
            "Светлые кучевые облака с мягкими контурами в серебристо-серых оттенках, создающие спокойный нейтральный фон",
            "Легкая облачная вуаль в пастельных серых тонах, полупрозрачная и нежная, с едва заметными переходами света",
            "Перисто-слоистые облака в приглушенных бежевых тонах, создающие размытую текстуру с мягкими световыми акцентами",
            "Дымчатые облачные формации в светло-серой гамме, с плавными переходами и воздушной, легкой текстурой",
            "Нежные кучево-дождевые облака в светлых монохромных оттенках, мягкие и объемные, с размытыми границами",
            "Облачное небо раннего утра в нейтральных серых тонах, с мягкими переходами и легкой дымчатой текстурой",
            "Высокие слоистые облака в оттенках светлого жемчуга, создающие воздушный, элегантный фон без ярких контрастов",
            "Перистые и перисто-кучевые облака в светло-кремовых тонах, с мягкими очертаниями и приглушенными переходами",
            "Легкие облачные массы в бледно-серых оттенках, напоминающие нежную вату с размытыми краями и мягкой текстурой",
            "Дымчатые слоистые облака в серебристо-белых тонах, переплетенные и воздушные, с плавными тональными переходами",
            "Нежные перисто-слоистые облака в монохромной гамме, создающие легкую вуаль с едва заметными градиентами серого",
            "Высокие кучевые облака в оттенках бледного песка, мягкие и объемные, с размытыми контурами и нежными тенями",
            "Облачная дымка в светлых нейтральных тонах, создающая воздушную текстуру с мягкими, едва уловимыми переходами",
            "Перистые облака в холодных серых оттенках, тонкие и изящные, словно легкие мазки акварели на сером небе",
            "Слоистые облака в мягких кремово-серых тонах, с плавными переходами и нежными, размытыми границами",
            "Легкие кучевые облака в пастельных оттенках слоновой кости, создающие воздушный фон с мягкими световыми акцентами",
            "Облачное покрывало в приглушенных серебристых тонах, нежное и полупрозрачное, с едва различимыми тональными переходами",
            "Легкие перистые облака в серебристо-серых оттенках, растянутые по всему небу, создающие нежную вуаль с мягкими переходами и размытыми границами без ярких акцентов",
            "Воздушные кучевые облака в светло-бежевых тонах с мягкими очертаниями и плавными переходами, словно пушистые подушки в золотистой утренней дымке раннего рассвета",
            "Нежные слоистые облака в оттенках слоновой кости и светло-серого, с мягкими границами и легкой текстурой, напоминающие тонкие слои шелковой ткани на фоне жемчужного неба",
            "Полупрозрачные перистые облака, растянутые тонкими нитями по светло-серому небу, с нежными размытыми краями и едва заметными переходами между оттенками холодного белого",
            "Высокие кучево-дождевые облака в мягких монохромных тонах с размытыми контурами и нежными градиентами серого, создающие воздушный объемный фон без контрастных элементов",
            "Легкая облачная дымка в нейтральных серо-белых оттенках, окутывающая небо подобно тончайшей вуали, с плавными, едва заметными переходами и мягкой воздушной текстурой",
            "Нежные слоисто-кучевые облака в пастельных оттенках бежевого и серого, с размытыми краями и мягкими волнистыми формами, напоминающие морскую пену на песчаном пляже",
            "Перисто-слоистые облака в холодных серебристых тонах, тонкие и изящные, создающие легкую ажурную текстуру с едва уловимыми тональными переходами и мягкими акцентами",
            "Дымчатые облачные формации в мягких оттенках светло-серого с тонкими градиентами и нежными переходами, словно утренний туман, застывший высоко в небе",
            "Воздушные кучевые облака в светлых кремовых тонах с мягкими, размытыми очертаниями и легкими тенями, образующие нежный, спокойный фон без ярких световых контрастов",
            "Высокие перистые облака в монохромных серебристо-белых оттенках, тонкие и прозрачные, с нежными размытыми краями и едва заметными переходами между светлыми и темными участками",
            "Облачная вуаль в приглушенных бежево-серых тонах, легкая и воздушная, с мягкими текстурными переходами и нежными световыми акцентами, создающая спокойный, гармоничный фон",
            "Слоистые облака в нейтральных оттенках жемчужно-серого, с плавными переходами и мягкими границами, напоминающие тонкие слои мраморной бумаги с нежной, элегантной текстурой",
            "Перисто-кучевые облака в светлых пастельных тонах с размытыми контурами и легкими тенями, словно мягкие хлопковые шарики на фоне бледно-серого утреннего неба",
            "Нежные облачные массы в оттенках светлого графита и белого, с мягкими тональными переходами и размытыми границами, создающие воздушный, лаконичный фон без резких контрастов",
            "Легкие кучевые облака в приглушенных оттенках слоновой кости, с мягкими, пушистыми краями и нежными тенями, похожие на взбитые сливки с легким золотистым отливом",
            "Высокие перистые облака в бледно-серебристых тонах, тонкие и изящные, напоминающие легкие мазки кисти на светло-сером фоне с плавными, едва заметными переходами",
            "Слоисто-дождевые облака в мягких оттенках холодного серого, с размытыми нижними краями и легкой туманной дымкой, создающие ощущение легкой меланхолии и спокойствия",
            "Облачное покрывало в нежных кремово-серых тонах с мягкими световыми акцентами и плавными тональными переходами, словно тонкая вуаль над спящим зимним пейзажем",
            "Перисто-слоистые облака в светлых монохромных оттенках с легкими волнистыми формами и размытыми краями, напоминающие прозрачные занавески, колышущиеся на легком ветру",
            "Высокие кучевые облака в приглушенных пастельных тонах с мягкими объемными формами и нежными тенями, создающие воздушный, легкий фон без явных цветовых акцентов",
            "Дымчатые облачные формации в светло-серых оттенках с плавными переходами и мягкими контурами, напоминающие легкие клубы дыма, медленно растворяющиеся в прохладном воздухе",
            "Нежные слоистые облака в бледно-бежевых тонах с едва заметными переходами и мягкими границами, создающие спокойный, элегантный фон с легкой текстурой и воздушностью",
            "Перистые облака в холодных серебристо-белых оттенках, тонкие и изящные, словно нежные штрихи белого карандаша на светло-сером фоне с мягкими, размытыми краями",
            "Облачная дымка в нейтральных оттенках светлого льна, полупрозрачная и воздушная, с мягкими текстурными переходами и нежными световыми эффектами утреннего рассеянного солнца",
            "Высокие кучево-дождевые облака в мягких монохромных тонах с размытыми контурами и легкими тенями, напоминающие пушистые горы на фоне жемчужно-серого неба",
            "Слоисто-кучевые облака в приглушенных оттенках светлого песка с мягкими волнистыми формами и плавными переходами, создающие нейтральный, гармоничный фон с легкой текстурой",
            "Легкие перистые облака в бледно-серебристых тонах, тонкие и прозрачные, с нежными размытыми краями и едва заметными переходами между светлыми и более темными участками",
            "Облачный покров в нежных оттенках холодного серого с мягкими тональными переходами и размытыми границами, напоминающий тонкую вуаль над спокойным зимним пейзажем",
            "Воздушные кучевые облака в светлых кремовых тонах с мягкими, пушистыми краями и легкими тенями, словно взбитые сливки на фоне нежно-бежевого неба утреннего рассвета",
            "Легкие клубы дыма в серебристо-серых оттенках, мягко растворяющиеся в воздухе с плавными переходами и размытыми границами, создающие нежную воздушную текстуру без резких контрастов",
            "Тонкие струйки белого дыма на светло-сером фоне, изящно переплетающиеся между собой, с мягкими размытыми контурами и нежными градиентами, словно танцующие в пространстве",
            "Мягкий снегопад в монохромных оттенках с размытыми, едва различимыми снежинками, создающий атмосферу зимнего спокойствия с нежными переходами света и воздушной текстурой",
            "Дымчатые завихрения в нейтральных светло-серых тонах, плавно растворяющиеся в пространстве, с мягкими формами и нежными переходами, напоминающие медитативные движения в воздухе",
            "Легкий снежный вихрь в пастельных белых и серебристых оттенках, с размытыми, мягкими контурами и плавными переходами, создающий ощущение зимней нежности и легкости",
            "Воздушные дымовые полосы в оттенках светлого графита и белого, с плавными изгибами и мягкими краями, словно легкие мазки кисти на нейтральном фоне",
            "Нежные снежные хлопья, медленно опускающиеся на светло-сером фоне, с размытыми очертаниями и мягкими переходами, создающие атмосферу тихого зимнего вечера без ярких акцентов",
            "Полупрозрачные клубы дыма в светло-бежевых тонах, мягко расползающиеся по пространству с нежными градиентами и воздушной текстурой, напоминающие утренний туман над теплой землей",
            "Снежная вуаль в холодных серебристо-белых оттенках, легкая и воздушная, с едва заметными переходами и мягкими светлыми акцентами, словно зимняя дымка над спящим полем",
            "Изящные дымовые спирали в нейтральных серых тонах, с мягкими изгибами и плавными переходами от светлого к темному, создающие ощущение легкости и воздушного танца",
            "Мягкий снежный покров в оттенках слоновой кости и светло-серого, с нежными тенями и размытыми границами, напоминающий пушистое одеяло с легкими складками и текстурными переходами",
            "Тонкие дымовые шлейфы в приглушенных пепельных тонах, с мягкими очертаниями и плавными переходами, словно элегантные линии графики на светлом холсте с легкими оттенками",
            "Кружащиеся снежинки в высоком ключе, с размытыми контурами и нежным свечением, создающие воздушную, легкую текстуру с мягкими переходами и приглушенным белым сиянием",
            "Воздушные дымчатые облака в светлых пастельных тонах, медленно растворяющиеся в пространстве с нежными переходами и мягкими границами, создающие ощущение умиротворения и легкости",
            "Снежная буря в мягких серебристо-белых оттенках, с размытыми движениями и нежными вихрями, напоминающая легкую вуаль, колышущуюся на холодном зимнем ветру",
            "Минималистичные дымовые формы в светло-пепельных тонах, с плавными линиями и мягкими переходами, создающие воздушную, лаконичную композицию без резких контрастов и ярких акцентов",
            "Нежный снежный флер в монохромных белых и серых оттенках, с мягкими, размытыми границами и легкими текстурными переходами, словно зимний сон в пастельных тонах",
            "Тающие снежинки на светлом фоне, полупрозрачные и нежные, с размытыми краями и мягкими бликами, создающие ощущение хрупкости и таяния без четких контуров",
            "Легкие дымные полосы в светло-бежевых тонах, плавно перетекающие друг в друга, с мягкими размытыми контурами и нежными градиентами, напоминающие шелковые ленты в воздухе",
            "Снежный вихрь в светлых нейтральных оттенках, с мягкими спиралевидными движениями и плавными переходами, создающий ощущение зимней легкости и воздушного танца ледяных кристаллов",
            "Тонкие слои дыма в пастельных серых тонах, наслаивающиеся друг на друга с едва заметными переходами и мягкими границами, создающие многомерную, но деликатную текстуру",
            "Мягкий снегопад сквозь морозную дымку, в холодных серебристых оттенках с нежными размытыми формами и легкими переливами, напоминающий зимний сон в высоком ключе",
            "Воздушные дымовые кольца в светло-серых тонах, плавно растворяющиеся в пространстве с мягкими градиентами и нежными переходами, создающие медитативный, спокойный визуальный ритм",
            "Снежные хлопья, сливающиеся с зимним небом в единую нежную текстуру светлых серебристо-белых оттенков, с размытыми границами и мягкими, едва заметными световыми акцентами",
            "Легкие клубы пара в нейтральных кремово-серых тонах, мягко рассеивающиеся в воздухе с плавными переходами и нежными формами, напоминающие утреннее дыхание в морозный день",
            "Зимняя метель в высоком ключе, с размытыми снежными вихрями и мягкими светлыми переходами, создающая ощущение легкости и воздушности без четких контуров и резких линий",
            "Элегантные дымовые завихрения в светлых пепельных тонах, с плавными изгибами и мягкими переходами, словно невесомые воздушные скульптуры в пространстве нейтральных оттенков",
            "Нежные снежные вуали в бледно-голубых и серебристых тонах, с размытыми краями и мягкими переливами, создающие ощущение зимней свежести и легкости без ярких акцентов",
            "Тающий снег в пастельных светло-серых оттенках, с нежными переходами и мягкими бликами, создающий минималистичную текстуру с легкими водянистыми разводами и воздушными переливами",
            "Тонкие струи дыма на светлом фоне, изящно переплетающиеся и создающие воздушную композицию с мягкими формами и нежными градиентами от белого к светло-серому без резких контрастов",

       ]


        from generator import MultiAccountFusionBrainAPI

        # Инициализация генераторов
        self.background_generator = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir='backgrounds',
            positive_prompts=bg_prompts,
            max_images=MAX_IMAGES
        )

        self.overlay_generator = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir='overlay',
            positive_prompts=ol_prompts,
            max_images=MAX_IMAGES
        )

        # Запуск генераторов
        self.background_generator.run_generation()
        self.overlay_generator.run_generation()

        # Создание мониторинг-задач
        bg_monitor = asyncio.create_task(self.background_generator.monitor_activity())
        ol_monitor = asyncio.create_task(self.overlay_generator.monitor_activity())

    async def handle_get_image(self, request):
        global file_usage_counter, image_usage_tracking, image_last_used

        try:
            token = request.query.get("token")
            if not token:
                return web.json_response({"status": "error", "message": "Token is required"}, status=400)

            backgrounds_dir = "backgrounds"
            if not os.path.exists(backgrounds_dir):
                return web.json_response({"status": "error", "message": "Backgrounds directory not found"}, status=404)

            async with file_lock:
                files = os.listdir(backgrounds_dir)
                if not files:
                    return web.json_response({"status": "error", "message": "Нет доступных изображений"}, status=400)

                current_time = time.time()
                available_files = [
                    f for f in files
                    if (token not in image_usage_tracking[f] and
                        (f not in image_last_used or
                         current_time - image_last_used[f] > REUSE_TIMEOUT))
                ]

                if not available_files:
                    return web.json_response(
                        {"status": "error", "message": "Все доступные изображения уже были использованы"},
                        status=429
                    )

                file_name = random.choice(available_files)
                file_path = os.path.join(backgrounds_dir, file_name)

                image_usage_tracking[file_name].add(token)
                image_last_used[file_name] = current_time
                file_usage_counter[file_name] = file_usage_counter.get(file_name, 0) + 1

            async with aiofiles.open(file_path, "rb") as f:
                file_data = await f.read()

            response = web.Response(body=file_data)
            response.content_type = mimetypes.guess_type(file_name)[0] or "application/octet-stream"
            response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'

            return response

        except Exception as e:
            error_message = f"[WEB SERVER] Ошибка в /get_image: {str(e)}"
            print(error_message)
            return web.json_response({"status": "error", "message": "Внутренняя ошибка сервера"}, status=500)

        finally:
            async with file_lock:
                if file_name in file_usage_counter:
                    file_usage_counter[file_name] -= 1

                if file_usage_counter.get(file_name) == 0:
                    try:
                        os.remove(file_path)
                        file_usage_counter.pop(file_name, None)
                        image_usage_tracking.pop(file_name, None)
                        image_last_used.pop(file_name, None)
                        print(f"[WEB SERVER] Файл {file_name} успешно удалён.")
                    except FileNotFoundError:
                        print(f"[WEB SERVER] Файл {file_name} уже удалён.")
                    except Exception as e:
                        print(f"[WEB SERVER] Ошибка при удалении файла {file_name}: {str(e)}")

    async def handle_get_overlay(self, request):
        global file_usage_counter, image_usage_tracking, image_last_used

        try:
            token = request.query.get("token")
            if not token:
                return web.json_response({"status": "error", "message": "Token is required"}, status=400)

            overlay_dir = "overlay"
            if not os.path.exists(overlay_dir):
                return web.json_response({"status": "error", "message": "Overlay directory not found"}, status=404)

            async with file_lock:
                files = os.listdir(overlay_dir)
                if not files:
                    return web.json_response({"status": "error", "message": "Нет доступных изображений"}, status=400)

                current_time = time.time()
                available_files = [
                    f for f in files
                    if (token not in image_usage_tracking[f] and
                        (f not in image_last_used or
                         current_time - image_last_used[f] > REUSE_TIMEOUT))
                ]

                if not available_files:
                    return web.json_response(
                        {"status": "error", "message": "Все доступные изображения уже были использованы"},
                        status=429
                    )

                file_name = random.choice(available_files)
                file_path = os.path.join(overlay_dir, file_name)

                image_usage_tracking[file_name].add(token)
                image_last_used[file_name] = current_time
                file_usage_counter[file_name] = file_usage_counter.get(file_name, 0) + 1

            async with aiofiles.open(file_path, "rb") as f:
                file_data = await f.read()

            response = web.Response(body=file_data)
            response.content_type = mimetypes.guess_type(file_name)[0] or "application/octet-stream"
            response.headers["Content-Disposition"] = f'attachment; filename="{file_name}"'

            return response

        except Exception as e:
            error_message = f"[WEB SERVER] Ошибка в /get_overlay: {str(e)}"
            print(error_message)
            return web.json_response({"status": "error", "message": "Внутренняя ошибка сервера"}, status=500)

        finally:
            async with file_lock:
                if file_name in file_usage_counter:
                    file_usage_counter[file_name] -= 1

                if file_usage_counter.get(file_name) == 0:
                    try:
                        os.remove(file_path)
                        file_usage_counter.pop(file_name, None)
                        image_usage_tracking.pop(file_name, None)
                        image_last_used.pop(file_name, None)
                        print(f"[WEB SERVER] Файл {file_name} успешно удалён.")
                    except FileNotFoundError:
                        print(f"[WEB SERVER] Файл {file_name} уже удалён.")
                    except Exception as e:
                        print(f"[WEB SERVER] Ошибка при удалении файла {file_name}: {str(e)}")


    async def run_server(self):
        app = web.Application()
        
        # Добавляем маршруты
        app.router.add_get('/get_image', self.handle_get_image)
        app.router.add_get('/get_overlay', self.handle_get_overlay)

        # Запускаем сервер
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, '0.0.0.0', WEBSERVER_PORT)

        while True:
            try:
                await site.start()
                print(f"[IMAGE SERVER] Server started at http://{SERVER_IP}:{WEBSERVER_PORT}")
                
                # Бесконечный цикл для поддержания сервера активным
                while self.is_running:
                    await asyncio.sleep(1)
                    
            except Exception as e:
                print(f"[IMAGE SERVER] Server error: {e}")
                print("[IMAGE SERVER] Restarting in 5 seconds...")
                await asyncio.sleep(5)
            finally:
                await runner.cleanup()

async def main():
    server = ImageServer()
    await server.initialize_generators()
    await server.run_server()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n[IMAGE SERVER] Shutting down...")
    except Exception as e:
        print(f"[IMAGE SERVER] Fatal error: {e}")
        traceback.print_exc() 