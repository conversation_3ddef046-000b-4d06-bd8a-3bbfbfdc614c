<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MagicVideo - Программа для уникализации видео</title>
    <link rel="icon" href="/landing/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="/landing/tailwind.min.css">
    <script src="/landing/lucide.js"></script>
    <style>
        :root {
            --primary: #4169E1;
            --primary-light: #6495ED;
            --dark: #111827;
            --darker: #0A0F1C;
            --light: #F9FAFB;
            --gray: #374151;
            --light-gray: #6B7280;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, Oxygen, <PERSON>buntu, Can<PERSON>ell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            background-color: var(--darker);
            color: var(--light);
            line-height: 1.6;
        }

        a {
            color: var(--primary-light);
            text-decoration: none;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            padding: 20px 0;
            position: sticky;
            top: 0;
            background-color: rgba(10, 15, 28, 0.9);
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 22px;
            font-weight: 700;
            color: var(--light);
        }

        .logo img {
            height: 40px;
            width: auto;
        }

        .logo-icon {
            color: var(--primary);
            font-size: 28px;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        nav a {
            color: var(--light);
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: var(--primary);
        }

        .btn {
            display: inline-block;
            background-color: var(--primary);
            color: white;
            padding: 10px 24px;
            border-radius: 8px;
            font-weight: 500;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background-color: var(--primary-light);
        }

        .btn-secondary {
            background-color: transparent;
            border: 1px solid var(--primary);
            color: var(--primary);
        }

        .btn-secondary:hover {
            background-color: rgba(65, 105, 225, 0.1);
        }

        /* Hero Section */
        .hero {
            padding: 100px 0 50px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 40px;
        }

        .hero-content {
            max-width: 600px;
        }

        h1 {
            font-size: 52px;
            line-height: 1.2;
            margin-bottom: 20px;
            font-weight: 800;
        }

        h1 span {
            background: linear-gradient(90deg, var(--primary), #8A2BE2);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .hero p {
            font-size: 18px;
            color: var(--light-gray);
            margin-bottom: 30px;
        }

        .hero-btns {
            display: flex;
            gap: 16px;
        }

        .hero-image {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .app-screenshot {
            width: 100%;
            max-width: 600px;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            transition: transform 0.3s;
        }

        .app-screenshot:hover {
            transform: translateY(-10px);
        }

        /* Features Section */
        .features {
            padding: 100px 0;
            background-color: var(--dark);
        }

        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }

        h2 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 16px;
        }

        .section-header p {
            font-size: 18px;
            color: var(--light-gray);
            max-width: 700px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            background-color: var(--darker);
            border-radius: 12px;
            padding: 30px;
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background-color: rgba(65, 105, 225, 0.1);
            color: var(--primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .feature-card h3 {
            font-size: 22px;
            margin-bottom: 12px;
        }

        .feature-card p {
            color: var(--light-gray);
        }

        /* How It Works */
        .how-it-works {
            padding: 100px 0;
        }

        .steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }

        .step {
            text-align: center;
            padding: 20px;
        }

        .step-number {
            width: 50px;
            height: 50px;
            background-color: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 700;
            margin: 0 auto 20px;
        }

        .step h3 {
            margin: 15px 0;
        }

        .step p {
            color: var(--light-gray);
        }

        /* Pricing */
        .pricing {
            padding: 100px 0;
            background-color: var(--dark);
        }

        .pricing-cards {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            margin-top: 60px;
        }

        .pricing-card {
            background-color: var(--darker);
            border-radius: 12px;
            padding: 30px;
            width: 100%;
            max-width: 300px;
            transition: transform 0.3s;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
        }

        .pricing-card.featured {
            border: 2px solid var(--primary);
            position: relative;
        }

        .featured-badge {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--primary);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .pricing-card h3 {
            font-size: 24px;
            margin-bottom: 20px;
        }

        .price {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .price span {
            font-size: 16px;
            font-weight: 400;
            color: var(--light-gray);
        }

        .pricing-features {
            margin: 30px 0;
            list-style: none;
        }

        .pricing-features li {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .pricing-features li::before {
            content: "✓";
            color: var(--primary);
            margin-right: 10px;
            font-weight: bold;
        }

        /* Testimonials */
        .testimonials {
            padding: 100px 0;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .testimonial-card {
            background-color: var(--dark);
            border-radius: 12px;
            padding: 30px;
            transition: transform 0.3s;
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
        }

        .testimonial-content {
            margin-bottom: 20px;
            font-style: italic;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: var(--gray);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: var(--light);
        }

        .author-info h4 {
            font-size: 18px;
        }

        .author-info p {
            font-size: 14px;
            color: var(--light-gray);
        }

        /* FAQ */
        .faq {
            padding: 100px 0;
            background-color: var(--dark);
        }

        .faq-container {
            max-width: 800px;
            margin: 60px auto 0;
        }

        .faq-item {
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .faq-question {
            background-color: var(--darker);
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 500;
        }

        .faq-answer {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out, padding 0.3s ease;
            color: var(--light-gray);
        }

        .faq-item.active .faq-answer {
            padding: 20px;
            max-height: 500px;
        }

        /* CTA */
        .cta {
            padding: 100px 0;
            text-align: center;
        }

        .cta-content {
            max-width: 700px;
            margin: 0 auto;
        }

        .cta h2 {
            font-size: 42px;
            margin-bottom: 20px;
        }

        .cta p {
            font-size: 18px;
            color: var(--light-gray);
            margin-bottom: 30px;
        }

        /* Footer */
        footer {
            background-color: var(--dark);
            padding: 70px 0 30px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 50px;
        }

        .footer-logo {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .footer-logo img {
            height: 40px;
            width: auto;
        }

        .footer-about p {
            color: var(--light-gray);
            margin-bottom: 20px;
        }

        .footer-links h4 {
            font-size: 18px;
            margin-bottom: 20px;
        }

        .footer-links ul {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: var(--light-gray);
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--light-gray);
            font-size: 14px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero {
                flex-direction: column;
                text-align: center;
                padding: 50px 0;
            }

            .hero-btns {
                justify-content: center;
            }

            h1 {
                font-size: 36px;
            }

            .section-header {
                margin-bottom: 40px;
            }

            nav {
                display: none;
            }

            .features, .how-it-works, .pricing, .testimonials, .faq, .cta {
                padding: 70px 0;
            }

            .pricing-cards {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container header-content">
            <div class="logo">
                <img src="/landing/logo.png" alt="MagicUniq Logo">
                <span>MagicUniq</span>
            </div>
            <nav>
                <ul>
                    <li><a href="#features">Функции</a></li>
                    <li><a href="#how-it-works">Как это работает</a></li>
                    <li><a href="#pricing">Цены</a></li>
                    <li><a href="#faq">FAQ</a></li>
                    <li><a href="https://magic-uniq.gitbook.io/magic-uniq" target="_blank">Документация</a></li>
                </ul>
            </nav>
            <a href="/landing/installer.exe" class="btn">Скачать</a>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero container">
        <div class="hero-content">
            <h1>Сделайте ваши видео <span>уникальными</span> для алгоритмов соцсетей</h1>
            <p>MagicUniq помогает обходить алгоритмы социальных сетей, деликатно модифицируя ваши видео с сохранением качества. Наша передовая технология уникализации гарантирует, что ваш контент получит заслуженный охват.</p>
            <div class="hero-btns">
                <a href="#download" class="btn">Скачать</a>
                <a href="#how-it-works" class="btn btn-secondary">Узнать больше</a>
            </div>
        </div>
        <div class="hero-image">
            <img src="/landing/gui.png" alt="MagicUniq Interface" class="app-screenshot">
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Мощные функции</h2>
                <p>MagicUniq предлагает полный набор инструментов для уникализации ваших видео и максимального охвата в социальных сетях</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i data-lucide="repeat"></i>
                    </div>
                    <h3>Параметры движения</h3>
                    <p>Добавляйте тонкие эффекты движения с настраиваемой амплитудой, скоростью и сложностью, чтобы сделать видео уникальным без потери качества.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i data-lucide="scan"></i>
                    </div>
                    <h3>Продвинутое масштабирование</h3>
                    <p>Изменяйте размеры видео с точным контролем для сохранения содержимого при создании алгоритмически уникальных видео.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i data-lucide="palette"></i>
                    </div>
                    <h3>Эффекты цвета и света</h3>
                    <p>Регулируйте насыщенность, прозрачность, гамму, яркость и контраст с минимальным визуальным влиянием для создания алгоритмически уникальных видео.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i data-lucide="sparkles"></i>
                    </div>
                    <h3>Специальные эффекты</h3>
                    <p>Применяйте тонкие эффекты снега, линий и границ, которые почти не видны зрителям, но делают ваш контент уникальным для алгоритмов.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i data-lucide="volume-2"></i>
                    </div>
                    <h3>Настройки звука</h3>
                    <p>Вносите небольшие изменения в аудиодорожки, которые обходят алгоритмы обнаружения, сохраняя исходное качество звука.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i data-lucide="settings"></i>
                    </div>
                    <h3>Система конфигурации</h3>
                    <p>Сохраняйте и загружайте ваши любимые комбинации параметров для быстрой и стабильной обработки видео.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section id="how-it-works" class="how-it-works container">
        <div class="section-header">
            <h2>Как работает MagicUniq</h2>
            <p>Наш оптимизированный процесс позволяет легко уникализировать видео всего за несколько шагов</p>
        </div>
        <div class="steps">
            <div class="step">
                <div class="step-number">1</div>
                <h3>Загрузите видео</h3>
                <p>Просто перетащите видео в MagicUniq или используйте файловый селектор для выбора видео с вашего устройства.</p>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <h3>Настройте параметры</h3>
                <p>Настройте параметры уникализации или выберите из наших пресетов, разработанных для конкретных социальных платформ.</p>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <h3>Обработайте видео</h3>
                <p>Наши продвинутые алгоритмы делают своё волшебство, применяя тонкие изменения, которые делают ваше видео уникальным.</p>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <h3>Сохраните и поделитесь</h3>
                <p>Загрузите обработанное видео и опубликуйте его в ваших любимых социальных сетях для увеличения охвата.</p>
            </div>
        </div>
    </section>

    <!-- Pricing -->
    <section id="pricing" class="pricing">
        <div class="container">
            <div class="section-header">
                <h2>Простые и прозрачные цены</h2>
                <p>Выберите план, который соответствует вашим потребностям</p>
            </div>
            <div class="pricing-cards">
                <div class="pricing-card">
                    <h3>Пробный период</h3>
                    <div class="price">0₽ <span>/1 день</span></div>
                    <p>Попробуйте все возможности программы бесплатно</p>
                    <ul class="pricing-features">
                        <li>Полный доступ ко всем функциям</li>
                        <li>Неограниченная обработка видео</li>
                        <li>1 день использования</li>
                        <li>Поддержка в Telegram</li>
                    </ul>
                    <a href="https://t.me/MagicUniqBot" target="_blank" class="btn" style="width: 100%; text-align: center;">Получить в боте</a>
                </div>
                <div class="pricing-card">
                    <h3>Неделя</h3>
                    <div class="price">10$ <span>/неделя</span></div>
                    <p>Идеально для краткосрочных проектов</p>
                    <ul class="pricing-features">
                        <li>Полный доступ ко всем функциям</li>
                        <li>Неограниченная обработка видео</li>
                        <li>До 5 пользовательских конфигураций</li>
                        <li>Поддержка в Telegram</li>
                    </ul>
                    <a href="https://t.me/MagicUniqBot" target="_blank" class="btn" style="width: 100%; text-align: center;">Купить в боте</a>
                </div>
                <div class="pricing-card featured">
                    <div class="featured-badge">Популярный</div>
                    <h3>Месяц</h3>
                    <div class="price">30$ <span>/месяц</span></div>
                    <p>Лучшая ценность для постоянных создателей контента</p>
                    <ul class="pricing-features">
                        <li>Неограниченное количество пользовательских конфигураций</li>
                        <li>Приоритетная поддержка</li>
                        <li>Возможность пакетной обработки</li>
                    </ul>
                    <a href="https://t.me/MagicUniqBot" target="_blank" class="btn" style="width: 100%; text-align: center;">Купить в боте</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="testimonials container">
        <div class="section-header">
            <h2>Отзывы наших пользователей</h2>
            <p>Тысячи создателей контента доверяют MagicUniq для увеличения охвата в социальных сетях</p>
        </div>
        <div class="testimonials-grid">
            <div class="testimonial-card">
                <div class="testimonial-content">
                    "После внедрения MagicUniq мы смогли увеличить количество успешных публикаций в TikTok и Instagram на 450%. Теперь мы заливаем по 200+ видео в день без банов и ограничений. Не представляю, как мы раньше работали без этого инструмента."
                </div>
                <div class="testimonial-author">
                    <div class="author-avatar">A</div>
                    <div class="author-info">
                        <h4>Анонимно</h4>
                        <p>OnlyFans агентство</p>
                    </div>
                </div>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-content">
                    "Когда начал продвигать гемблинг через YouTube Shorts, столкнулся с постоянными блокировками. MagicUniq полностью решил эту проблему — за последний месяц ни одно видео не было заблокировано, а конверсия выросла в 3 раза."
                </div>
                <div class="testimonial-author">
                    <div class="author-avatar">Т</div>
                    <div class="author-info">
                        <h4>Анонимно</h4>
                        <p>Арбитражник, вертикаль Gambling</p>
                    </div>
                </div>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-content">
                    "Лью трафик на Telegram-каналы через видео в различных соц сетях (Snapchat, YouTube Shorts, TikTok) уже 3 года. С MagicUniq статистика подписок выросла на 280%, потому что видео больше не блокируются на этапе модерации. Стоимость подписки снизилась с $2.8 до $0.9."
                </div>
                <div class="testimonial-author">
                    <div class="author-avatar">П</div>
                    <div class="author-info">
                        <h4>Анонимно</h4>
                        <p>Траффер, TG-подписки</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ -->
    <section id="faq" class="faq">
        <div class="container">
            <div class="section-header">
                <h2>Часто задаваемые вопросы</h2>
                <p>Всё, что вам нужно знать о MagicUniq</p>
            </div>
            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        Будут ли изменения заметны для зрителей?
                        <i data-lucide="plus"></i>
                    </div>
                    <div class="faq-answer">
                        <p>MagicUniq позволяет гибко настраивать интенсивность изменений — от практически незаметных до более серьезных модификаций для обхода самых строгих алгоритмов. При этом качество видео и аудио остается на высоком уровне в любом случае. Вы сами выбираете оптимальный баланс между степенью уникализации и визуальными изменениями.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        Работает ли это на всех платформах социальных сетей?
                        <i data-lucide="plus"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Да, MagicUniq эффективен на всех основных платформах социальных сетей, включая YouTube, Instagram, TikTok, Facebook и Twitter. Мы регулярно обновляем наши алгоритмы, чтобы адаптироваться к изменениям в системах обнаружения платформ.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        Какие форматы видео поддерживаются?
                        <i data-lucide="plus"></i>
                    </div>
                    <div class="faq-answer">
                        <p>MagicUniq поддерживает все распространенные форматы видео, включая MP4, MOV, AVI, WMV и другие. По умолчанию обработанные видео экспортируются в том же формате, что и исходные, но вы также можете выбрать предпочтительный формат вывода.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        Снизит ли обработка качество моего видео?
                        <i data-lucide="plus"></i>
                    </div>
                    <div class="faq-answer">
                        <p>MagicUniq разработан для сохранения качества видео при внесении алгоритмических изменений. Любое снижение качества минимально и обычно не заметно для зрителей. Вы также можете настроить уровень сохранения качества в настройках.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">
                        Где я могу получить лицензию?
                        <i data-lucide="plus"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Вы можете приобрести лицензию через наш Telegram бот <a href="https://t.me/MagicUniqBot" target="_blank">@MagicUniqBot</a>. Там же доступно получение бесплатной тестовой лицензии на 1 день, чтобы вы могли оценить все возможности программы.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA -->
    <section id="download" class="cta container">
        <div class="cta-content">
            <h2>Нужно заливать видео без блокировок?</h2>
            <p>Скачивайте MagicUniq и забудьте о банах из-за дубликатов контента. Увеличивайте конверсию, снижайте стоимость лидов и обходите самые жесткие алгоритмы модерации.</p>
            <div class="flex flex-col md:flex-row gap-4 justify-center mt-8" style="max-width: 600px; margin: 0 auto;">
                <a href="/landing/installer.exe" class="btn flex items-center justify-center gap-2" style="min-width: 220px; position: relative; padding-left: 46px;">
                    <i data-lucide="download" style="position: absolute; left: 16px; top: 50%; transform: translateY(-50%);"></i>
                    Скачать MagicUniq
                </a>
                <a href="https://t.me/MagicUniqBot" target="_blank" class="btn btn-secondary flex items-center justify-center gap-2" style="min-width: 220px; position: relative; padding-left: 46px;">
                    <i data-lucide="send" style="position: absolute; left: 16px; top: 50%; transform: translateY(-50%);"></i>
                    Получить лицензию в Telegram
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-about">
                    <div class="footer-logo">
                        <img src="/landing/logo.png" alt="MagicUniq Logo">
                        <span>MagicUniq</span>
                    </div>
                    <p>Идеальное решение для создания уникальных видео, которые обходят алгоритмы социальных сетей и максимизируют ваш охват.</p>
                </div>
                <div class="footer-links">
                    <h4>Быстрые ссылки</h4>
                    <ul>
                        <li><a href="#features">Функции</a></li>
                        <li><a href="#how-it-works">Как это работает</a></li>
                        <li><a href="#pricing">Цены</a></li>
                        <li><a href="#faq">FAQ</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h4>Ресурсы</h4>
                    <ul>
                        <li><a href="https://magic-uniq.gitbook.io/magic-uniq" target="_blank">Документация</a></li>
                        <li><a href="https://t.me/magic_uniq" target="_blank">Блог</a></li>
                        <li><a href="https://t.me/code_reverse" target="_blank">Поддержка</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h4>Правовая информация</h4>
                    <ul>
                        <li><a href="#">Условия использования</a></li>
                        <li><a href="#">Политика конфиденциальности</a></li>
                        <li><a href="#">Политика возврата</a></li>
                        <li><a href="#">Лицензионное соглашение</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 MagicUniq. Все права защищены.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Инициализация Lucide иконок с правильными настройками для всех иконок
        document.addEventListener('DOMContentLoaded', function() {
            lucide.createIcons({
                attrs: {
                    stroke: 'currentColor',
                    'stroke-width': '2',
                    'stroke-linecap': 'round',
                    'stroke-linejoin': 'round',
                    fill: 'none'
                }
            });

            // FAQ Accordion
            document.querySelectorAll('.faq-question').forEach(question => {
                question.addEventListener('click', () => {
                    const item = question.parentNode;
                    const isActive = item.classList.contains('active');

                    // Close all items
                    document.querySelectorAll('.faq-item').forEach(faqItem => {
                        faqItem.classList.remove('active');
                        const icon = faqItem.querySelector('.faq-question i');
                        if (icon) {
                            icon.setAttribute('data-lucide', 'plus');
                            lucide.replace(icon);
                        }
                    });

                    // If the clicked item wasn't active, open it
                    if (!isActive) {
                        item.classList.add('active');
                        const icon = question.querySelector('i');
                        if (icon) {
                            icon.setAttribute('data-lucide', 'minus');
                            lucide.replace(icon);
                        }
                    }
                });
            });
        });
        
        // FAQ Accordion
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', () => {
                const item = question.parentNode;
                const isActive = item.classList.contains('active');

                // Close all items
                document.querySelectorAll('.faq-item').forEach(faqItem => {
                    faqItem.classList.remove('active');
                    const icon = faqItem.querySelector('.faq-question i');
                    lucide.replace(icon, { name: 'plus' });
                });

                // If the clicked item wasn't active, open it
                if (!isActive) {
                    item.classList.add('active');
                    const icon = question.querySelector('i');
                    lucide.replace(icon, { name: 'minus' });
                }
            });
        });

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>
</html>