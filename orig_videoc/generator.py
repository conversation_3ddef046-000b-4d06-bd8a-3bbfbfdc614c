import requests
import json
import time
import os
from pathlib import Path
import base64
import random
import signal
import sys
import threading
from queue import Queue
from typing import List, Dict, Optional
import traceback
import glob

class APIAccount:
    def __init__(self, api_key: str, secret_key: str):
        self.api_key = api_key
        self.secret_key = secret_key
        self.headers = {
            'X-Key': f'Key {api_key}',
            'X-Secret': f'Secret {secret_key}',
        }
        self.is_busy = False
        self.consecutive_errors = 0
        self.last_error_time = 0
        self.total_generated = 0

class MultiAccountFusionBrainAPI:
    def __init__(self, accounts: List[Dict[str, str]], save_dir: str, positive_prompts: List[
        str], max_images: int = 10000):
        self.URL = 'https://api-key.fusionbrain.ai/'
        self.SAVE_DIR = Path(save_dir)
        self.accounts = [APIAccount(acc['api_key'], acc['secret_key']) for acc in accounts]
        self.prompt_queue = Queue()
        self.is_running = True
        self.is_paused = False
        self.generation_count = 0
        self.lock = threading.Lock()
        self.active_threads = []
        self.MAX_RETRIES = 3
        self.ERROR_COOLDOWN = 60  # секунд
        self.MIN_DELAY_BETWEEN_REQUESTS = 2  # минимальная задержка между запросами
        self.MAX_DELAY_BETWEEN_REQUESTS = 5  # максимальная задержка между запросами
        self.MAX_IMAGES = max_images
        self.CHECK_INTERVAL = 60  # Проверять количество файлов каждые 60 секунд

        self.SAVE_DIR.mkdir(parents=True, exist_ok=True)

        # Загружаем промпты
        self.positive_prompts = positive_prompts

        # Негативный промпт
        self.negative_prompt = "уродливый, низкое качество, размытый, искаженный, непропорциональный, неестественные цвета, артефакты, шум, пиксели, надписи, подписи, watermark"

    def handle_exit(self, signum, frame):
        """Обработчик для корректного завершения работы"""
        print(f"\n[{self.SAVE_DIR}] Получен сигнал завершения. Завершаем работу всех потоков...")
        self.is_running = False

        # Очищаем очередь промптов
        while not self.prompt_queue.empty():
            try:
                self.prompt_queue.get_nowait()
            except:
                pass

        # Ждем завершения всех активных потоков
        for thread in self.active_threads:
            thread.join(timeout=5)

        print(f"\n[{self.SAVE_DIR}] Генерация завершена. Всего сгенерировано изображений: {self.generation_count}")

    def count_images(self) -> int:
        """Подсчитывает количество изображений в папке сохранения"""
        return len(glob.glob(str(self.SAVE_DIR / "*")))

    def should_pause(self) -> bool:
        """Проверяет, следует ли приостановить генерацию из-за превышения лимита файлов"""
        count = self.count_images()
        if count >= self.MAX_IMAGES:
            if not self.is_paused:
                print(f"\n[{self.SAVE_DIR}] Достигнут лимит в {self.MAX_IMAGES} изображений. Генерация приостановлена.")
                self.is_paused = True
            return True
        else:
            if self.is_paused:
                print(f"\n[{self.SAVE_DIR}] Количество изображений ниже лимита ({count}/{self.MAX_IMAGES}). Генерация возобновлена.")
                self.is_paused = False
            return False

    def should_retry(self, account: APIAccount) -> bool:
        """Проверяет, следует ли повторить попытку для данного аккаунта"""
        current_time = time.time()
        if current_time - account.last_error_time > self.ERROR_COOLDOWN:
            account.consecutive_errors = 0
        return account.consecutive_errors < self.MAX_RETRIES

    def handle_error(self, account: APIAccount, error: str):
        """Обработка ошибок с учетом последовательных неудач"""
        account.consecutive_errors += 1
        account.last_error_time = time.time()
        print(f"[{self.SAVE_DIR}] Ошибка для аккаунта {account.api_key[-8:]}: {error}")
        if account.consecutive_errors >= self.MAX_RETRIES:
            print(f"[{self.SAVE_DIR}] Аккаунт {account.api_key[-8:]} временно приостановлен на {self.ERROR_COOLDOWN} секунд")

    def get_model(self, account: APIAccount) -> Optional[str]:
        """Получение доступных моделей для аккаунта с улучшенной обработкой ошибок"""
        try:
            response = requests.get(
                self.URL + 'key/api/v1/pipelines',
                headers={
                    **account.headers,
                    'Accept': 'application/json'
                },
                timeout=10
            )

            if response.status_code != 200:
                print(f"[{self.SAVE_DIR}] Ошибка получения моделей (HTTP {response.status_code}): {response.text}")
                return None

            try:
                data = response.json()
            except json.JSONDecodeError as je:
                print(f"[{self.SAVE_DIR}] Ошибка декодирования JSON при получении моделей: {str(je)}")
                print(f"[{self.SAVE_DIR}] Ответ сервера: {response.text}")
                return None

            if not data or not isinstance(data, list) or len(data) == 0:
                print(f"[{self.SAVE_DIR}] Неверный формат данных моделей: {json.dumps(data, ensure_ascii=False)}")
                return None

            if 'id' not in data[0]:
                print(f"[{self.SAVE_DIR}] Поле 'id' не найдено в данных модели: {json.dumps(data[0], ensure_ascii=False)}")
                return None

            return data[0]['id']

        except Exception as e:
            print(f"[{self.SAVE_DIR}] Ошибка получения модели для аккаунта {account.api_key[-8:]}: {str(e)}")
            print(f"[{self.SAVE_DIR}] Traceback:\n{traceback.format_exc()}")
            return None

    def generate(self, prompt: str, model: str, account: APIAccount) -> Optional[str]:
        """Запуск генерации изображения с улучшенной обработкой запросов"""
        if not self.is_running or self.is_paused:
            return None

        # Проверяем входные данные
        if not prompt or len(prompt.strip()) == 0:
            print(f"[{self.SAVE_DIR}] Ошибка: пустой prompt для аккаунта {account.api_key[-8:]}")
            return None

        # Нормализуем промпт (убираем лишние пробелы и переносы строк)
        prompt = " ".join(prompt.strip().split())

        # Проверяем длину промпта
        if len(prompt) > 500:  # максимальная длина может варьироваться в зависимости от API
            prompt = prompt[:500]
            print(f"[{self.SAVE_DIR}] Промпт был обрезан до 500 символов")

        params = {
            "type": "GENERATE",
            "numImages": 1,
            "width": 1080,
            "height": 1920,
            "negativePromptDecoder": self.negative_prompt,
            "generateParams": {
                "query": prompt
            }
        }

        # Создаем multipart/form-data с явным указанием Content-Type
        files = {
            'pipeline_id': (None, model),
            'params': (None, json.dumps(params, ensure_ascii=False), 'application/json')
        }

        headers = {
            **account.headers,
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        retry_count = 0
        while retry_count < self.MAX_RETRIES and self.is_running and not self.is_paused:
            try:
                # Добавляем случайную задержку перед запросом
                time.sleep(random.uniform(1, 3))

                print(f"\n[{self.SAVE_DIR}] Отправка запроса для аккаунта {account.api_key[-8:]}")
                print(f"[{self.SAVE_DIR}] Параметры запроса:")
                print(f"[{self.SAVE_DIR}] - pipeline_id: {model}")
                print(f"[{self.SAVE_DIR}] - prompt length: {len(prompt)} символов")

                response = requests.post(
                    self.URL + 'key/api/v1/pipeline/run',
                    headers=headers,
                    files=files,
                    timeout=15
                )

                # Логируем детали ответа
                print(f"[{self.SAVE_DIR}] Получен ответ: HTTP {response.status_code}")
                try:
                    response_json = response.json()
                    print(f"[{self.SAVE_DIR}] Тело ответа: {json.dumps(response_json, ensure_ascii=False, indent=2)}")
                except:
                    print(f"[{self.SAVE_DIR}] Тело ответа (не JSON): {response.text}")

                if response.status_code not in [200, 201]:
                    error_message = f"Ошибка HTTP {response.status_code}"
                    try:
                        error_json = response.json()
                        error_message += f": {json.dumps(error_json, ensure_ascii=False, indent=2)}"
                    except:
                        error_message += f": {response.text}"
                    print(f"[{self.SAVE_DIR}] Ошибка запроса: {error_message}")

                    # Специальная обработка ошибки 400
                    if response.status_code == 400:
                        # Если это ошибка превышения лимита запросов или другая временная ошибка
                        if "rate limit" in response.text.lower() or "too many requests" in response.text.lower():
                            wait_time = random.uniform(5, 15)  # более длительное ожидание
                            print(f"[{self.SAVE_DIR}] Превышен лимит запросов. Ожидание {wait_time:.1f} секунд...")
                            time.sleep(wait_time)
                        elif "invalid request" in response.text.lower():
                            print(f"[{self.SAVE_DIR}] Некорректный запрос. Проверка параметров...")
                            # Можно добавить дополнительную валидацию параметров

                    retry_count += 1
                    if retry_count < self.MAX_RETRIES:
                        wait_time = 2 ** retry_count + random.uniform(2, 5)
                        print(f"[{self.SAVE_DIR}] Повторная попытка {retry_count}/{self.MAX_RETRIES} через {wait_time:.1f} секунд...")
                        time.sleep(wait_time)
                    continue

                try:
                    result = response.json()
                except json.JSONDecodeError as je:
                    print(f"[{self.SAVE_DIR}] Ошибка декодирования JSON ответа: {str(je)}")
                    retry_count += 1
                    continue

                if 'uuid' not in result:
                    print(f"[{self.SAVE_DIR}] UUID не найден в ответе: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    retry_count += 1
                    continue

                print(f"[{self.SAVE_DIR}] Успешно получен UUID: {result['uuid']}")
                account.consecutive_errors = 0
                return result['uuid']

            except Exception as e:
                retry_count += 1
                error_details = f"Ошибка генерации изображения: {str(e)}"
                print(f"[{self.SAVE_DIR}] {error_details}")
                self.handle_error(account, error_details)

                if retry_count < self.MAX_RETRIES:
                    wait_time = 2 ** retry_count + random.uniform(2, 5)
                    print(f"[{self.SAVE_DIR}] Повторная попытка {retry_count}/{self.MAX_RETRIES} через {wait_time:.1f} секунд...")
                    time.sleep(wait_time)
                else:
                    print(f"[{self.SAVE_DIR}] Превышено количество попыток для аккаунта {account.api_key[-8:]}")
                    return None

        return None

    def check_generation(self, uuid: str, account: APIAccount) -> dict:
        """Улучшенная проверка статуса генерации"""
        max_attempts = 60
        attempt = 0

        while attempt < max_attempts and self.is_running and not self.is_paused:
            if not self.is_running:
                return {"status": "CANCELLED"}

            try:
                # Добавляем небольшую случайную задержку между проверками
                time.sleep(random.uniform(1, 2))

                response = requests.get(
                    self.URL + 'key/api/v1/pipeline/status/' + uuid,
                    headers=account.headers,
                    timeout=10
                )

                if response.status_code == 429:  # Too Many Requests
                    wait_time = random.uniform(5, 10)
                    print(f"[{self.SAVE_DIR}] Превышен лимит запросов при проверке статуса. Ожидание {wait_time:.1f} секунд...")
                    time.sleep(wait_time)
                    continue

                if response.status_code != 200:
                    print(f"[{self.SAVE_DIR}] Ошибка при проверке статуса: HTTP {response.status_code}")
                    attempt += 1
                    continue

                result = response.json()

                # Если статус всё ещё INITIAL после нескольких попыток, увеличиваем интервал проверки
                if result['status'] == 'INITIAL' and attempt > 10:
                    time.sleep(random.uniform(2, 4))
                    
                # Адаптация к обновленному формату ответа
                if 'result' in result and 'files' in result['result']:
                    result['images'] = result['result']['files']
                    
                return result

            except requests.exceptions.Timeout:
                print(f"[{self.SAVE_DIR}] Таймаут при проверке статуса")
                if not self.is_running:
                    return {"status": "CANCELLED"}
                time.sleep(random.uniform(1, 3))
                attempt += 1
                continue
            except Exception as e:
                print(f"[{self.SAVE_DIR}] Ошибка проверки статуса: {str(e)}")
                attempt += 1
                if attempt < max_attempts:
                    time.sleep(random.uniform(1, 3))
                    continue
                return {"status": "ERROR", "error": str(e)}

            attempt += 1

        return {"status": "ERROR", "error": "Превышено время ожидания"}

    def save_images(self, images_data: List[str], prompt: str):
        """Сохранение сгенерированных изображений"""
        if not self.is_running or self.is_paused:
            return

        timestamp = time.strftime("%Y%m%d_%H%M%S")

        for idx, image_base64 in enumerate(images_data):
            if not self.is_running or self.is_paused:
                return

            # Еще раз проверяем количество файлов перед сохранением
            if self.count_images() >= self.MAX_IMAGES:
                print(f"[{self.SAVE_DIR}] Достигнут лимит в {self.MAX_IMAGES} изображений при сохранении. Генерация приостановлена.")
                self.is_paused = True
                return

            image_data = base64.b64decode(image_base64)
            safe_prompt = "".join(x for x in prompt[:30] if x.isalnum() or x in (" ", "-", "_")).strip()
            filename = f"{safe_prompt}_{timestamp}_{idx + 1}.jpg"
            file_path = self.SAVE_DIR / filename

            with open(file_path, 'wb') as f:
                f.write(image_data)

            with self.lock:
                self.generation_count += 1
                print(f"[{self.SAVE_DIR}] Сохранено изображение: {filename} (Всего: {self.generation_count})")

    def worker(self, account: APIAccount):
        """Улучшенный рабочий процесс с дополнительным логированием"""
        print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Запуск воркера")

        # Получаем модель с повторными попытками
        model = None
        for attempt in range(3):
            try:
                model = self.get_model(account)
                if model:
                    print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Успешно получена модель {model}")
                    break
                print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Не удалось получить модель, попытка {attempt + 1}/3")
                time.sleep(2 ** attempt)
            except Exception as e:
                print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Ошибка получения модели: {str(e)}")
                if attempt < 2:
                    time.sleep(2 ** attempt)

        if not model:
            print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Критическая ошибка: не удалось получить модель после 3 попыток")
            return

        last_check_time = 0

        while self.is_running:
            try:
                # Проверяем, нужно ли приостановить генерацию
                current_time = time.time()
                if current_time - last_check_time > self.CHECK_INTERVAL:
                    if self.should_pause():
                        time.sleep(self.CHECK_INTERVAL)  # Если приостановлено, ждем и проверяем снова
                        last_check_time = current_time
                        continue
                    last_check_time = current_time

                # Проверяем статус аккаунта
                if account.consecutive_errors >= self.MAX_RETRIES:
                    cooldown_remaining = self.ERROR_COOLDOWN - (time.time() - account.last_error_time)
                    if cooldown_remaining > 0:
                        print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Аккаунт на паузе еще {cooldown_remaining:.1f} сек")
                        time.sleep(min(5, cooldown_remaining))
                        continue
                    print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Сброс счетчика ошибок")
                    account.consecutive_errors = 0

                # Получаем промпт
                try:
                    prompt = self.prompt_queue.get_nowait()
                    print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Получен промпт из очереди: {prompt[:50]}...")
                except:
                    prompt = random.choice(self.positive_prompts)
                    print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Использую случайный промпт: {prompt[:50]}...")

                # Генерация изображения
                print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Начало генерации...")
                uuid = self.generate(prompt, model, account)

                if uuid:
                    print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Получен UUID: {uuid}")
                    status_check_start = time.time()

                    while self.is_running and not self.is_paused and (
                            time.time() - status_check_start) < 300:  # 5 минут таймаут
                        status = self.check_generation(uuid, account)

                        if status['status'] == 'DONE':
                            print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Генерация завершена успешно")
                            if 'images' in status:
                                self.save_images(status['images'], prompt)
                            elif 'result' in status and 'files' in status['result']:
                                self.save_images(status['result']['files'], prompt)
                            else:
                                print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Ошибка: нет данных изображений в ответе: {status}")
                            account.total_generated += 1
                            break
                        elif status['status'] in ['ERROR', 'CANCELLED']:
                            print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Ошибка генерации: {status.get('error', 'Неизвестная ошибка')}")
                            self.handle_error(account, f"Ошибка генерации: {status.get('error', 'Неизвестная ошибка')}")
                            break

                        print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Ожидание генерации... Статус: {status['status']}")
                        time.sleep(2)
                else:
                    print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Не удалось получить UUID")
                    self.handle_error(account, "Не удалось получить UUID")

                # Случайная задержка между запросами
                delay = random.uniform(self.MIN_DELAY_BETWEEN_REQUESTS, self.MAX_DELAY_BETWEEN_REQUESTS)
                print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Ожидание {delay:.1f} секунд перед следующим запросом")
                time.sleep(delay)

            except Exception as e:
                print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Неожиданная ошибка:")
                traceback.print_exc()
                self.handle_error(account, str(e))
                time.sleep(5)

        print(f"[{self.SAVE_DIR}][{account.api_key[-8:]}] Воркер завершил работу. Всего сгенерировано: {account.total_generated}")

    def run_generation(self):
        """Запуск многопоточной генерации с улучшенным мониторингом"""
        print(f"[{self.SAVE_DIR}] Запуск многопоточной генерации с {len(self.accounts)} аккаунтами")

        self.active_threads = []
        for account in self.accounts:
            thread = threading.Thread(target=self.worker, args=(account,))
            thread.daemon = True
            thread.start()
            self.active_threads.append(thread)
            time.sleep(1)  # Небольшая задержка между запуском потоков

        # Возвращаем управление вызывающему коду для запуска других генераторов

    def monitor_activity(self):
        """Отдельный метод для мониторинга активности"""
        try:
            while self.is_running and any(thread.is_alive() for thread in self.active_threads):
                active_count = sum(1 for thread in self.active_threads if thread.is_alive())
                total_generated = sum(account.total_generated for account in self.accounts)
                file_count = self.count_images()
                status = "ПРИОСТАНОВЛЕНО" if self.is_paused else "АКТИВНО"

                print(f"\n[{self.SAVE_DIR}] Статус генерации: {status}")
                print(f"[{self.SAVE_DIR}] Активных потоков: {active_count}/{len(self.active_threads)}")
                print(f"[{self.SAVE_DIR}] Всего сгенерировано: {total_generated}")
                print(f"[{self.SAVE_DIR}] Изображений в папке: {file_count}/{self.MAX_IMAGES}")

                time.sleep(30)  # Проверяем каждые 30 секунд
        except KeyboardInterrupt:
            self.handle_exit(signal.SIGINT, None)
        finally:
            self.is_running = False
            print(f"\n[{self.SAVE_DIR}] Завершение работы...")
            for thread in self.active_threads:
                thread.join(timeout=5)
            print(f"[{self.SAVE_DIR}] Генерация завершена. Всего сгенерировано: {self.generation_count}")

class CombinedGenerator:
    def __init__(self, accounts, bg_prompts, overlay_prompts, max_images=10000):
        self.background_generator = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir='backgrounds',
            positive_prompts=bg_prompts,
            max_images=max_images
        )

        self.overlay_generator = MultiAccountFusionBrainAPI(
            accounts=accounts,
            save_dir='overlay',
            positive_prompts=overlay_prompts,
            max_images=max_images
        )

        self.is_running = True

        # Регистрируем обработчики сигналов
        signal.signal(signal.SIGINT, self.handle_exit)
        signal.signal(signal.SIGTERM, self.handle_exit)

    def handle_exit(self, signum, frame):
        """Обработчик для корректного завершения работы всех генераторов"""
        print("\nПолучен сигнал завершения. Завершаем работу всех потоков...")
        self.is_running = False
        self.background_generator.is_running = False
        self.overlay_generator.is_running = False
        print("\nГенерация завершена.")
        sys.exit(0)

    def run(self):
        """Запуск обоих генераторов"""
        print("Запуск комбинированного генератора изображений")
        print("Для остановки нажмите Ctrl+C")

        # Запускаем генераторы
        self.background_generator.run_generation()
        self.overlay_generator.run_generation()

        # Создаем отдельные потоки для мониторинга
        bg_monitor = threading.Thread(target=self.background_generator.monitor_activity)
        bg_monitor.daemon = True
        bg_monitor.start()

        ol_monitor = threading.Thread(target=self.overlay_generator.monitor_activity)
        ol_monitor.daemon = True
        ol_monitor.start()

        # Ждем нажатия Ctrl+C
        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.handle_exit(signal.SIGINT, None)

if __name__ == "__main__":
    # Список аккаунтов (одинаков для обоих генераторов)
    accounts = [
        {
            "api_key": "********************************",
            "secret_key": "CB9EB2D550EA66D708601D2C0EE97ED4"
        },
        {
            "api_key": "B99D6F9FA87820336880A337AFD42BD9",
            "secret_key": "80EF97EEAB92902FA28D43EA5AD2B981"
        },
        {
            "api_key": "0F4C95D11DF72B20EB148E3CE83C642D",
            "secret_key": "F749531F9E0D603A6AF692A53E968BFE"
        },
        {
            "api_key": "194DF8C7B84BDE6A22B152DCFAB328AD",
            "secret_key": "2764C94E25ECE5984558A61BC91D6826"
        },
        {
            "api_key": "5478E6DEF3D5BE0CA77535E544049E55",
            "secret_key": "339AA2D557EE5430088B9AB6A56DB5F6"
        },
        {
            "api_key": "7750EDFD36971CD7F6EFC4BE1DA845B3",
            "secret_key": "7C9F3BB7D2E19D277F7F433DFD02464B"
        },
        {
            "api_key": "15306067641844C1E7284BFE3786041B",
            "secret_key": "38B626D266D924E812CBCE38BEC83FBB"
        },
        {
            "api_key": "B7CC3DAFE5E1A2C01A8D67D81BB1C496",
            "secret_key": "CE9CA7053004C687D09ADE81466E995C"
        },
        {
            "api_key": "23A0010BC9D720DB3694602EDE02CEC7",
            "secret_key": "56A004539C4ACB5468853F06722C731A"
        },
        {
            "api_key": "E3B6803C8160844B9F706024666B31AB",
            "secret_key": "75A3178A7C4A8D3A78D051F941C06A99"
        },
        {
            "api_key": "3CBE43A828FEBFD4469CC5FD45595199",
            "secret_key": "58B3CDAB99E6B0BFA7D8ADF00FEA484D"
        },
        {
            "api_key": "FD6BF32737D506243DD84A7599876675",
            "secret_key": "4F3BC044B5C58C111694D8FF7EE96008"
        },
        {
            "api_key": "D0F80563652AC37E234BF046D01E5EC4",
            "secret_key": "6EDD90617FF9A128B9DC8D5F5B63E122"
        },
        {
            "api_key": "3C7BC2F6663EE7A129EF5B3A95A8113E",
            "secret_key": "E6A99687A056BF1E519B1A331E8FD03D"
        },
        {
            "api_key": "A5A6E7047BB44370C841B0A008E25B90",
            "secret_key": "513CB77D6BB34092D66A4BA541AB0972"
        },
        {
            "api_key": "C26F79DE58DAD026A8475EB81757DEF2",
            "secret_key": "A921E13CF67EA38D151C0404213D82F1"
        },
        {
            "api_key": "B30DE7160055BCFE5ADAEFA412742766",
            "secret_key": "A3B151C43D90DB64B084766D3F07E2B0"
        },
        {
            "api_key": "C24C6BCB805E4C5762A1EC5E554B3660",
            "secret_key": "DB0876EAA286434A305553DF3245EE69"
        },
        {
            "api_key": "85807F1B78E73014A947BA820DC5C86F",
            "secret_key": "4B2467E519884856CC5EDB0398C66380"
        },
        {
            "api_key": "A442EA2728CAA9BCAE12BB631D6BF2C6",
            "secret_key": "FDA66EC85BD3A26715691EE635952F45"
        },
        {
            "api_key": "DA3AC820C32A9C10A1EF49ED015BBA27",
            "secret_key": "419B21E3598A87E2D7CBB27396088D24"
        },
        {
            "api_key": "5EADC73D5EB5EF2374C70159D699D58C",
            "secret_key": "C5C0CDCE345AD10BBBFA5F10EA0D7106"
        },
        {
            "api_key": "1BE0C243AC9894766BDAE6E06637D219",
            "secret_key": "6BB830897B1B02443BD04C955F00427E"
        },
        {
            "api_key": "B1701C9CDE00B976CF6B9DA634D98DCB",
            "secret_key": "84515F2C3C16E1DA7162A8BA74C1708A"
        },
        {
            "api_key": "798697B7B7E9F38A612703F7D5AAD8D1",
            "secret_key": "FB1A0C0A5571F488BC04E22C1D6AD45C"
        },
        {
            "api_key": "48EE6ADA1302AC6001685092E670D405",
            "secret_key": "5F7FBFDE62AE589357A92ED3BABC3CB0"
        },
        {
            "api_key": "104E2B5F03925ED699640E14C34675A2",
            "secret_key": "0178B2636DEC8300CC1117C3D08FA88A"
        },
        {
            "api_key": "57AEFF467BD712BEE01A02AB00CE14B0",
            "secret_key": "E9790A204CFE30E171D37F8C4F49AB18"
        },
        {
            "api_key": "543A930E0469B75A5564EE53474E72B1",
            "secret_key": "0274B082D371020882A7EDD484931EEB"
        },
        {
            "api_key": "F6E1AADBEEA7A47F341AA163260267C5",
            "secret_key": "543035B50AF1C1A9A359BF6D4B425086"
        },
        {
            "api_key": "09422C8B9C7AC715287B00ED07EC5993",
            "secret_key": "C5E2D7356E6D04EB6CCB59C4BB082F66 "
        },
        {
            "api_key": "170768AD01115B3380EADDE459C4DC8F",
            "secret_key": "58811D14506120DD0BC24C6CEBA51EA8"
        },
        {
            "api_key": "77C12602D0A506B883A264803D48AF48",
            "secret_key": "2E06484D58B002ED3F365B8EF2D70000"
        },
        {
            "api_key": "276FC1B33985CF2129842F329893A357",
            "secret_key": "E6CE60320E055B959908F556F40A8460"
        },
        {
            "api_key": "3400386AA7349DE674BEB061C92A9BAF",
            "secret_key": "9F297437BCC26605059B3ADFBA49B730"
        },
        {
            "api_key": "E5FD4893107AB0B71452B5E6F022C7A7",
            "secret_key": "AC3960F3A714D36BE92BB8994022267F"
        },
        {
            "api_key": "028F8CF8DAB18095F31F98279942CF01",
            "secret_key": "8ABA2CDF9D91BDA8C2533EE6B49C3947"
        },
        {
            "api_key": "DE14D7F60AF2C6794FA32C24C57E6EEB",
            "secret_key": "20526642A885A2C985E8EC61048385E4B"
        },
        {
            "api_key": "48D988C3FBA45F91D8DCB4F083E3F257",
            "secret_key": "15D118612D1BFAFA36C3D35789216B1A"
        },
        {
            "api_key": "A6B8AA725EFCB0944D45FDBE670F0CCB",
            "secret_key": "2EA84C83A0E98FBAB787923403B92D25"
        }
    ]

    # Промпты для фонов (из первого скрипта)
    bg_prompts = [
            "Тихий городской парк ранним утром, мягкий свет сквозь деревья, пустые скамейки, нейтральные тона",
            "Минималистичный интерьер офиса, большие окна, мягкий дневной свет, деревянный стол и стул",
            "Пустынная дорога через лес, легкий туман, рассветное освещение, спокойные зеленые и серые тона",
            "Старый мост через реку, мягкий свет от заката, едва заметные облака на горизонте",
            "Интерьер современной квартиры, минималистичная мебель, нейтральная цветовая гамма, мягкое освещение",
            "Городская улица под дождем, мокрый асфальт, отражение фонарей, приглушенные тона",
            "Берег озера в сумерках, спокойная водная гладь, едва заметные силуэты деревьев",
            "Промышленный цех с большими окнами, мягкий свет, металлические конструкции, нейтральные тона",
            "Пустая автостоянка вечером, длинные тени от фонарей, легкая дымка в воздухе",
            "Старая библиотека с высокими полками, мягкий свет из окон, деревянные детали интерьера",
            "Дорога через поле, окруженное травой, пасмурное небо, легкий ветер, землистые тона",
            "Кофейня с деревянными столами, мягкий свет от ламп, чашки на стойке, приглушенные коричневые оттенки",
            "Пустынный вокзал ночью, тусклый свет от ламп, силуэт поезда вдалеке",
            "Горный пейзаж на рассвете, приглушенные пастельные тона, туман между вершинами",
            "Современный лофт с кирпичными стенами, бетонный пол, металлические балки, нейтральная палитра",
            "Пустынная набережная в сумерках, мягкий свет фонарей, едва заметные волны на воде",
            "Лесная тропинка в тумане, мягкий свет сквозь деревья, приглушенные зеленые и серые тона",
            "Интерьер спортзала, минималистичное освещение, оборудование из металла, нейтральные тона",
            "Пустая терраса кафе, мягкий свет от фонарей, вид на городские огни вдали",
            "Дорога через пустыню, рассветное освещение, едва заметные облака на горизонте",
            "Старый порт, мягкий свет от фонарей, силуэты лодок вдалеке, приглушенные тона",
            "Пустынная автозаправка ночью, тусклый свет от ламп, легкая дымка в воздухе",
            "Минималистичный интерьер гостиной, большие окна, мягкий свет, нейтральная цветовая гамма",
            "Городской парк зимой, мягкий свет от фонарей, едва заметные следы на снегу",
            "Пустынная дорога через холмы, пасмурное небо, легкий туман, землистые тона",
            "Старый маяк на берегу, мягкий свет от заката, едва заметные волны на воде",
            "Реалистичный горный пейзаж на рассвете, мягкие пастельные тона, туман, медленно поднимающийся над долиной, вдали виднеются заснеженные вершины, атмосфера умиротворения и спокойствия",
            "Уютный камин в деревянном доме, теплый свет огня, темный интерьер с мягкими тенями, на полу ковер, на стенах картины в деревянных рамках, атмосфера домашнего уюта",
            "Тихое озеро в лесу, отражение деревьев в воде, мягкие зеленые и синие оттенки, на поверхности воды легкая рябь, вдали виднеются горы, атмосфера гармонии с природой",
            "Городской пейзаж ночью, огни небоскребов, дождь на асфальте, мокрые тротуары, отражения фонарей в лужах, атмосфера мегаполиса в дождливый вечер",
            "Пустыня с песчаными дюнами, закатное солнце, длинные тени, песок переливается золотистыми оттенками, вдали виднеется одинокое дерево, атмосфера уединения",
            "Старый каменный мост через реку, осенние деревья, желтые и оранжевые листья, легкий туман над водой, атмосфера осеннего утра",
            "Каменный пляж с волнами, серое небо, вода бьется о камни, вдали виднеются скалы, атмосфера спокойствия и силы природы",
            "Лесная тропа, окруженная высокими деревьями, солнечные лучи пробиваются сквозь листву, мягкий зеленый свет, атмосфера утренней прогулки",
            "Поле с дикими цветами, закатное солнце, длинные тени, легкий ветер колышет траву, атмосфера летнего вечера",
            "Старый замок на холме, туман вокруг, серое небо, каменные стены покрыты мхом, атмосфера загадочности и истории",
            "Дорога через горы, извилистый серпантин, облака ниже уровня дороги, вдали виднеются снежные вершины, атмосфера путешествия",
            "Тропический пляж с пальмами, бирюзовая вода, белый песок, легкие облака на небе, атмосфера расслабления и отдыха",
            "Заснеженный лес, снег на ветках деревьев, тишина, легкий туман, атмосфера зимнего утра",
            "Портовый город, лодки в гавани, старые здания, мягкий свет заката, атмосфера морского приключения",
            "Деревянная хижина в горах, дым из трубы, вокруг заснеженные вершины, атмосфера уединения и тепла",
            "Поле подсолнухов, яркое солнце, желтые цветы на фоне голубого неба, атмосфера летнего дня",
            "Каменный каньон, высокие стены, свет солнца пробивается сквозь узкое ущелье, атмосфера величия природы",
            "Деревенский пейзаж, старый сарай, поле с высокой травой, легкий ветер, атмосфера спокойной сельской жизни",
            "Городской парк осенью, скамейки, опавшие листья, легкий туман, атмосфера уединения в городе",
            "Водопад в джунглях, густая зелень, брызги воды, легкий туман, атмосфера дикой природы",
            "Старый маяк на скале, бурное море, волны разбиваются о камни, атмосфера силы и стойкости",
            "Дорога через пустыню, бесконечный горизонт, редкие кактусы, атмосфера путешествия в неизвестность",
            "Лесной ручей, прозрачная вода, камни на дне, солнечные лучи пробиваются сквозь деревья, атмосфера свежести",
            "Городская улица в дождь, мокрый асфальт, отражения фонарей, люди под зонтами, атмосфера вечернего мегаполиса",
            "Зимний лес, заснеженные деревья, тишина, легкий туман, атмосфера уединения и покоя",
            "Пляж с камнями, волны, серое небо, вдали виднеются скалы, атмосфера спокойствия и силы",
            "Горная река, быстрый поток, камни в воде, вокруг густой лес, атмосфера дикой природы",
            "Старый железнодорожный мост, ржавые рельсы, вокруг густая зелень, атмосфера заброшенности и истории",
            "Поле с лавандой, фиолетовые цветы, голубое небо, легкий ветер, атмосфера летнего дня",
            "Городская набережная, велосипеды, скамейки, река, атмосфера спокойного вечера",
            "Лесная поляна, грибы, мох, солнечные лучи пробиваются сквозь деревья, атмосфера сказки",
            "Дорога через поле, бесконечный горизонт, облака на небе, атмосфера свободы",
            "Старый корабль на берегу, ржавый корпус, песок вокруг, атмосфера заброшенности и истории",
            "Горное озеро, кристально чистая вода, вокруг заснеженные вершины, атмосфера уединения",
            "Деревенский дом, дым из трубы, заснеженные поля, атмосфера зимнего уюта",
            "Поле с маками, красные цветы, зеленые травы, голубое небо, атмосфера летнего дня",
            "Городской мост ночью, огни города, отражения в воде, атмосфера романтики",
            "Лесная дорога, туман, высокие деревья, атмосфера загадочности",
            "Пляж с ракушками, волны, легкий ветер, атмосфера спокойствия",
            "Горная тропа, извилистая дорога, вокруг скалы, атмосфера приключения",
            "Реалистичный вид на вулкан, дым поднимается из кратера, вокруг застывшая лава, серые и черные тона, атмосфера мощи и опасности",
            "Тропический лес, густая зелень, лианы, солнечные лучи пробиваются сквозь кроны деревьев, атмосфера дикой природы",
            "Старый маяк на скалистом берегу, бурное море, волны разбиваются о камни, атмосфера одиночества и силы",
            "Деревянный пирс на озере, спокойная вода, отражение деревьев, легкий туман, атмосфера утреннего спокойствия",
            "Городской парк весной, цветущие деревья, скамейки, люди гуляют, атмосфера свежести и жизни",
            "Пустынный каньон, высокие скалы, свет солнца освещает стены, атмосфера величия и пустоты",
            "Зимний лес, заснеженные деревья, тишина, легкий снегопад, атмосфера уюта и покоя",
            "Поле с подсолнухами, яркое солнце, желтые цветы на фоне голубого неба, атмосфера летнего дня",
            "Старый замок в горах, туман вокруг, серое небо, каменные стены покрыты мхом, атмосфера загадочности",
            "Дорога через горы, извилистый серпантин, облака ниже уровня дороги, вдали виднеются снежные вершины, атмосфера путешествия",
            "Тропический пляж с пальмами, бирюзовая вода, белый песок, легкие облака на небе, атмосфера расслабления",
            "Заснеженный лес, снег на ветках деревьев, тишина, легкий туман, атмосфера зимнего утра",
            "Портовый город, лодки в гавани, старые здания, мягкий свет заката, атмосфера морского приключения",
            "Деревянная хижина в горах, дым из трубы, вокруг заснеженные вершины, атмосфера уединения и тепла",
            "Поле подсолнухов, яркое солнце, желтые цветы на фоне голубого неба, атмосфера летнего дня",
            "Каменный каньон, высокие стены, свет солнца пробивается сквозь узкое ущелье, атмосфера величия природы",
            "Деревенский пейзаж, старый сарай, поле с высокой травой, легкий ветер, атмосфера спокойной сельской жизни",
            "Городской парк осенью, скамейки, опавшие листья, легкий туман, атмосфера уединения в городе",
            "Водопад в джунглях, густая зелень, брызги воды, легкий туман, атмосфера дикой природы",
            "Старый маяк на скале, бурное море, волны разбиваются о камни, атмосфера силы и стойкости",
            "Дорога через пустыню, бесконечный горизонт, редкие кактусы, атмосфера путешествия в неизвестность",
            "Лесной ручей, прозрачная вода, камни на дне, солнечные лучи пробиваются сквозь деревья, атмосфера свежести",
            "Городская улица в дождь, мокрый асфальт, отражения фонарей, люди под зонтами, атмосфера вечернего мегаполиса",
            "Зимний лес, заснеженные деревья, тишина, легкий туман, атмосфера уединения и покоя",
            "Пляж с камнями, волны, серое небо, вдали виднеются скалы, атмосфера спокойствия и силы",
            "Горная река, быстрый поток, камни в воде, вокруг густой лес, атмосфера дикой природы",
            "Старый железнодорожный мост, ржавые рельсы, вокруг густая зелень, атмосфера заброшенности и истории",
            "Поле с лавандой, фиолетовые цветы, голубое небо, легкий ветер, атмосфера летнего дня",
            "Городская набережная, велосипеды, скамейки, река, атмосфера спокойного вечера",
            "Лесная поляна, грибы, мох, солнечные лучи пробиваются сквозь деревья, атмосфера сказки",
            "Дорога через поле, бесконечный горизонт, облака на небе, атмосфера свободы",
            "Старый корабль на берегу, ржавый корпус, песок вокруг, атмосфера заброшенности и истории",
            "Горное озеро, кристально чистая вода, вокруг заснеженные вершины, атмосфера уединения",
            "Деревенский дом, дым из трубы, заснеженные поля, атмосфера зимнего уюта",
            "Поле с маками, красные цветы, зеленые травы, голубое небо, атмосфера летнего дня",
            "Городской мост ночью, огни города, отражения в воде, атмосфера романтики",
            "Лесная дорога, туман, высокие деревья, атмосфера загадочности",
            "Пляж с ракушками, волны, легкий ветер, атмосфера спокойствия",
            "Горная тропа, извилистая дорога, вокруг скалы, атмосфера приключения",
            "Старый замок на холме, туман вокруг, серое небо, каменные стены покрыты мхом, атмосфера загадочности и истории",
            "Утренняя гавань с рыбацкими лодками, морская дымка над водой, чайки в небе, влажный воздух, атмосфера начала дня",
            "Горное озеро в тумане, отражение снежных вершин, каменистый берег, тихая вода, атмосфера величественного спокойствия",
            "Весенний сад в цвету, лепестки на ветру, солнечные блики через ветви, мягкий свет, атмосфера пробуждения природы",
            "Старая мельница у реки, заросший мхом камень, журчащая вода, папоротники по берегам, атмосфера забытого времени",
            "Осенняя аллея в парке, красно-желтая листва, длинные тени, прохладный воздух, атмосфера умиротворения",
            "Ночной мост через реку, отражения фонарей в воде, пустынная набережная, легкий туман, атмосфера городской романтики",
            "Летний луг на рассвете, капли росы на траве, первые лучи солнца, птичье пение, атмосфера свежести утра",
            "Заброшенная теплица, разбитые стекла, вьющиеся растения, солнечные лучи сквозь пыль, атмосфера меланхолии",
            "Горная тропа в облаках, каменистый склон, редкие альпийские цветы, прохладный ветер, атмосфера высоты",
            "Зимний лес в сумерках, следы на снегу, голые ветви деревьев, тихий снегопад, атмосфера таинственности",
            "Песчаный берег на закате, набегающие волны, следы на мокром песке, солёный бриз, атмосфера умиротворения",
            "Старый сад после дождя, мокрая листва, капли на цветах, свежий воздух, атмосфера обновления",
            "Деревенская околица в полдень, полевые цветы, жужжание пчел, теплый ветер, атмосфера летнего дня",
            "Ночная пристань, отражения фонарей, покачивающиеся лодки, шум волн, атмосфера морской романтики",
            "Горное плато на рассвете, низкие облака, дикие травы, прохладный ветер, атмосфера простора",
            "Осенний виноградник, спелые грозди, золотистая листва, мягкий свет, атмосфера урожая",
            "Зимняя деревня вечером, дым из труб, свет в окнах, сугробы снега, атмосфера уюта",
            "Весенний ручей в лесу, талая вода, первая зелень, влажный воздух, атмосфера пробуждения",
            "Летняя терраса в цветах, плетеная мебель, солнечные блики, жужжание пчел, атмосфера загородной жизни",
            "Старая башня на холме, каменная кладка, дикий плющ, грозовые облака, атмосфера древности",
            "Прибрежные скалы в шторм, бьющиеся волны, морская пена, хмурое небо, атмосфера стихии",
            "Полевая дорога летом, колосья пшеницы, васильки у обочины, жаркое солнце, атмосфера зрелости лета",
            "Горное озеро на закате, розовые облака, тихая вода, горные пики, атмосфера величия природы",
            "Весенний сад в сумерках, цветущие яблони, вечерняя прохлада, последние лучи солнца, атмосфера нежности",
            "Старый причал на реке, скрипучие доски, тихая вода, речные травы, атмосфера речной жизни",
            "Осенний парк в тумане, мокрые скамейки, опавшие листья, тишина, атмосфера одиночества",
            "Летний вечер в поле, стрекот кузнечиков, высокие травы, закатное солнце, атмосфера покоя",
            "Зимняя аллея в парке, заснеженные фонари, следы на снегу, морозный воздух, атмосфера сказки",
            "Весенний дождь в городе, мокрый асфальт, отражения витрин, спешащие люди, атмосфера городской жизни",
            "Горная хижина на склоне, альпийские луга, далекие вершины, чистый воздух, атмосфера высокогорья",
            "Старая мельница осенью, заросший пруд, желтые листья, тихий день, атмосфера забвения",
            "Морской берег утром, следы на песке, крики чаек, свежий бриз, атмосфера начала дня",
            "Деревенский двор летом, цветущие мальвы, колодец, куры в пыли, атмосфера сельской жизни",
            "Лесное озеро в тумане, коряги в воде, утренняя дымка, тишина, атмосфера таинственности",
            "Городской двор после дождя, мокрые деревья, лужи с отражениями, свежий воздух, атмосфера чистоты",
            "Старый маяк в бурю, темные тучи, пенные волны, ветер с моря, атмосфера морской стихии",
            "Весенний луг с цветами, порхающие бабочки, теплый ветер, яркое солнце, атмосфера радости",
            "Горное ущелье на рассвете, туманная дымка, скалистые стены, горный поток, атмосфера величия",
            "Зимний лес в солнечный день, искрящийся снег, синие тени, морозный воздух, атмосфера бодрости",
            "Осенняя река на закате, золотые отражения, опавшие листья на воде, тихий вечер, атмосфера грусти",
            "Лавандовое поле на рассвете, бледно-фиолетовые цветы, серебристая роса, мягкий утренний свет, атмосфера пробуждения",
            "Пляж с белым песком, бирюзовая вода, светлые облака, редкие пальмы, атмосфера тропического спокойствия",
            "Березовая роща весной, нежно-зеленая листва, белые стволы, солнечные блики, атмосфера легкости",
            "Цветущий миндальный сад, бледно-розовые лепестки, мягкий ветер, светлое небо, атмосфера нежности",
            "Горное озеро в пастельных сумерках, жемчужная вода, светлый туман, тихий вечер, атмосфера умиротворения",
            "Меловые скалы у моря, белый камень, бледно-голубая вода, легкие облака, атмосфера чистоты",
            "Рисовые террасы на рассвете, светло-зеленые поля, утренняя дымка, бледное солнце, атмосфера гармонии",
            "Дюны в пастельных тонах, бежевый песок, светлое небо, редкие травы, атмосфера минимализма",
            "Соляное озеро на закате, розоватая вода, белые кристаллы соли, нежное небо, атмосфера неземного",
            "Цветущая вишня в парке, бледно-розовые цветы, светлая зелень, мягкий свет, атмосфера весеннего обновления",
            "Туманное утро в долине, жемчужная дымка, светлые силуэты деревьев, прохладный воздух, атмосфера начала дня",
            "Заснеженное поле на рассвете, бледно-голубые тени, розовое небо, искрящийся снег, атмосфера чистоты",
            "Прибрежные травы в дюнах, светло-золотые колоски, бежевый песок, легкий бриз, атмосфера прибрежного покоя",
            "Горное плато в облаках, светло-серая дымка, бледные горные цветы, мягкий свет, атмосфера высоты",
            "Пшеничное поле перед грозой, светло-золотые колосья, жемчужные облака, тихий ветер, атмосфера ожидания",
            "Заброшенная оранжерея, выцветшие стекла, бледные лучи света, пыльный воздух, атмосфера забвения",
            "Утренний сад в росе, светло-зеленая трава, капли на листьях, нежный свет, атмосфера свежести",
            "Морской берег в пасмурный день, светло-серая вода, бледный песок, легкая дымка, атмосфера меланхолии",
            "Высокогорное озеро, бирюзовая вода, светлые камни, редкие облака, атмосфера чистоты гор",
            "Цветущий луг ромашек, белые цветы, светло-зеленая трава, мягкое солнце, атмосфера летней идиллии",
            "Старый маяк в тумане, белые стены, жемчужное море, светлое небо, атмосфера морской романтики",
            "Песчаная коса на рассвете, бежевый песок, бледно-голубая вода, утренний свет, атмосфера уединения",
            "Цветущий льняное поле, голубые цветы, светлое небо, легкий ветер, атмосфера нежности",
            "Известняковый каньон, белые скалы, светло-голубое небо, редкие облака, атмосфера минерального спокойствия",
            "Утренний туман в парке, жемчужная дымка, светлые силуэты деревьев, роса, атмосфера таинственности",
            "Заснеженный сад, белые ветви, бледно-розовый рассвет, морозный воздух, атмосфера зимней сказки",
            "Прибрежные скалы в пастельных тонах, светло-серый камень, бирюзовое море, легкие облака, атмосфера вечности",
            "Цветущий абрикосовый сад, бледно-розовые цветы, светлая зелень, мягкий свет, атмосфера весеннего пробуждения",
            "Горное озеро в облаках, перламутровая вода, светлый туман, тихий день, атмосфера горного спокойствия",
            "Летний сад в пастельных тонах, светло-зеленая листва, бледные цветы, мягкий свет, атмосфера уюта",
            "Меловой берег реки, белые обрывы, светло-голубая вода, перистые облака, атмосфера природной геометрии",
            "Цветущий вереск на холмах, бледно-лиловые цветы, светлое небо, легкий ветер, атмосфера шотландских просторов",
            "Туманное болото на рассвете, светло-зеленый мох, белая дымка, тихое утро, атмосфера таинственности",
            "Старый причал в пастельных тонах, выцветшее дерево, светлая вода, легкие облака, атмосфера прибрежного покоя",
            "Горная долина в облаках, жемчужный туман, светлые склоны, мягкий свет, атмосфера высокогорья",
            "Песчаные отмели на отливе, бежевый песок, светло-голубые лужицы, перламутровое небо, атмосфера морской геометрии",
            "Цветущий сад камней, светло-серый гравий, бледные мхи, утренняя роса, атмосфера дзен-сада",
            "Заброшенная теплица в светлых тонах, белые рамы, пыльные стекла, проникающий свет, атмосфера забвения",
            "Прибрежные дюны на закате, светло-бежевый песок, бледно-розовое небо, редкие травы, атмосфера прибрежного вечера",
            "Горное плато в тумане, светло-серые камни, белая дымка, тихий воздух, атмосфера высоты",
            "Цветущее поле гречихи, белые цветы, светло-зеленые листья, мягкое солнце, атмосфера летнего дня",
            "Известняковые террасы, белые камни, бирюзовые воды, светлое небо, атмосфера природных узоров",
            "Туманная бухта на рассвете, перламутровая вода, светлые скалы, нежное утро, атмосфера морского пробуждения",
            "Цветущий яблоневый сад, бледно-розовые цветы, светлая зелень, утренний свет, атмосфера весеннего сада",
            "Горная река в пастельных тонах, светло-голубая вода, белая пена, светлые камни, атмосфера горной свежести",
            "Прибрежные солончаки, белая соль, бледно-розовая вода, светлое небо, атмосфера природной абстракции",
            "Цветущий хлопок в поле, белые коробочки, светло-зеленые листья, мягкий свет, атмосфера южного дня",
            "Туманный луг на рассвете, жемчужная дымка, светлые травы, росистое утро, атмосфера пробуждения",
            "Меловые холмы, белые склоны, светло-голубое небо, редкие травы, атмосфера природного минимализма",
            "Морской берег в пастельных тонах, бежевый песок, светло-бирюзовая вода, легкие облака, атмосфера безмятежности",
            "Цветущая магнолия в саду, бледно-розовые цветы, светлые ветви, мягкий свет, атмосфера весенней нежности"

       ]

    # Промпты для оверлеев (из второго скрипта)
    ol_prompts = [
        "Размытое изображение облаков с нежными оттенками серого и белого, легкое и воздушное, без четких контуров",
            "Спокойная водная поверхность с легкими волнами и приглушенными отражениями в серо-голубых тонах, размытый эффект",
            "Легкий дым  с мягкими переходами и приглушенными серыми тонами, абстрактная дымка",
            "Легкий туман с мягкими переходами и приглушенными серыми тонами, абстрактная дымка",
            "Мягкое изображение леса в густом тумане, с приглушёнными зелёными и серыми оттенками, без чётких деталей, создающее ощущение дымки и спокойствия",
            "Плавные очертания гор, окутанных густыми облаками, с нейтральными серыми и белыми тонами, размытые линии вершин, напоминающие дым",
            "Спокойное море с мягким туманом над поверхностью, приглушённые синие и серые оттенки, размытый горизонт, похожий на облака или дым",
            "Размытое изображение водопада, где падающая вода сливается с туманом, мягкие белые и серые тона, создающие эффект дымного движения",
            "Лес с верхушками деревьев, скрытыми в низких облаках, приглушённые зелёные и белые оттенки, размытые контуры, напоминающие туман или дым",
            "Туманные абстрактные потоки с плавными переходами серого и белого, образующие легкую дымку, словно раннее утро в горах, когда туман стелется по долине, создавая мягкие, размытые силуэты, без резких контрастов и чётких линий",
            "Монохромные акварельные разводы в оттенках серого и белого, с легкими размытыми границами и плавными переходами, создающие эффект туманной дымки без ярких цветов",
            "Нежные акварельные пятна в приглушенных бежевых и светло-серых тонах, с мягкими размытыми краями и легкими переливами, образующие спокойный, нейтральный фон",
            "Светлые дымчатые текстуры с едва заметными переходами от белого к светло-серому, создающие воздушный эффект без насыщенных оттенков, словно утренний туман над водой",
            "Минималистичные акварельные разводы в пастельных нейтральных тонах, преимущественно белый, серый и светло-бежевый, с размытыми границами и мягкими переходами",
            "Тонкие слои прозрачной краски в оттенках слоновой кости и светло-серого, с едва заметными переходами и легкой текстурой, напоминающей старую бумагу",
            "Приглушенные разводы в монохромной гамме, имитирующие легкий дым или туман, с мягкими градиентами от белого к светло-серому, без ярких акцентов",
            "Нейтральные акварельные текстуры в оттенках светлого кофе с молоком и серого, с плавными переходами и размытыми контурами, создающие спокойный фон без насыщенных цветов",
            "Светлые мраморные текстуры с едва различимыми прожилками в тонах слоновой кости и светлого песка, с мягкими переходами и нежными оттенками без яркости",
            "Минималистичные разводы в оттенках холодного серого и белого, создающие ощущение легкой туманности и воздушности, с плавными переходами и отсутствием резких контрастов",
            "Нежная текстура старинной бумаги с легкими потертостями и мягкими тональными переходами в светло-бежевых и кремовых оттенках, без ярких цветовых акцентов",
            "Легкие абстрактные мазки в светло-серой и белой гамме, напоминающие размытое отражение в воде, с мягкими, расплывчатыми переходами и без интенсивных цветов",
            "Приглушенная текстура белого мрамора с тонкими серыми прожилками, мягкими переходами и нежными оттенками, создающая элегантный фон без насыщенных цветов",
            "Легкие акварельные разводы в мягких оттенках серого и белого, создающие воздушную текстуру с плавными переходами без ярких акцентов",
            "Нежные дымчатые потоки приглушенных бежевых тонов, размытые и полупрозрачные, словно утренний туман над песчаным пляжем",
            "Минималистичная текстура светлого мрамора с едва заметными серыми прожилками и мягкими переходами между оттенками",
            "Монохромные абстрактные пятна с размытыми краями в тонах слоновой кости, создающие спокойный нейтральный фон",
            "Акварельные разводы в приглушенных оттенках светло-серого, с мягкими границами и легкой текстурой старой бумаги",
            "Светлые облачные формации в нейтральных тонах, размытые и воздушные, словно белый дым на сером небе",
            "Нежные абстрактные потеки в оттенках светлого кофе с молоком, с плавными переходами и размытыми краями",
            "Мягкая текстура состаренной бумаги с легкими неровностями и теплыми кремовыми оттенками без яркого цветового контраста",
            "Светлые туманные вуали в холодных серых тонах, создающие впечатление утреннего марева над спокойной водой",
            "Приглушенные мраморные разводы в оттенках белого и светло-бежевого, с нежными переходами и элегантной текстурой",
            "Легкая песчаная текстура с мягкими тональными переходами в светло-бежевых оттенках, напоминающая спокойную пустыню",
            "Бледные акварельные мазки в оттенках холодного серого, создающие ощущение легкого тумана без ярких цветовых пятен",
            "Мягкие дымчатые струи в монохромных светло-серых тонах, плавно переходящие от одного оттенка к другому",
            "Нейтральные абстрактные формы с размытыми границами в оттенках светлого песка, создающие спокойный, гармоничный фон",
            "Легкая текстура потрескавшегося гипса в белых и кремовых тонах, с мягкими переходами и приглушенными контрастами",
            "Воздушные акварельные пятна в оттенках светлой умбры и серого, создающие мягкий, нейтральный фоновый эффект",
            "Нежные разводы в светло-кофейных тонах, напоминающие легкую дымку над поверхностью теплого молока с размытыми контурами",
            "Мягкие абстрактные текстуры состаренного пергамента с едва заметными потертостями и теплыми бежевыми оттенками",
            "Монохромные водяные разводы в оттенках графита и белого, создающие эффект размытых отражений на мокром асфальте",
            "Светлые мраморные текстуры с тонкими серыми прожилками и мягкими, едва заметными переходами между нейтральными оттенками",
            "Приглушенные акварельные мазки в тонах слоновой кости, создающие эффект старинной, выцветшей на солнце бумаги",
            "Легкие разводы пастельных серо-бежевых оттенков, размытые и нежные, словно утренний туман над песчаными дюнами",
            "Минималистичные текстуры светлого бетона с мягкими переходами и легкими потертостями в нейтральных серых тонах",
            "Нежные абстрактные формы в оттенках пепельно-белого, создающие воздушный, полупрозрачный фон без ярких акцентов",
            "Светлые песочные текстуры с тонкими разводами в тёплых нейтральных тонах, словно след волны на мокром песке",
            "Приглушенные акварельные потеки в холодных серых оттенках, размытые и лёгкие, напоминающие зимнее утреннее небо",
            "Мягкие мраморные разводы в оттенках белого и светло-серого с плавными переходами и нежной элегантной текстурой",
            "Воздушные абстрактные формы в тонах слоновой кости, создающие ощущение легкости и мягкости без заметных контрастов",
            "Нейтральные акварельные разводы молочно-белых и светло-песочных оттенков с размытыми краями и мягкими переходами",
            "Светлые дымчатые полупрозрачные текстуры в оттенках холодного серого, создающие ощущение тумана над утренним озером",
            "Перистые облака в светло-серых тонах, тонкие и полупрозрачные, с мягкими размытыми краями и плавными переходами",
            "Легкая облачная дымка в нейтральных оттенках белого и серого, создающая воздушную текстуру без резких контрастов",
            "Нежные кучевые облака на светло-сером фоне, размытые и объемные, словно пушистые подушки в утреннем тумане",
            "Мягкие высокие облака с размытыми очертаниями в пастельных серых тонах, создающие ощущение легкости и простора",
            "Облачные массы в монохромной гамме, напоминающие дымчатые завихрения с плавными переходами и нежными границами",
            "Рассеянные перистые облака в бледно-бежевых оттенках, создающие нейтральный фон с легкими текстурными элементами",
            "Воздушные слоистые облака в оттенках слоновой кости, мягкие и размытые, словно укрытые утренней дымкой",
            "Высокие перистые облака в холодных серо-белых тонах, тонкие и изящные, с нежными размытыми краями",
            "Светлые кучевые облака с мягкими контурами в серебристо-серых оттенках, создающие спокойный нейтральный фон",
            "Легкая облачная вуаль в пастельных серых тонах, полупрозрачная и нежная, с едва заметными переходами света",
            "Перисто-слоистые облака в приглушенных бежевых тонах, создающие размытую текстуру с мягкими световыми акцентами",
            "Дымчатые облачные формации в светло-серой гамме, с плавными переходами и воздушной, легкой текстурой",
            "Нежные кучево-дождевые облака в светлых монохромных оттенках, мягкие и объемные, с размытыми границами",
            "Облачное небо раннего утра в нейтральных серых тонах, с мягкими переходами и легкой дымчатой текстурой",
            "Высокие слоистые облака в оттенках светлого жемчуга, создающие воздушный, элегантный фон без ярких контрастов",
            "Перистые и перисто-кучевые облака в светло-кремовых тонах, с мягкими очертаниями и приглушенными переходами",
            "Легкие облачные массы в бледно-серых оттенках, напоминающие нежную вату с размытыми краями и мягкой текстурой",
            "Дымчатые слоистые облака в серебристо-белых тонах, переплетенные и воздушные, с плавными тональными переходами",
            "Нежные перисто-слоистые облака в монохромной гамме, создающие легкую вуаль с едва заметными градиентами серого",
            "Высокие кучевые облака в оттенках бледного песка, мягкие и объемные, с размытыми контурами и нежными тенями",
            "Облачная дымка в светлых нейтральных тонах, создающая воздушную текстуру с мягкими, едва уловимыми переходами",
            "Перистые облака в холодных серых оттенках, тонкие и изящные, словно легкие мазки акварели на сером небе",
            "Слоистые облака в мягких кремово-серых тонах, с плавными переходами и нежными, размытыми границами",
            "Легкие кучевые облака в пастельных оттенках слоновой кости, создающие воздушный фон с мягкими световыми акцентами",
            "Облачное покрывало в приглушенных серебристых тонах, нежное и полупрозрачное, с едва различимыми тональными переходами",
            "Легкие перистые облака в серебристо-серых оттенках, растянутые по всему небу, создающие нежную вуаль с мягкими переходами и размытыми границами без ярких акцентов",
            "Воздушные кучевые облака в светло-бежевых тонах с мягкими очертаниями и плавными переходами, словно пушистые подушки в золотистой утренней дымке раннего рассвета",
            "Нежные слоистые облака в оттенках слоновой кости и светло-серого, с мягкими границами и легкой текстурой, напоминающие тонкие слои шелковой ткани на фоне жемчужного неба",
            "Полупрозрачные перистые облака, растянутые тонкими нитями по светло-серому небу, с нежными размытыми краями и едва заметными переходами между оттенками холодного белого",
            "Высокие кучево-дождевые облака в мягких монохромных тонах с размытыми контурами и нежными градиентами серого, создающие воздушный объемный фон без контрастных элементов",
            "Легкая облачная дымка в нейтральных серо-белых оттенках, окутывающая небо подобно тончайшей вуали, с плавными, едва заметными переходами и мягкой воздушной текстурой",
            "Нежные слоисто-кучевые облака в пастельных оттенках бежевого и серого, с размытыми краями и мягкими волнистыми формами, напоминающие морскую пену на песчаном пляже",
            "Перисто-слоистые облака в холодных серебристых тонах, тонкие и изящные, создающие легкую ажурную текстуру с едва уловимыми тональными переходами и мягкими акцентами",
            "Дымчатые облачные формации в мягких оттенках светло-серого с тонкими градиентами и нежными переходами, словно утренний туман, застывший высоко в небе",
            "Воздушные кучевые облака в светлых кремовых тонах с мягкими, размытыми очертаниями и легкими тенями, образующие нежный, спокойный фон без ярких световых контрастов",
            "Высокие перистые облака в монохромных серебристо-белых оттенках, тонкие и прозрачные, с нежными размытыми краями и едва заметными переходами между светлыми и темными участками",
            "Облачная вуаль в приглушенных бежево-серых тонах, легкая и воздушная, с мягкими текстурными переходами и нежными световыми акцентами, создающая спокойный, гармоничный фон",
            "Слоистые облака в нейтральных оттенках жемчужно-серого, с плавными переходами и мягкими границами, напоминающие тонкие слои мраморной бумаги с нежной, элегантной текстурой",
            "Перисто-кучевые облака в светлых пастельных тонах с размытыми контурами и легкими тенями, словно мягкие хлопковые шарики на фоне бледно-серого утреннего неба",
            "Нежные облачные массы в оттенках светлого графита и белого, с мягкими тональными переходами и размытыми границами, создающие воздушный, лаконичный фон без резких контрастов",
            "Легкие кучевые облака в приглушенных оттенках слоновой кости, с мягкими, пушистыми краями и нежными тенями, похожие на взбитые сливки с легким золотистым отливом",
            "Высокие перистые облака в бледно-серебристых тонах, тонкие и изящные, напоминающие легкие мазки кисти на светло-сером фоне с плавными, едва заметными переходами",
            "Слоисто-дождевые облака в мягких оттенках холодного серого, с размытыми нижними краями и легкой туманной дымкой, создающие ощущение легкой меланхолии и спокойствия",
            "Облачное покрывало в нежных кремово-серых тонах с мягкими световыми акцентами и плавными тональными переходами, словно тонкая вуаль над спящим зимним пейзажем",
            "Перисто-слоистые облака в светлых монохромных оттенках с легкими волнистыми формами и размытыми краями, напоминающие прозрачные занавески, колышущиеся на легком ветру",
            "Высокие кучевые облака в приглушенных пастельных тонах с мягкими объемными формами и нежными тенями, создающие воздушный, легкий фон без явных цветовых акцентов",
            "Дымчатые облачные формации в светло-серых оттенках с плавными переходами и мягкими контурами, напоминающие легкие клубы дыма, медленно растворяющиеся в прохладном воздухе",
            "Нежные слоистые облака в бледно-бежевых тонах с едва заметными переходами и мягкими границами, создающие спокойный, элегантный фон с легкой текстурой и воздушностью",
            "Перистые облака в холодных серебристо-белых оттенках, тонкие и изящные, словно нежные штрихи белого карандаша на светло-сером фоне с мягкими, размытыми краями",
            "Облачная дымка в нейтральных оттенках светлого льна, полупрозрачная и воздушная, с мягкими текстурными переходами и нежными световыми эффектами утреннего рассеянного солнца",
            "Высокие кучево-дождевые облака в мягких монохромных тонах с размытыми контурами и легкими тенями, напоминающие пушистые горы на фоне жемчужно-серого неба",
            "Слоисто-кучевые облака в приглушенных оттенках светлого песка с мягкими волнистыми формами и плавными переходами, создающие нейтральный, гармоничный фон с легкой текстурой",
            "Легкие перистые облака в бледно-серебристых тонах, тонкие и прозрачные, с нежными размытыми краями и едва заметными переходами между светлыми и более темными участками",
            "Облачный покров в нежных оттенках холодного серого с мягкими тональными переходами и размытыми границами, напоминающий тонкую вуаль над спокойным зимним пейзажем",
            "Воздушные кучевые облака в светлых кремовых тонах с мягкими, пушистыми краями и легкими тенями, словно взбитые сливки на фоне нежно-бежевого неба утреннего рассвета",
            "Легкие клубы дыма в серебристо-серых оттенках, мягко растворяющиеся в воздухе с плавными переходами и размытыми границами, создающие нежную воздушную текстуру без резких контрастов",
            "Тонкие струйки белого дыма на светло-сером фоне, изящно переплетающиеся между собой, с мягкими размытыми контурами и нежными градиентами, словно танцующие в пространстве",
            "Мягкий снегопад в монохромных оттенках с размытыми, едва различимыми снежинками, создающий атмосферу зимнего спокойствия с нежными переходами света и воздушной текстурой",
            "Дымчатые завихрения в нейтральных светло-серых тонах, плавно растворяющиеся в пространстве, с мягкими формами и нежными переходами, напоминающие медитативные движения в воздухе",
            "Легкий снежный вихрь в пастельных белых и серебристых оттенках, с размытыми, мягкими контурами и плавными переходами, создающий ощущение зимней нежности и легкости",
            "Воздушные дымовые полосы в оттенках светлого графита и белого, с плавными изгибами и мягкими краями, словно легкие мазки кисти на нейтральном фоне",
            "Нежные снежные хлопья, медленно опускающиеся на светло-сером фоне, с размытыми очертаниями и мягкими переходами, создающие атмосферу тихого зимнего вечера без ярких акцентов",
            "Полупрозрачные клубы дыма в светло-бежевых тонах, мягко расползающиеся по пространству с нежными градиентами и воздушной текстурой, напоминающие утренний туман над теплой землей",
            "Снежная вуаль в холодных серебристо-белых оттенках, легкая и воздушная, с едва заметными переходами и мягкими светлыми акцентами, словно зимняя дымка над спящим полем",
            "Изящные дымовые спирали в нейтральных серых тонах, с мягкими изгибами и плавными переходами от светлого к темному, создающие ощущение легкости и воздушного танца",
            "Мягкий снежный покров в оттенках слоновой кости и светло-серого, с нежными тенями и размытыми границами, напоминающий пушистое одеяло с легкими складками и текстурными переходами",
            "Тонкие дымовые шлейфы в приглушенных пепельных тонах, с мягкими очертаниями и плавными переходами, словно элегантные линии графики на светлом холсте с легкими оттенками",
            "Кружащиеся снежинки в высоком ключе, с размытыми контурами и нежным свечением, создающие воздушную, легкую текстуру с мягкими переходами и приглушенным белым сиянием",
            "Воздушные дымчатые облака в светлых пастельных тонах, медленно растворяющиеся в пространстве с нежными переходами и мягкими границами, создающие ощущение умиротворения и легкости",
            "Снежная буря в мягких серебристо-белых оттенках, с размытыми движениями и нежными вихрями, напоминающая легкую вуаль, колышущуюся на холодном зимнем ветру",
            "Минималистичные дымовые формы в светло-пепельных тонах, с плавными линиями и мягкими переходами, создающие воздушную, лаконичную композицию без резких контрастов и ярких акцентов",
            "Нежный снежный флер в монохромных белых и серых оттенках, с мягкими, размытыми границами и легкими текстурными переходами, словно зимний сон в пастельных тонах",
            "Тающие снежинки на светлом фоне, полупрозрачные и нежные, с размытыми краями и мягкими бликами, создающие ощущение хрупкости и таяния без четких контуров",
            "Легкие дымные полосы в светло-бежевых тонах, плавно перетекающие друг в друга, с мягкими размытыми контурами и нежными градиентами, напоминающие шелковые ленты в воздухе",
            "Снежный вихрь в светлых нейтральных оттенках, с мягкими спиралевидными движениями и плавными переходами, создающий ощущение зимней легкости и воздушного танца ледяных кристаллов",
            "Тонкие слои дыма в пастельных серых тонах, наслаивающиеся друг на друга с едва заметными переходами и мягкими границами, создающие многомерную, но деликатную текстуру",
            "Мягкий снегопад сквозь морозную дымку, в холодных серебристых оттенках с нежными размытыми формами и легкими переливами, напоминающий зимний сон в высоком ключе",
            "Воздушные дымовые кольца в светло-серых тонах, плавно растворяющиеся в пространстве с мягкими градиентами и нежными переходами, создающие медитативный, спокойный визуальный ритм",
            "Снежные хлопья, сливающиеся с зимним небом в единую нежную текстуру светлых серебристо-белых оттенков, с размытыми границами и мягкими, едва заметными световыми акцентами",
            "Легкие клубы пара в нейтральных кремово-серых тонах, мягко рассеивающиеся в воздухе с плавными переходами и нежными формами, напоминающие утреннее дыхание в морозный день",
            "Зимняя метель в высоком ключе, с размытыми снежными вихрями и мягкими светлыми переходами, создающая ощущение легкости и воздушности без четких контуров и резких линий",
            "Элегантные дымовые завихрения в светлых пепельных тонах, с плавными изгибами и мягкими переходами, словно невесомые воздушные скульптуры в пространстве нейтральных оттенков",
            "Нежные снежные вуали в бледно-голубых и серебристых тонах, с размытыми краями и мягкими переливами, создающие ощущение зимней свежести и легкости без ярких акцентов",
            "Тающий снег в пастельных светло-серых оттенках, с нежными переходами и мягкими бликами, создающий минималистичную текстуру с легкими водянистыми разводами и воздушными переливами",
            "Тонкие струи дыма на светлом фоне, изящно переплетающиеся и создающие воздушную композицию с мягкими формами и нежными градиентами от белого к светло-серому без резких контрастов",

       ]

    # Запускаем комбинированный генератор
    combined = CombinedGenerator(
        accounts=accounts,
        bg_prompts=bg_prompts,
        overlay_prompts=ol_prompts,
        max_images=10000
    )
    combined.run()
