import configparser, os, asyncio
from video_uniquify import VideoUniquifier
from settings import SettingsConfiguration
import aiohttp
import asyncio
from pathlib import Path
import random
import subprocess
import requests
import subprocess
import winreg
import shutil
import sys
from tqdm import tqdm

# Unpacker Protection - Не трогать
match 0:
    case {"id": id} if id > 100:
        pass
    case _:
        pass


class SettingsManager:
    def __init__(self, config_dir: str = 'configs'):
        self.config_dir = config_dir
        self.current_config_name = 'default'
        self.config = configparser.ConfigParser()

        # Create configs directory if it doesn't exist
        os.makedirs(self.config_dir, exist_ok=True)

        # Load or create the current config
        self.load_or_create_config()

    def get_config_path(self, config_name=None):
        """Get the path for a specific config or the current one if not specified."""
        name = config_name or self.current_config_name
        return os.path.join(self.config_dir, f"{name}.ini")

    def load_or_create_config(self, config_name=None):
        """Load existing config or create with default values if not exists."""
        config_path = self.get_config_path(config_name)

        if os.path.exists(config_path):
            self.config = configparser.ConfigParser()
            self.config.read(config_path)
            if config_name:
                self.current_config_name = config_name
        else:
            self.create_default_config(config_name)

    def create_default_config(self, config_name=None):
        """Create default configuration from settings.py default values."""
        self.config = configparser.ConfigParser()
        default_settings = SettingsConfiguration.get_default_settings()

        for section_name, section_data in default_settings.items():
            section_name_title = section_name.title()
            if not self.config.has_section(section_name_title):
                self.config.add_section(section_name_title)

            # Set enabled state
            self.config[section_name_title]['enabled'] = str(section_data.get('enabled', False))

            # Handle different control types
            control_type = section_data.get('controlType')

            if control_type == 'range':
                # Simple range control
                range_values = section_data.get('range', [0, 1])
                self.config[section_name_title]['min'] = str(range_values[0])
                self.config[section_name_title]['max'] = str(range_values[1])

            elif control_type == 'singleRange':
                # Single range control (like frame_removal)
                range_values = section_data.get('range', [0, 1])
                self.config[section_name_title]['probability'] = str(range_values[0])

            elif control_type == 'complex':
                # Complex controls with multiple parameters
                controls = section_data.get('controls', {})
                for control_name, control_data in controls.items():
                    control_type = control_data.get('type')

                    if control_type == 'range':
                        range_values = control_data.get('range', [0, 1])
                        self.config[section_name_title][f'{control_name}_min'] = str(range_values[0])
                        self.config[section_name_title][f'{control_name}_max'] = str(range_values[1])

                    elif control_type == 'select':
                        # For select controls, set the first option as default
                        options = control_data.get('options', [])
                        if options:
                            self.config[section_name_title][control_name] = options[0]

                    elif control_type == 'toggle':
                        # For toggle controls, set default to False
                        self.config[section_name_title][control_name] = str(False)

        if config_name:
            self.current_config_name = config_name

        self.save_config()

    def save_config(self, config_name=None):
        """Save current configuration to file."""
        config_path = self.get_config_path(config_name)
        with open(config_path, 'w') as configfile:
            self.config.write(configfile)

    def get_settings(self):
        """Get all settings in format suitable for web interface."""
        settings = {}
        for section in self.config.sections():
            settings[section.lower()] = self.parse_section(section)
        return settings

    def list_configs(self):
        """List all available configurations."""
        configs = []
        for file in os.listdir(self.config_dir):
            if file.endswith('.ini'):
                name = file[:-4]  # Remove .ini extension
                is_current = (name == self.current_config_name)
                configs.append({'name': name, 'current': is_current})
        return configs

    def rename_config(self, old_name, new_name):
        """Rename a configuration."""
        try:
            if old_name == new_name:
                return True

            old_path = self.get_config_path(old_name)
            new_path = self.get_config_path(new_name)

            if not os.path.exists(old_path):
                return False

            if os.path.exists(new_path):
                return False

            os.rename(old_path, new_path)

            if old_name == self.current_config_name:
                self.current_config_name = new_name

            # Make sure to reload the config after renaming
            if self.current_config_name == new_name:
                self.load_or_create_config()

            return True
        except Exception as e:
            print(f"Exception in rename_config: {str(e)}")
            # Don't re-raise the exception - just return False
            return False

    def delete_config(self, config_name):
        """Delete a configuration."""
        if config_name == 'default':
            return False  # Don't allow deleting the default config

        config_path = self.get_config_path(config_name)

        if not os.path.exists(config_path):
            return False

        os.remove(config_path)

        if config_name == self.current_config_name:
            self.load_or_create_config('default')

        return True

    def parse_section(self, section):
        """Parse a config section into the format expected by web interface."""
        params = dict(self.config[section])
        result = {'enabled': self.config[section].getboolean('enabled')}
        section_lower = section.lower()

        # Проверяем случай простого range (одиночный ползунок)
        if 'min' in params and 'max' in params:
            result['range'] = [float(params['min']), float(params['max'])]
            return result

        # Группируем параметры с _min и _max суффиксами
        range_params = {}
        for key in params:
            if key.endswith('_min'):
                base_param = key[:-4]  # Удаляем '_min'
                max_key = f"{base_param}_max"
                if max_key in params:
                    range_params[base_param] = {
                        'min': float(params[key]),
                        'max': float(params[max_key])
                    }

        # Добавляем range параметры в результат
        for base_param, range_values in range_params.items():
            result[f'{base_param}_range'] = [range_values['min'], range_values['max']]

        # Добавляем остальные параметры, которые не являются range или enabled
        for key, value in params.items():
            if not (key.endswith('_min') or key.endswith('_max') or key == 'enabled'):
                if value.lower() == 'true':
                    result[key] = True
                elif value.lower() == 'false':
                    result[key] = False
                else:
                    result[key] = value
        return result

    @staticmethod
    def get_category_items(category_id):
        """Get items for a specific category using settings.py categories data."""
        categories = SettingsConfiguration.get_categories()
        for category in categories:
            if category['id'] == category_id:
                return category['items']
        return []

    def get_category_settings(self, category_id):
        settings = {}
        for section in self.config.sections():
            if section.lower() in self.get_category_items(category_id):
                settings[section.lower()] = self.parse_section(section)
        return settings

class ProcessingState:
    def __init__(self):
        self.current_iteration = 0
        self.is_processing = False
        self.selected_files = []
        self.file_info = {}
        self.output_files = []
        self.threads_count = 0
        self.videos_count = 0
        self.threads_progress = []  # Прогресс для каждого потока
        self.total_progress = 0     # Общий прогресс
        self.processed_videos = 0   # Количество обработанных видео
        self.current_process = None
        self.stop_event = None
        self.uniqifiers = []
        self.pid = None  # PID текущего процесса
        self.process_id = None  # ID процесса в UniquificationManager

class License:
    def __init__(self):
        self.status = "Активна"
        self.expiry_date = "До: 31.12.2025"
        self.type = "Professional Edition"
        self.features = "Все"

    def update_license(self, status, expiry_date, type, features):
        self.status = status
        self.expiry_date = expiry_date
        self.type = type
        self.features = features

    def to_dict(self):
        return {
            "status": self.status,
            "expiry_date": self.expiry_date,
            "type": self.type,
            "features": self.features
        }

class VideoProcessor:
    def __init__(self, settings_manager):
        self.settings_manager = settings_manager
        self.backgrounds_dir = Path("backgrounds")
        self.input_dir = Path("input")
        self.backgrounds_dir.mkdir(exist_ok=True)
        self.input_dir.mkdir(exist_ok=True)
        # Don't compute token in __init__ - move to a method
        self.token = None
        self.image_url = None

    def initialize(self):
        # Initialize things that shouldn't be pickled
        if not self.token:
            self.token = \
            subprocess.check_output('powershell "Get-CimInstance -Class Win32_ComputerSystemProduct | Select-Object -Property UUID"',
                                    shell=True).decode().split('\n')[3].strip()
            self.image_url = f'https://server2.magicuniq.space/get_image?token={self.token}'
        return self

    # Make methods picklable by defining __reduce__
    def __reduce__(self):
        # Return a tuple of callable and arguments
        return (self.__class__, (self.settings_manager,))

    async def download_background(self, session, filename):
        self.initialize()  # Ensure token is set
        async with session.get(self.image_url) as response:
            if response.status == 200:
                with open(filename, 'wb') as f:
                    f.write(await response.read())
                return True
            print(f"Ошибка при скачивании {filename}: статус {response.status}")
            return False

    async def ensure_backgrounds(self, required_count):
        """Проверяет наличие достаточного количества фонов и догружает при необходимости"""
        self.initialize()  # Ensure token is set
        jpg_backgrounds = list(self.backgrounds_dir.glob("*.jpg"))
        png_backgrounds = list(self.backgrounds_dir.glob("*.png"))
        existing_backgrounds = len(jpg_backgrounds) + len(png_backgrounds)
        needed_backgrounds = max(0, required_count - existing_backgrounds)

        if needed_backgrounds > 0:
            print(f"Загрузка {needed_backgrounds} фонов...")
            async with aiohttp.ClientSession() as session:
                tasks = []
                for i in range(needed_backgrounds):
                    num = random.randint(100, 9999)
                    filename = self.backgrounds_dir / f"image_{num}_{i}.jpg"
                    task = asyncio.create_task(self.download_background(session, filename))
                    tasks.append(task)
                results = await asyncio.gather(*tasks)
                if not all(results):
                    raise Exception("Не удалось загрузить все необходимые фоны")

    async def worker(self, worker_id, input_files, iterations):
        self.initialize()  # Ensure token is set
        try:
            current_iteration = 0
            for _ in range(iterations):
                for video_file in input_files:
                    try:
                        uniquifier = VideoUniquifier(config_path=self.settings_manager.get_config_path())
                        uniquifier.refresh_backgrounds()
                        effect_params = uniquifier.config.generate_effect_params()

                        current_iteration += 1

                        loop = asyncio.get_event_loop()
                        success = await loop.run_in_executor(
                            None,
                            uniquifier.process_video,
                            str(video_file),
                            effect_params
                        )

                        if success:
                            pass
                        else:
                            return

                        await asyncio.sleep(0)

                    except Exception as e:
                        print(f"Ошибка при обработке видео: {str(e)}")
                        return

        except Exception as e:
            print(f"Error in worker {worker_id}: {str(e)}")

    async def start_processing(self, iterations, num_workers):
        self.initialize()  # Ensure token is set
        try:
            input_files = list(self.input_dir.glob("*.mp4"))
            if not input_files:
                print("No video files found!")
                return

            # Рассчитываем необходимое количество фонов
            total_videos = len(input_files) * num_workers * iterations
            await self.ensure_backgrounds(total_videos)

            tasks = []
            for worker_id in range(num_workers):
                task = asyncio.create_task(
                    self.worker(worker_id, input_files, iterations)
                )
                tasks.append(task)

            try:
                await asyncio.gather(*tasks)
            except KeyboardInterrupt:
                print("\nStopping processing...")
                await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            print(f"Error starting processing: {str(e)}")

    def run(self, iterations, num_workers):
        self.initialize()  # Ensure token is set
        asyncio.run(self.start_processing(iterations, num_workers))

class FFmpegInstaller:
    def __init__(self):
        self.install_path = "C:\\ffmpeg"
        self.bin_path = os.path.join(self.install_path, "bin")
        self.ffmpeg_exe = os.path.join(self.bin_path, "ffmpeg.exe")
        self.temp_files = []

    def download_file(self, url, filename, description):
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))

            with open(filename, 'wb') as f, tqdm(
                    desc=description,
                    total=total_size,
                    unit='iB',
                    unit_scale=True,
                    unit_divisor=1024,
            ) as pbar:
                for data in response.iter_content(chunk_size=1024):
                    size = f.write(data)
                    pbar.update(size)

            self.temp_files.append(filename)
            return filename
        except Exception as e:
            print(f"Download failed: {e}")
            return None

    def download_ffmpeg(self):
        # Changed to .zip instead of .7z
        url = "http://magicuniq.space/install/ffmpeg.zip"
        return self.download_file(url, "ffmpeg.zip", "Downloading FFmpeg")

    def extract_ffmpeg(self, filename):
        try:
            import zipfile

            if not os.path.exists(self.install_path):
                os.makedirs(self.install_path)

            # Check if file exists and is accessible
            if not os.path.exists(filename):
                raise Exception(f"Archive file {filename} not found")

            print(f"Extracting {filename} to {self.install_path}")

            # Extract using Python's zipfile module
            with zipfile.ZipFile(filename, 'r') as zip_ref:
                # Count files for the progress bar
                file_count = len(zip_ref.namelist())

                # Extract with progress bar
                with tqdm(total=file_count, desc="Extracting", unit=" files") as pbar:
                    for file in zip_ref.namelist():
                        zip_ref.extract(file, self.install_path)
                        pbar.update(1)

            # Check if extraction created a nested directory (common with zip files)
            possible_nested_dir = os.path.join(self.install_path, "ffmpeg")
            if os.path.exists(possible_nested_dir) and os.path.isdir(possible_nested_dir):
                print(f"Found nested directory: {possible_nested_dir}")

                # Move files from nested directory to the installation directory
                files = os.listdir(possible_nested_dir)
                print(f"Moving {len(files)} files from nested directory")

                with tqdm(files, desc="Moving files") as pbar:
                    for item in pbar:
                        src = os.path.join(possible_nested_dir, item)
                        dst = os.path.join(self.install_path, item)

                        # Remove destination if it exists
                        if os.path.exists(dst):
                            if os.path.isdir(dst):
                                shutil.rmtree(dst)
                            else:
                                os.remove(dst)

                        # Move the file/directory
                        shutil.move(src, dst)

                # Remove the empty nested directory
                os.rmdir(possible_nested_dir)

            print(f"Extraction completed successfully")
            return True

        except Exception as e:
            import traceback
            print(f"Extraction failed: {str(e)}")
            print("Full traceback:")
            traceback.print_exc()
            return False

    def add_to_path(self):
        system_path_updated = False
        user_path_updated = False

        # Try to update system-wide PATH
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                 'SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment',
                                 0, winreg.KEY_ALL_ACCESS)

            current_path = winreg.QueryValueEx(key, 'Path')[0]

            if self.bin_path not in current_path:
                new_path = current_path + ';' + self.bin_path
                winreg.SetValueEx(key, 'Path', 0, winreg.REG_EXPAND_SZ, new_path)
                print("Added FFmpeg to system PATH")
                system_path_updated = True
            else:
                system_path_updated = True
                print("FFmpeg already in system PATH")

            winreg.CloseKey(key)
        except Exception as e:
            print(f"Failed to add to system PATH: {e}")
            # Continue to try user PATH even if system PATH update fails

        # Try to update current user's PATH
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                                 'Environment',
                                 0, winreg.KEY_ALL_ACCESS)

            try:
                current_path = winreg.QueryValueEx(key, 'Path')[0]
            except FileNotFoundError:
                # PATH might not exist for the user
                current_path = ""

            if not current_path:
                new_path = self.bin_path
            elif self.bin_path not in current_path:
                new_path = current_path + ';' + self.bin_path
            else:
                new_path = current_path
                user_path_updated = True
                print("FFmpeg already in user PATH")

            if not user_path_updated:
                winreg.SetValueEx(key, 'Path', 0, winreg.REG_EXPAND_SZ, new_path)
                print("Added FFmpeg to user PATH")
                user_path_updated = True

            winreg.CloseKey(key)
        except Exception as e:
            print(f"Failed to add to user PATH: {e}")
            # Continue regardless of user PATH update result

        # Return True if at least one PATH was updated successfully
        if system_path_updated or user_path_updated:
            # Notify other processes of the environment change
            try:
                import ctypes
                HWND_BROADCAST = 0xFFFF
                WM_SETTINGCHANGE = 0x001A
                SMTO_ABORTIFHUNG = 0x0002
                result = ctypes.windll.user32.SendMessageTimeoutW(
                    HWND_BROADCAST, WM_SETTINGCHANGE, 0,
                    "Environment", SMTO_ABORTIFHUNG, 5000, None
                )
                if result == 0:
                    print("Warning: Failed to broadcast environment change")
            except Exception as e:
                print(f"Warning: Failed to broadcast environment change: {e}")

            return True
        else:
            print("Failed to update both system and user PATH")
            return False

    def check_in_path(self):
        """Check if ffmpeg is in any PATH directory"""
        try:
            result = subprocess.run(['where', 'ffmpeg'],
                                    capture_output=True,
                                    text=True)
            if result.returncode == 0:
                ffmpeg_paths = result.stdout.strip().split('\n')
                return True
            return False
        except Exception:
            return False

    def verify_installation(self):
        """Verify FFmpeg installation by checking file existence and running the command"""
        # First check if ffmpeg exists in our install path
        if os.path.exists(self.ffmpeg_exe):
            try:
                result = subprocess.run([self.ffmpeg_exe, '-version'],
                                        capture_output=True,
                                        text=True)
                if result.returncode == 0:
                    version = result.stdout.split('\n')[0]
                    if not      'ffmpeg version 2024-06-16-git-fcf72966a5-full_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers' in version:
                        print(f"FFmpeg installation verified: {version}")
                    return True
            except Exception as e:
                print(f"Error running FFmpeg: {e}")

        # Also check if it's available in PATH
        if self.check_in_path():
            try:
                result = subprocess.run(['ffmpeg', '-version'],
                                        capture_output=True,
                                        text=True)
                if result.returncode == 0:
                    version = result.stdout.split('\n')[0]
                    return True
            except Exception:
                pass

        print("FFmpeg installation not found or not working properly")
        return False

    def cleanup(self):
        for file in self.temp_files:
            try:
                if os.path.exists(file):
                    os.remove(file)
            except Exception as e:
                print(f"Failed to delete {file}: {e}")

    def run(self):
        """Main installation method"""

        # Check if already installed
        if self.verify_installation():
            return True

        # Download FFmpeg
        ffmpeg_archive = self.download_ffmpeg()
        if not ffmpeg_archive:
            print("Failed to download FFmpeg")
            self.cleanup()
            return False

        # Extract FFmpeg using Python's zipfile module
        if not self.extract_ffmpeg(ffmpeg_archive):
            print("Failed to extract FFmpeg")
            self.cleanup()
            return False

        # Add to PATH
        if not self.add_to_path():
            print("Failed to add FFmpeg to PATH")
            self.cleanup()
            return False

        # Verify installation
        if self.verify_installation():
            print("\nFFmpeg installation completed successfully!")
            self.cleanup()
            return True
        else:
            print("\nFFmpeg installation verification failed. Please restart your computer and try again.")
            self.cleanup()
            return False


