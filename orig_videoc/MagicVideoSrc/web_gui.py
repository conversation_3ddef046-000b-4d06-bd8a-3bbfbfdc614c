import sys
import signal
import psutil
from flask import Flask, render_template, request, jsonify, render_template_string, make_response
from werkzeug.utils import secure_filename
import os
from multiprocessing import Process
import atexit
import asyncio
import requests
import logging
from pathlib import Path
import tempfile
import re
import shutil
import traceback
from settings import SettingsConfiguration
from video_uniquify import UniquificationManager
import time
import configparser
import subprocess

from classes import FFmpegInstaller, ProcessingState, License, SettingsManager, VideoProcessor

# Unpacker Protection - Не трогать
match 0:
    case {"id": id} if id > 100:
        pass
    case _:
        pass

ffmpeg_installer = FFmpegInstaller()
processing_state = ProcessingState()
license_info = License()
settings_manager = SettingsManager(config_dir="configs")
processor = VideoProcessor(settings_manager=settings_manager)


log = logging.getLogger('werkzeug')
log.setLevel(logging.ERROR)  # Будут показываться только ошибки

app = Flask(__name__, static_folder='web', template_folder='web')
app.config['UPLOAD_FOLDER'] = 'input'
unification_manager = None

open_in_gui = False
flask_debug = False
use_localhost = False

if use_localhost:
    app.config['base_url'] = '/web'
else:
    app.config['base_url'] = 'http://magicuniq.space/staticV3'

@app.route('/')
def index():
    if not use_localhost:
        # Получаем HTML-контент с удаленного сайта
        response = requests.get(f'{app.config["base_url"]}/index.html')
        response.raise_for_status()

        # Явно указываем кодировку UTF-8
        response.encoding = 'utf-8'
        html_content = response.text

        return render_template_string(
            html_content,
            state=processing_state,
            selected_files=processing_state.selected_files,
            output_files=processing_state.output_files
        )
    else:
        return render_template('index.html',
                               state=processing_state,
                               selected_files=processing_state.selected_files,
                               output_files=processing_state.output_files)

@app.route('/get_license_info')
def get_license_info():
    return jsonify(license_info.to_dict())

@app.route('/upload', methods=['POST'])
def upload_files():
    # Ensure upload directory exists
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    if 'files[]' not in request.files:
        return jsonify({'error': 'No file part'})

    files = request.files.getlist('files[]')

    for file in files:
        if file.filename == '':
            continue

        if file:
            filename = secure_filename(file.filename)

            # Check if the file is a .mov file and rename it to .mp4
            if filename.lower().endswith('.mov'):
                # Change the extension from .mov to .mp4
                filename = os.path.splitext(filename)[0] + '.mp4'

            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(file_path)
            processing_state.selected_files.append(filename)

    return jsonify({
        'files': processing_state.selected_files,
        'success': True
    })

@app.route('/reset_processing', methods=['POST'])
def reset_processing():
    processing_state.is_processing = False
    processing_state.current_iteration = 0
    processing_state.selected_files = []
    processing_state.output_files = []
    return jsonify({'status': 'reset'})

@app.route('/update_setting', methods=['POST'])
def update_setting():
    try:
        data = request.json
        setting_id = data['settingId']
        setting_value = data['value']
        setting_type = data['type']

        # Получаем название секции (с большой буквы)
        section_name = setting_id.title()

        if not settings_manager.config.has_section(section_name):
            settings_manager.config.add_section(section_name)

        # Обновляем enabled состояние
        settings_manager.config[section_name]['enabled'] = str(setting_value.get('enabled', False))
        # Handle different setting types
        if setting_type == 'range':
            if 'range' in setting_value:
                range_data = setting_value['range']
                if range_data.get('min') is not None:
                    settings_manager.config[section_name]['min'] = str(range_data['min'])
                if range_data.get('max') is not None:
                    settings_manager.config[section_name]['max'] = str(range_data['max'])
        elif setting_type == 'singleRange':
            if 'probability' in setting_value:
                settings_manager.config[section_name]['probability'] = str(setting_value['probability'])
        elif setting_type == 'complex':
            # Process each parameter in the complex setting
            for param_name, param_data in setting_value.items():
                # Skip the 'enabled' parameter as it's already handled
                if param_name == 'enabled':
                    continue

                if isinstance(param_data, dict) and 'type' in param_data:
                    param_type = param_data['type']

                    if param_type == 'range':
                        # Handle range type parameters
                        min_val = param_data.get('min')
                        max_val = param_data.get('max')
                        if min_val is not None:
                            settings_manager.config[section_name][f'{param_name}_min'] = str(min_val)
                        if max_val is not None:
                            settings_manager.config[section_name][f'{param_name}_max'] = str(max_val)

                    elif param_type == 'toggle':
                        # Handle toggle type parameters
                        settings_manager.config[section_name][param_name] = str(param_data.get('value', False))

                    elif param_type == 'select':
                        # Handle select type parameters
                        settings_manager.config[section_name][param_name] = str(param_data.get('value', ''))

        settings_manager.save_config()
        return jsonify({'success': True})

    except Exception as e:
        print(traceback.format_exc())
        return jsonify({'success': False, 'error': str(e)})

@app.route('/get_settings')
def get_settings():
    return jsonify(settings_manager.get_settings())

@app.route('/remove_file', methods=['POST'])
def remove_file():
    data = request.json
    filename = data.get('filename')

    if filename:
        # Create deleted folder if it doesn't exist
        deleted_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'deleted')
        os.makedirs(deleted_folder, exist_ok=True)

        # Source and destination paths
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        dest_path = os.path.join(deleted_folder, filename)

        # If file exists, move it to deleted folder instead of removing
        if os.path.exists(file_path):
            # Handle file already exists in deleted folder
            if os.path.exists(dest_path):
                base_name, extension = os.path.splitext(filename)
                counter = 1
                while os.path.exists(dest_path):
                    new_filename = f"{base_name}_{counter}{extension}"
                    dest_path = os.path.join(deleted_folder, new_filename)
                    counter += 1

            shutil.move(file_path, dest_path)
            if filename in processing_state.selected_files:
                processing_state.selected_files.remove(filename)

    # Return updated list of files
    files = []
    for file in os.listdir(app.config['UPLOAD_FOLDER']):
        if os.path.isfile(os.path.join(app.config['UPLOAD_FOLDER'], file)) and file.lower().endswith(('.mp4', '.avi',
                                                                                                      '.mov', '.mkv')):
            files.append(file)

    return jsonify({
        'success': True,
        'files': files
    })

@app.route('/clear_files', methods=['POST'])
def clear_files():
    # Create deleted folder if it doesn't exist
    deleted_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'deleted')
    os.makedirs(deleted_folder, exist_ok=True)

    # Clear the selected files list
    processing_state.selected_files = []

    # Move all video files from the upload folder to deleted folder
    for file in os.listdir(app.config['UPLOAD_FOLDER']):
        if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
            source_path = os.path.join(app.config['UPLOAD_FOLDER'], file)
            dest_path = os.path.join(deleted_folder, file)

            # Handle file already exists in deleted folder
            if os.path.exists(dest_path):
                base_name, extension = os.path.splitext(file)
                counter = 1
                while os.path.exists(dest_path):
                    new_filename = f"{base_name}_{counter}{extension}"
                    dest_path = os.path.join(deleted_folder, new_filename)
                    counter += 1

            try:
                shutil.move(source_path, dest_path)
            except Exception as e:
                print(f"Error moving file {file}: {e}")

    return jsonify({'status': 'files_cleared'})

@app.route('/get_existing_files')
def get_existing_files():
    files = []
    for file in os.listdir(app.config['UPLOAD_FOLDER']):
        if os.path.isfile(os.path.join(app.config['UPLOAD_FOLDER'], file)) and file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
            files.append(file)
    return jsonify({
        'files': files
    })

def dict_to_json(data):
    if isinstance(data, dict):
        items = []
        for key, value in data.items():
            items.append(f'"{key}": {dict_to_json(value)}')
        return '{' + ', '.join(items) + '}'
    elif isinstance(data, list):
        items = [dict_to_json(item) for item in data]
        return '[' + ', '.join(items) + ']'
    elif isinstance(data, str):
        return f'"{data}"'
    elif isinstance(data, bool):
        return 'true' if data else 'false'
    elif data is None:
        return 'null'
    else:
        return str(data)

@app.route('/get_category_settings/<category>')
def get_category_settings(category):
    try:
        # Получаем настройки для конкретной категории
        return jsonify({
            'success': True,
            'settings': settings_manager.get_category_settings(category)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/get_default_settings')
def get_default_settings():
    return dict_to_json(SettingsConfiguration.get_default_settings())

@app.route('/get_categories')
def get_categories():
    return jsonify(SettingsConfiguration.get_categories())

@app.route('/check_processing_status')
def check_processing_status():
    is_currently_processing = False
    process_info = None
    was_processing = processing_state.is_processing

    # Проверяем статус через UniquificationManager если есть process_id
    if processing_state.process_id:
        process_info = uniquification_manager.get_process_info(processing_state.process_id)
        if process_info:
            is_currently_processing = process_info.get('status') == 'running' and process_info.get('is_running', False)

            # Обновляем состояние обработки
            processing_state.total_progress = process_info.get('progress', 0)
            processing_state.current_iteration = process_info.get('current_iteration', 0)
            processing_state.is_processing = is_currently_processing

            # Если обработка была активна, но теперь завершена, открываем папку output
            if was_processing and not is_currently_processing:
                open_output_folder()

    return jsonify({
        'is_processing': is_currently_processing,
        'process_info': process_info,
        'threads_count': processing_state.threads_count,
        'videos_count': processing_state.videos_count,
        'total_progress': processing_state.total_progress,
        'processed_videos': processing_state.processed_videos
    })

# Add this new function to open the output folder
def open_output_folder():
    try:
        output_folder = os.path.abspath('output')
        os.makedirs(output_folder, exist_ok=True)
        subprocess.Popen(f'explorer "{output_folder}"')

    except Exception as e:
        print(f"Error opening output folder: {e}")

@app.route('/start_processing', methods=['POST'])
def start_processing_endpoint():
    try:
        data = request.json
        iterations = data.get('videos_count', 1)
        num_threads = data.get('threads_count', 1)



        if processing_state.is_processing:
            return jsonify({'status': 'error', 'message': 'Already running'}), 400

        # Создаем процесс
        processing_state.current_process = Process(
            target=processor.run,
            args=(iterations, num_threads)
        )
        processing_state.current_process.start()

        # Сохраняем PID процесса
        processing_state.pid = processing_state.current_process.pid

        # Регистрируем процесс в UniquificationManager
        processing_state.process_id = uniquification_manager.register_process(
            video_path="batch_process",
            total_iterations=iterations,
            pid=processing_state.pid
        )

        processing_state.is_processing = True
        processing_state.threads_count = num_threads
        processing_state.videos_count = iterations

        return jsonify({'status': 'success', 'message': 'Started'})

    except Exception as e:
        print(f"Ошибка при запуске: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/stop_processing', methods=['POST'])
def stop_processing():
    try:
        # Останавливаем через UniquificationManager
        if processing_state.process_id:
            uniquification_manager.stop_all()

            # Ждем подтверждения остановки
            max_wait = 10  # максимальное время ожидания в секундах
            wait_time = 0
            check_interval = 0.5  # интервал проверки в секундах

            while wait_time < max_wait:
                process_info = uniquification_manager.get_process_info(processing_state.process_id)
                if process_info.get('status') == 'stopped' or not process_info.get('is_running', False):
                    break
                time.sleep(check_interval)
                wait_time += check_interval

            # Сбрасываем состояние
            processing_state.is_processing = False
            processing_state.pid = None
            processing_state.process_id = None
            processing_state.current_process = None

            return jsonify({
                'status': 'success',
                'message': 'Обработка успешно остановлена'
            })

        return jsonify({
            'status': 'error',
            'message': 'Процесс не найден'
        }), 404

    except Exception as e:
        print(f"Ошибка при остановке: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/list_configs')
def list_configs():
    """Get a list of all available configurations."""
    return jsonify({
        'configs': settings_manager.list_configs(),
        'current': settings_manager.current_config_name
    })

@app.route('/create_config', methods=['POST'])
def create_config():
    """Create a new configuration."""
    data = request.json
    config_name = data.get('name')

    if not config_name:
        return jsonify({'success': False, 'error': 'No configuration name provided'})

    # Replace invalid characters for filenames
    config_name = "".join(c for c in config_name if c.isalnum() or c in '._- ')

    # Check if config already exists
    config_path = settings_manager.get_config_path(config_name)
    if os.path.exists(config_path):
        return jsonify({'success': False, 'error': 'Configuration with this name already exists'})

    try:
        settings_manager.create_default_config(config_name)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/switch_config', methods=['POST'])
def switch_config():
    """Switch to a different configuration."""
    data = request.json
    config_name = data.get('name')

    if not config_name:
        return jsonify({'success': False, 'error': 'No configuration name provided'})

    try:
        settings_manager.load_or_create_config(config_name)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/rename_config', methods=['POST'])
def rename_config():
    """Rename a configuration."""
    data = request.json
    old_name = data.get('old_name')
    new_name = data.get('new_name')

    if not old_name or not new_name:
        return jsonify({'success': False, 'error': 'Both old and new configuration names are required'})

    # Replace invalid characters for filenames
    new_name = "".join(c for c in new_name if c.isalnum() or c in '._- ')

    try:
        # The issue might be in the return value handling
        success = settings_manager.rename_config(old_name, new_name)

        # Force success to True if it looks like the operation worked
        if success is False:
            # Check if the rename actually worked despite the false return
            if settings_manager.current_config_name == new_name or os.path.exists(settings_manager.get_config_path(new_name)):
                success = True

        return jsonify({'success': success})
    except Exception as e:
        print(f"Error renaming config: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/delete_config', methods=['POST'])
def delete_config():
    """Delete a configuration."""
    data = request.json
    config_name = data.get('name')

    if not config_name:
        return jsonify({'success': False, 'error': 'No configuration name provided'})

    try:
        result = settings_manager.delete_config(config_name)
        return jsonify({'success': result})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/export_config')
def export_config():
    """Export a configuration file for download."""
    config_name = request.args.get('name')
    if not config_name:
        return jsonify({'success': False, 'error': 'No configuration name provided'}), 400

    config_path = settings_manager.get_config_path(config_name)
    if not os.path.exists(config_path):
        return jsonify({'success': False, 'error': 'Configuration not found'}), 404

    try:
        with open(config_path, 'r') as file:
            content = file.read()

        # Set headers for file download
        response = make_response(content)
        response.headers['Content-Disposition'] = f'attachment; filename={config_name}.ini'
        response.headers['Content-Type'] = 'application/octet-stream'  # Changed from text/plain
        response.headers['Cache-Control'] = 'no-cache'
        return response
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/import_config', methods=['POST'])
def import_config():
    """Import a configuration file."""
    try:
        if 'config_file' not in request.files:
            return jsonify({'success': False, 'error': 'No file part in the request'}), 400

        file = request.files['config_file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No selected file'}), 400

        if not file.filename.lower().endswith('.ini'):
            return jsonify({'success': False, 'error': 'File must be .ini format'}), 400

        # Get config name from filename (remove .ini extension)
        config_name = os.path.splitext(file.filename)[0]

        # Check if config name already exists
        config_path = settings_manager.get_config_path(config_name)
        if os.path.exists(config_path):
            # Add a suffix to make the name unique
            base_name = config_name
            counter = 1
            while os.path.exists(config_path):
                config_name = f"{base_name}_{counter}"
                config_path = settings_manager.get_config_path(config_name)
                counter += 1

        # Save the uploaded file with proper error handling
        try:
            file.save(config_path)

            # Test if the saved file is valid by trying to read it
            test_config = configparser.ConfigParser()
            test_config.read(config_path)

            # Load the new config
            settings_manager.load_or_create_config(config_name)

            return jsonify({'success': True, 'name': config_name})
        except configparser.Error:
            # If there's an error parsing the config, delete it and return an error
            if os.path.exists(config_path):
                os.remove(config_path)
            return jsonify({'success': False, 'error': 'Invalid configuration file format'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/check_onboarding_complete')
def check_onboarding_complete():
    try:
        # Check if the completion file exists in the data directory
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
        completion_file = os.path.join(data_dir, 'onboarding_complete')

        is_complete = os.path.exists(completion_file)

        return jsonify({'complete': is_complete})
    except Exception as e:
        print(f"Error checking onboarding status: {str(e)}")
        return jsonify({'complete': False, 'error': str(e)})

@app.route('/set_onboarding_complete', methods=['POST'])
def set_onboarding_complete():
    try:
        # Ensure the data directory exists
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
        os.makedirs(data_dir, exist_ok=True)

        # Create a file to mark onboarding as complete
        completion_file = os.path.join(data_dir, 'onboarding_complete')
        with open(completion_file, 'w') as f:
            f.write('completed')

        return jsonify({'success': True})
    except Exception as e:
        print(f"Error saving onboarding status: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

def cleanup():
    try:
        # Останавливаем все процессы через менеджер
        if 'uniquification_manager' in globals() and uniquification_manager:
            try:
                uniquification_manager.stop_all()
            except:
                pass
        # Завершаем текущий процесс обработки
        if hasattr(processing_state, 'current_process') and processing_state.current_process:
            try:
                if processing_state.current_process.is_alive():
                    processing_state.current_process.terminate()
                    processing_state.current_process.join(timeout=1)
            except:
                pass
        # Очищаем временные файлы с обработкой ошибок доступа
        temp_dir = Path(tempfile.gettempdir())
        patterns = [
            r'processed_audio_\d{4}\.wav',
            r'temp_video_\d{4}\.mp4',
            r'extracted_audio_\d{4}\.wav',
            r'final_\d{4}\.mp4'
        ]

        for file_path in temp_dir.iterdir():
            if file_path.is_file():
                for pattern in patterns:
                    try:
                        if re.match(pattern, file_path.name):
                            try:
                                file_path.unlink(missing_ok=True)
                            except:
                                pass
                            break
                    except:
                        pass
        # Очищаем UniqTemp только если доступ разрешен
        uniq_temp = temp_dir / 'UniqTemp'
        if uniq_temp.exists():
            try:
                # Перебираем файлы и удаляем каждый отдельно
                for item in uniq_temp.glob('*'):
                    try:
                        if item.is_file():
                            item.unlink(missing_ok=True)
                        elif item.is_dir():
                            shutil.rmtree(item, ignore_errors=True)
                    except:
                        pass
                # Пытаемся удалить саму директорию
                try:
                    shutil.rmtree(uniq_temp, ignore_errors=True)
                except:
                    pass
            except:
                pass
    except Exception as e:
            pass

atexit.register(cleanup)

def run_server():
    print('Запускаем сервер на http://127.0.0.1:5000/')
    app.run(debug=flask_debug, port=5000)

def safe_exit():
    """
    Функция для корректного завершения всех дочерних процессов перед выходом
    из программы. Сначала завершает все дочерние процессы, а затем текущий процесс.
    """
    # Получить текущий процесс
    current_process = psutil.Process(os.getpid())

    # Получить все дочерние процессы текущего процесса
    try:
        children = current_process.children(recursive=True)

        # Сначала пытаемся завершить процессы корректно
        for child in children:
            try:
                child.terminate()
            except psutil.NoSuchProcess:
                pass

        # Ждем завершения процессов (timeout в секундах)
        _, alive = psutil.wait_procs(children, timeout=1)

        # Если какие-то процессы не завершились корректно, завершаем их принудительно
        for child in alive:
            try:
                child.kill()
            except psutil.NoSuchProcess:
                pass

        # print(f"Завершено {len(children)} дочерних процессов")
    except Exception as e:
        print(f"Ошибка при завершении дочерних процессов: {e}")

    # Завершаем текущий процесс
    print("Завершение работы программы...")
    os._exit(0)

def run_webview():
    import webview
    ffmpeg_installer.run()
    window = webview.create_window('MagicUniq', app, maximized=True, confirm_close=False)
    webview.start()
    safe_exit()

def main_uniq():
    global uniquification_manager
    try:
        cleanup()
        uniquification_manager = UniquificationManager()
        if not open_in_gui:
            run_server()
        else:
            run_webview()
    except Exception as e:
        print(e)
        os._exit(1)

if __name__ == '__main__':
   main_uniq()
