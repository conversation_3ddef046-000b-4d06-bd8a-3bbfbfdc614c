import os
from pathlib import Path
import shutil
import cv2
import numpy as np
import random
from datetime import datetime
from tqdm import tqdm

# Unpacker Protection - Не трогать
match 0:
    case {"id": id} if id > 100:
        pass
    case _:
        pass

class Snowflake:
    def __init__(self, width, height):
        self.width = width
        self.height = height
        # Сначала распределяем по всему экрану
        self.reset(width, height, initial=True)
        # Добавляем параметры для более реалистичного движения
        self.angle = random.uniform(0, 2 * np.pi)
        self.swing_speed = random.uniform(0.01, 0.1)
        self.swing_amplitude = random.uniform(1, 3)
        self.swing_offset = random.uniform(0, 2 * np.pi)
        # Параметры для мерцания
        self.flicker_speed = random.uniform(0.05, 0.2)
        self.base_opacity = self.opacity
        self.time = random.uniform(0, 2 * np.pi)

    def reset(self, width, height, initial=False):
        self.x = random.randint(0, width)
        # При первой инициализации распределяем по всей высоте
        if initial:
            self.y = random.randint(0, height)
        else:
            # При последующих сбросах появляемся сверху
            self.y = random.randint(-10, 0)

        self.size = random.randint(1, 10)
        self.speed = random.uniform(1, 6)
        self.opacity = random.uniform(0.1, 1.0)
        self.wind = random.uniform(-0.6, 0.6)
        # Разные формы снежинок
        self.shape_type = random.choice(['circle', 'star', 'crystal'])

    def move(self, frame_number):
        # Добавляем колебательное движение
        self.time += 0.1
        self.angle += self.swing_speed

        # Синусоидальное движение для более реалистичного падения
        wind_effect = self.swing_amplitude * np.sin(self.angle + self.swing_offset)

        self.x += self.wind + wind_effect
        self.y += self.speed

        # Мерцание снежинок
        self.opacity = self.base_opacity * (0.7 + 0.3 * np.sin(self.time * self.flicker_speed))

        # При выходе за пределы экрана сбрасываем позицию без initial=True
        if self.y > self.height or self.x < 0 or self.x > self.width:
            self.reset(self.width, self.height, initial=False)

class SnowEffect:
    def __init__(self, width, height):
        self.width = width
        self.height = height
        self.snowflakes = []
        self.initialize_snowflakes()

    def initialize_snowflakes(self):
        # Генерируем случайное количество снежинок от 50 до 100
        snowflake_count = random.randint(30, 89)
        self.snowflakes = [Snowflake(self.width, self.height) for _ in range(snowflake_count)]

    def draw_crystal(self, layer, x, y, size, color, opacity):
        points = []
        num_points = 6
        for i in range(num_points):
            angle = 2 * np.pi * i / num_points
            points.append((
                int(x + size * np.cos(angle)),
                int(y + size * np.sin(angle))
            ))

        # Рисуем основные лучи
        for i in range(num_points):
            pt1 = (int(x), int(y))
            pt2 = points[i]
            cv2.line(layer, pt1, pt2, color, 1)

        # Рисуем соединяющие линии
        for i in range(num_points):
            pt1 = points[i]
            pt2 = points[(i + 1) % num_points]
            cv2.line(layer, pt1, pt2, color, 1)

    def draw_star(self, layer, x, y, size, color, opacity):
        points = []
        num_points = 5
        for i in range(num_points * 2):
            angle = np.pi * i / num_points
            curr_size = size if i % 2 == 0 else size * 0.5
            points.append((
                int(x + curr_size * np.cos(angle)),
                int(y + curr_size * np.sin(angle))
            ))

        pts = np.array(points, np.int32)
        pts = pts.reshape((-1, 1, 2))
        cv2.fillPoly(layer, [pts], color)

    def apply(self, frame, frame_number):
        frame_float = frame.astype(np.float32)
        snow_layer = np.zeros_like(frame_float)

        for snowflake in self.snowflakes:
            color = (255 * snowflake.opacity, 255 * snowflake.opacity, 255 * snowflake.opacity)

            if snowflake.shape_type == 'circle':
                # Классическая круглая снежинка с ореолом
                cv2.circle(snow_layer, (int(snowflake.x), int(snowflake.y)),
                          snowflake.size + 2, color, 1)
                cv2.circle(snow_layer, (int(snowflake.x), int(snowflake.y)),
                          snowflake.size, color, -1)
            elif snowflake.shape_type == 'star':
                self.draw_star(snow_layer, snowflake.x, snowflake.y,
                             snowflake.size, color, snowflake.opacity)
            else:  # crystal
                self.draw_crystal(snow_layer, snowflake.x, snowflake.y,
                                snowflake.size, color, snowflake.opacity)

            snowflake.move(frame_number)

        # Добавляем размытие с разной интенсивностью для разных размеров снежинок
        snow_layer_small = cv2.GaussianBlur(snow_layer, (3, 3), 1)
        snow_layer_big = cv2.GaussianBlur(snow_layer, (7, 7), 3)
        snow_layer = cv2.addWeighted(snow_layer_small, 0.6, snow_layer_big, 0.4, 0)

        # Добавляем легкий свечение
        glow = cv2.GaussianBlur(snow_layer, (15, 15), 5)
        snow_layer = cv2.addWeighted(snow_layer, 1, glow, 0.2, 0)

        result = cv2.addWeighted(frame_float, 1.0, snow_layer, 0.7, 0)
        result = np.clip(result, 0, 255).astype(np.uint8)

        return result
