class LaunchManager {
    constructor() {
        this.init();
        this.checkProcessingStatus();

        // Add status polling if processing is active
        if (this.isProcessing) {
            this.statusInterval = setInterval(() => {
                this.checkProcessingStatus();
            }, 1000); // Check every second
        }
    }

    init() {
        this.initModalHandlers();
        this.initSliders();
    }

    initSliders() {
        // Initialize videos slider with max limit of 200
        const videosSlider = document.getElementById('videosSlider');
        const videosDisplay = document.getElementById('videosValue');
        const videosSelected = videosSlider.parentElement.querySelector('.range-selected');

        // Set maximum value to 200
        videosSlider.max = 200;

        const updateVideosSlider = () => {
            const value = parseInt(videosSlider.value);
            const percent = ((value - videosSlider.min) / (videosSlider.max - videosSlider.min)) * 100;

            videosSelected.style.left = '0%';
            videosSelected.style.width = `${percent}%`;
            videosDisplay.textContent = `${value} видео`;
        };

        videosSlider.addEventListener('input', updateVideosSlider);
        videosSlider.addEventListener('change', updateVideosSlider);
        updateVideosSlider();

        // Initialize threads slider
        const threadsSlider = document.getElementById('threadsSlider');
        const threadsDisplay = document.getElementById('threadsValue');
        const threadsSelected = threadsSlider.parentElement.querySelector('.range-selected');
        const warning = document.getElementById('threadsWarning');

        const updateThreadsSlider = () => {
            const value = parseInt(threadsSlider.value);
            const percent = ((value - threadsSlider.min) / (threadsSlider.max - threadsSlider.min)) * 100;

            threadsSelected.style.left = '0%';
            threadsSelected.style.width = `${percent}%`;
            threadsDisplay.textContent = `${value} поток${value > 1 ? 'ов' : ''}`;

            if (value > 3) {
                warning.classList.remove('hidden');
            } else {
                warning.classList.add('hidden');
            }
        };

        threadsSlider.addEventListener('input', updateThreadsSlider);
        threadsSlider.addEventListener('change', updateThreadsSlider);
        updateThreadsSlider();
    }

    showCompletionState() {
    const container = document.getElementById('threadProgressBars');
    const stopButton = document.getElementById('stopProcessing');
    const closeButton = document.getElementById('closeLaunchModal');
    const completeButton = document.getElementById('completeCloseButton');

    // Hide stop button
    stopButton.classList.add('hidden');

    // Show close buttons
    closeButton.classList.remove('hidden');
    completeButton.classList.remove('hidden');

    // Update content with completion message
    container.innerHTML = `
        <div class="flex flex-col items-center justify-center space-y-6">
            <div class="flex items-center gap-3 text-green-400">
                <i data-lucide="check" class="w-8 h-8"></i>
                <span class="text-xl font-semibold">Уникализация успешно завершена</span>
            </div>
        </div>
    `;

    // Initialize the new Lucide icons
    lucide.createIcons();

    // Add event listener to the complete button if not already added
    if (!this.completeButtonInitialized) {
        completeButton.addEventListener('click', () => {
            this.isProcessing = false;
            this.closeModal();
        });
        this.completeButtonInitialized = true;
    }
}

    switchToProgress() {
        const title = document.getElementById('launchModalTitle');
        const settings = document.getElementById('launchSettings');
        const progress = document.getElementById('progressContent');
        const startButton = document.getElementById('startProcessing');
        const stopButton = document.getElementById('stopProcessing');
        const closeButton = document.getElementById('closeLaunchModal');
        const threadsCount = parseInt(document.getElementById('threadsSlider').value);
        const videosCount = parseInt(document.getElementById('videosSlider').value);

        this.isProcessing = true;

        // Hide close button during processing
        closeButton.classList.add('hidden');

        // Update title with transition
        title.style.opacity = '0';
        setTimeout(() => {
            title.textContent = 'Статус работы';
            title.style.opacity = '1';
        }, 300);

        // Hide settings and start button
        settings.style.opacity = '0';
        startButton.style.opacity = '0';
        setTimeout(() => {
            settings.classList.add('hidden');
            startButton.classList.add('hidden');
            stopButton.classList.remove('hidden');

            // Show progress content
            progress.classList.remove('hidden');
            this.initProgressBars(threadsCount, videosCount);
            setTimeout(() => {
                progress.style.opacity = '1';
                stopButton.style.opacity = '1';
            }, 50);
        }, 300);
    }

    async stopProcessing() {
        try {
            const response = await fetch('/stop_processing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                this.clearStatusPolling();
                this.closeModal();
                location.reload();
            }
        } catch (error) {
            console.error('Error stopping processing:', error);
        }
    }

    async showProgressModal(threadsCount, videosCount) {
        // Показываем модальное окно только если оно еще не показано
        if (!this.progressModalShown) {
            const modal = document.getElementById('launchModal');
            const title = document.getElementById('launchModalTitle');
            const settings = document.getElementById('launchSettings');
            const progress = document.getElementById('progressContent');
            const startButton = document.getElementById('startProcessing');
            const stopButton = document.getElementById('stopProcessing');
            const closeButton = document.getElementById('closeLaunchModal');

            modal.classList.remove('hidden');
            modal.classList.add('flex');

            const backdrop = modal.querySelector('.modal-backdrop');
            const dialog = modal.querySelector('.modal-dialog');
            backdrop.classList.add('active');
            dialog.classList.add('active');

            closeButton.classList.add('hidden');

            title.style.opacity = '0';
            setTimeout(() => {
                title.textContent = 'Статус работы';
                title.style.opacity = '1';
            }, 300);

            settings.style.opacity = '0';
            startButton.style.opacity = '0';
            setTimeout(() => {
                settings.classList.add('hidden');
                startButton.classList.add('hidden');
                stopButton.classList.remove('hidden');

                progress.classList.remove('hidden');
                this.initProgressBars(threadsCount, videosCount);
                setTimeout(() => {
                    progress.style.opacity = '1';
                    stopButton.style.opacity = '1';
                }, 50);
            }, 300);

            this.progressModalShown = true;
        }
    }

    initProgressBars(threadsCount, videosCount) {
        const container = document.getElementById('threadProgressBars');
        container.innerHTML = `
            <div class="text-center text-lg text-gray-300">
                Обработка началась
                <p class="mt-2 text-sm text-gray-400">
                    Прогресс можно отслеживать в консоли
                </p>
            </div>
        `;

        this.totalVideos = videosCount;
        this.processedVideos = 0;
    }

    updateProgress(threadIndex, progress) {
        if (threadIndex === -1) {
            console.log(`Общий прогресс: ${progress}%`);
        } else {
            console.log(`Поток ${threadIndex + 1}: ${progress}%`);
        }
    }

    updateTotalProgress(progress, processedVideos, totalVideos) {
        console.log(`Всего обработано: ${processedVideos} из ${totalVideos} (${progress}%)`);
    }

    initModalHandlers() {
        const processingToggle = document.getElementById('processingToggle');
        const modal = document.getElementById('launchModal');
        const backdrop = modal.querySelector('.modal-backdrop');
        const dialog = modal.querySelector('.modal-dialog');
        const closeButton = document.getElementById('closeLaunchModal');
        const startButton = document.getElementById('startProcessing');
        const stopButton = document.getElementById('stopProcessing');

        processingToggle.addEventListener('click', () => {
            if (!this.isProcessing) {
                modal.classList.remove('hidden');
                modal.classList.add('flex');

                requestAnimationFrame(() => {
                    backdrop.classList.add('active');
                    dialog.classList.add('active');
                });
            }
        });

        closeButton.addEventListener('click', () => {
            if (!this.isProcessing) {
                this.closeModal();
            }
        });

        backdrop.addEventListener('click', (e) => {
            if (e.target === backdrop && !this.isProcessing) {
                this.closeModal();
            }
        });

        startButton.addEventListener('click', () => {
            this.startProcessing();
        });

        stopButton.addEventListener('click', () => {
            this.stopProcessing();
        });
    }

    // Show error message in the modal
    showErrorMessage(message, statusCode) {
        const container = document.getElementById('threadProgressBars');
        container.innerHTML = `
            <div class="flex flex-col items-center justify-center space-y-4">
                <div class="flex items-center gap-3 text-red-500">
                    <i data-lucide="alert-circle" class="w-8 h-8"></i>
                    <span class="text-xl font-semibold">Ошибка</span>
                </div>
                <div class="text-center">
                    <p class="text-gray-300">${message}</p>
                    ${statusCode ? `<p class="mt-1 text-sm text-gray-400">Код ответа: ${statusCode}</p>` : ''}
                </div>
            </div>
        `;

        // Initialize the Lucide icons
        lucide.createIcons();
    }

    // Set button to loading state
    setButtonLoading(button, isLoading) {
        if (isLoading) {
            // Save original button text
            this.originalButtonText = button.innerHTML;

            // Replace with spinner
            button.innerHTML = `
                <div class="flex items-center justify-center">
                    <i data-lucide="loader-2" class="w-5 h-5 animate-spin mr-2"></i>
                    <span>Загрузка...</span>
                </div>
            `;

            // Disable the button
            button.disabled = true;
            button.classList.add('opacity-70', 'cursor-not-allowed');

            // Initialize the Lucide icons
            lucide.createIcons();
        } else {
            // Restore original button state
            if (this.originalButtonText) {
                button.innerHTML = this.originalButtonText;
            }

            // Re-enable the button
            button.disabled = false;
            button.classList.remove('opacity-70', 'cursor-not-allowed');
        }
    }

    async startProcessing() {
        const threadsCount = parseInt(document.getElementById('threadsSlider').value);
        const videosCount = parseInt(document.getElementById('videosSlider').value);
        const startButton = document.getElementById('startProcessing');

        // Set button to loading state
        this.setButtonLoading(startButton, true);

        try {
            const response = await fetch('/start_processing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    threads_count: threadsCount,
                    videos_count: videosCount
                })
            });

            // Get response data for possible error handling
            let responseData;
            try {
                responseData = await response.json();
            } catch (e) {
                responseData = { message: "Ошибка при обработке ответа сервера" };
            }

            if (response.ok) {
                this.isProcessing = true;
                this.progressModalShown = false;
                await this.showProgressModal(threadsCount, videosCount);
                this.updateTotalProgress(0, 0, videosCount);
                Array(threadsCount).fill(0).forEach((_, index) => {
                    this.updateProgress(index, 0);
                });

                // Start status polling
                this.statusInterval = setInterval(() => {
                    this.checkProcessingStatus();
                }, 1000);
            } else {
                // Reset loading button
                this.setButtonLoading(startButton, false);

                // Show error message
                const errorMessage = responseData.message || "Произошла ошибка при запуске обработки";
                this.showErrorMessage(errorMessage, response.status);

                // Prepare modal to show error
                const progress = document.getElementById('progressContent');
                const settings = document.getElementById('launchSettings');
                const title = document.getElementById('launchModalTitle');
                const closeButton = document.getElementById('closeLaunchModal');

                // Update title
                title.style.opacity = '0';
                setTimeout(() => {
                    title.textContent = 'Ошибка запуска';
                    title.style.opacity = '1';
                }, 300);

                // Hide settings, show progress with error
                settings.style.opacity = '0';
                setTimeout(() => {
                    settings.classList.add('hidden');
                    progress.classList.remove('hidden');
                    closeButton.classList.remove('hidden');
                    setTimeout(() => {
                        progress.style.opacity = '1';
                    }, 50);
                }, 300);
            }
        } catch (error) {
            console.error('Error starting processing:', error);

            // Reset loading button
            this.setButtonLoading(startButton, false);

            // Show error message
            this.showErrorMessage("Ошибка соединения с сервером", null);

            // Prepare modal to show error
            const progress = document.getElementById('progressContent');
            const settings = document.getElementById('launchSettings');
            const title = document.getElementById('launchModalTitle');
            const closeButton = document.getElementById('closeLaunchModal');

            // Update title
            title.style.opacity = '0';
            setTimeout(() => {
                title.textContent = 'Ошибка запуска';
                title.style.opacity = '1';
            }, 300);

            // Hide settings, show progress with error
            settings.style.opacity = '0';
            setTimeout(() => {
                settings.classList.add('hidden');
                progress.classList.remove('hidden');
                closeButton.classList.remove('hidden');
                setTimeout(() => {
                    progress.style.opacity = '1';
                }, 50);
            }, 300);
        }
    }

    async checkProcessingStatus() {
        try {
            const response = await fetch('/check_processing_status');
            const data = await response.json();

            if (!data.is_processing && this.isProcessing) {
                // Процесс завершен
                this.isProcessing = false;
                this.showCompletionState();
                this.clearStatusPolling();
                return;
            }

            if (data.is_processing && !this.progressModalShown) {
                this.isProcessing = true;
                await this.showProgressModal(data.threads_count, data.videos_count);
            }

            if (data.is_processing) {
                this.updateTotalProgress(data.total_progress, data.processed_videos, data.videos_count);
                data.threads_progress.forEach((progress, index) => {
                    this.updateProgress(index, progress);
                });
            }
        } catch (error) {
            console.error('Error checking processing status:', error);
        }
    }
    clearStatusPolling() {
        if (this.statusInterval) {
            clearInterval(this.statusInterval);
            this.statusInterval = null;
        }
    }

    closeModal() {
    if (this.isProcessing && !this.isCompleted) return;

    this.clearStatusPolling();

    const modal = document.getElementById('launchModal');
    const backdrop = modal.querySelector('.modal-backdrop');
    const dialog = modal.querySelector('.modal-dialog');
    const completeButton = document.getElementById('completeCloseButton');

    backdrop.classList.remove('active');
    dialog.classList.remove('active');

    setTimeout(() => {
        modal.classList.add('hidden');
        modal.classList.remove('flex');

        // Reset modal state
        const settings = document.getElementById('launchSettings');
        const progress = document.getElementById('progressContent');
        const startButton = document.getElementById('startProcessing');
        const stopButton = document.getElementById('stopProcessing');
        const title = document.getElementById('launchModalTitle');

        // Reset button to original state
        this.setButtonLoading(startButton, false);

        settings.classList.remove('hidden');
        settings.style.opacity = '1';
        progress.classList.add('hidden');
        progress.style.opacity = '0';
        startButton.classList.remove('hidden');
        startButton.style.opacity = '1';
        stopButton.classList.add('hidden');
        completeButton.classList.add('hidden'); // Скрываем кнопку завершения
        title.textContent = 'Параметры запуска';
    }, 300);
}
}

document.addEventListener('DOMContentLoaded', () => {
    window.launchManager = new LaunchManager();
});
