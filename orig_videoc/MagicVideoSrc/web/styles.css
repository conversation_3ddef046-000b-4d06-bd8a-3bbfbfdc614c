/* Custom styles that might not be covered by Tailwind */
body {
    background-attachment: fixed;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#dropzone {
    transition: all 0.3s ease;
}

/* Hide default file input styling */
input[type="file"] {
    display: none;
}

/* Анимация для модального окна */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.modal-animate-in {
    animation: modalFadeIn 0.2s ease-out;
}

/* Стили для скроллбара */
.settings-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.settings-scrollbar::-webkit-scrollbar-track {
    background: #1F2937;
    border-radius: 4px;
}

.settings-scrollbar::-webkit-scrollbar-thumb {
    background: #4B5563;
    border-radius: 4px;
}

.settings-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
}

/* Стили для переключателей */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #4B5563;
    transition: .4s;
    border-radius: 24px;
}

.toggle-switch-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-switch-slider {
    background-color: #3B82F6;
}

input:checked + .toggle-switch-slider:before {
    transform: translateX(20px);
}

/* Скрытие скроллбара */
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* Улучшенные стили для переключателей */
.switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #4B5563;
    transition: .4s;
    border-radius: 24px;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .switch-slider {
    background-color: #3B82F6;
}

input:checked + .switch-slider:before {
    transform: translateX(24px);
}

/* Базовые стили контейнера ползунка */
.range-slider {
    position: relative;
    width: 100%;
    padding: 10px 0;
}

/* Стили для трека ползунка */
.range-track {
    position: absolute;
    width: 100%;
    height: 4px;
    background: #4B5563;
    border-radius: 2px;
    top: 50%;
    transform: translateY(-50%);
}

/* Стили для выделенной области между ползунками */
.range-selected {
    position: absolute;
    height: 4px;
    background: #3B82F6;
    top: 50%;
    transform: translateY(-50%);
}

/* Стили для input[range] */
.range-slider input[type="range"] {
    position: absolute;
    width: 100%;
    -webkit-appearance: none;
    pointer-events: none;
    background: none;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
}

/* Стили для контрольных точек (кружочков) */
.range-slider input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    pointer-events: auto;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #3B82F6;
    border: 2px solid #fff;
    cursor: pointer;
    z-index: 3;
}

.range-slider input[type="range"]::-moz-range-thumb {
    pointer-events: auto;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #3B82F6;
    border: 2px solid #fff;
    cursor: pointer;
    z-index: 3;
}

/* Стили для активного состояния контрольных точек */
.range-slider input[type="range"]::-webkit-slider-thumb:hover,
.range-slider input[type="range"]::-webkit-slider-thumb:active {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.2);
}

.range-slider input[type="range"]::-moz-range-thumb:hover,
.range-slider input[type="range"]::-moz-range-thumb:active {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0.2);
}

/* Стили для отображения значений */
#range_value {
    font-family: 'Consolas', monospace;
    font-size: 0.875rem;
    color: #D1D5DB;
}

/* Hide scrollbar but keep functionality */
.no-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.no-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* Modal transition */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Content sections styling */
#settingsContent > div {
    @apply mb-6 last:mb-0 bg-gray-900/30 rounded-lg p-4;
}

/* Ensure proper spacing for settings groups */
#settingsTabs button {
    @apply w-full text-left px-4 py-3 rounded-lg transition-colors flex items-center gap-3
           hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

#settingsTabs button.active {
    @apply bg-gray-700;
}

.modal-content {
    height: 90vh;
    width: 70vw;
}

/* Стили для иконок */
[data-lucide] {
    stroke-width: 1.5px;
    stroke: currentColor;
}

/* Анимация для иконок при наведении */
button:hover [data-lucide] {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* Стили для активных кнопок */
button.active [data-lucide] {
    color: #3B82F6;
}

/* Стили для иконок в предупреждениях */
.text-yellow-500 [data-lucide] {
    stroke: currentColor;
}

.search-place {
    display: flex;
    align-items: center;
}

/* Modal animations */
.modal-backdrop {
    opacity: 0;
    backdrop-filter: blur(0);
    transition: all 0.3s ease-in-out;
}

.modal-backdrop.active {
    opacity: 1;
    backdrop-filter: blur(8px);
}

.modal-dialog {
    opacity: 0;
    transform: scale(0.95);
    transition: all 0.3s ease-in-out;
}

.modal-dialog.active {
    opacity: 1;
    transform: scale(1);
}

/* Анимация исчезновения */
.modal-fadeOut {
    animation: modalFadeOut 0.3s ease-in-out;
}

/* Add these styles to your existing CSS */
.file-list-container {
    position: relative;
    max-height: 280px; /* Height to accommodate 3 files plus some spacing */
    overflow-y: auto;
    padding: 20px 0;
}

/* Top gradient overlay */
.file-list-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(to bottom,
        rgba(31, 41, 55, 1) 0%,
        rgba(31, 41, 55, 0.8) 50%,
        rgba(31, 41, 55, 0) 100%
    );
    pointer-events: none;
    z-index: 10;
}

/* Bottom gradient overlay */
.file-list-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(to top,
        rgba(31, 41, 55, 1) 0%,
        rgba(31, 41, 55, 0.8) 50%,
        rgba(31, 41, 55, 0) 100%
    );
    pointer-events: none;
    z-index: 10;
}

/* Hide scrollbar but keep functionality */
.file-list-container {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.file-list-container::-webkit-scrollbar {
    display: none;
}

/* File item styling */
.file-item {
    margin: 12px 0;
    padding: 16px;
    background: rgba(31, 41, 55, 0.5);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.file-item:hover {
    background: rgba(55, 65, 81, 0.5);
}

/* Add button styling */
.add-file-button {
    position: sticky;
    bottom: 0;
    margin-top: 12px;
    padding: 16px;
    background: rgba(31, 41, 55, 0.5);
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-file-button:hover {
    background: rgba(55, 65, 81, 0.5);
}

.file-list-wrapper {
    position: relative;
}

.file-list-content {
    position: relative;
    max-height: 280px;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.file-list-content::-webkit-scrollbar {
    display: none;
}

.blur-overlay-top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;

    z-index: 10;
    pointer-events: none;
}

.blur-overlay-bottom {
    position: absolute;
    bottom: 64px; /* Adjust based on the height of your + button */
    left: 0;
    right: 0;
    height: 40px;

    z-index: 10;
    pointer-events: none;
}

.add-file-button {
    position: sticky;
    bottom: 0;
    width: 100%;
    padding: 16px;
    margin-top: 12px;
    background: rgba(31, 41, 55, 0.5);
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.single-border{
    border-radius: 2px;
}

/* Стили для кастомного селекта */
select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

select:focus {
    outline: none;
}

/* Анимация для стрелки */
select:focus + div [data-lucide="chevron-down"] {
    transform: rotate(180deg);
    transition: transform 0.2s ease;
}

/* Стили для опций */
select option {
    background-color: #374151;
    color: #D1D5DB;
    padding: 8px;
}

select option:hover {
    background-color: #4B5563;
}

@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

.skeleton-loader {
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.03) 25%,
        rgba(255, 255, 255, 0.05) 37%,
        rgba(255, 255, 255, 0.03) 63%
    );
    background-size: 1000px 100%;
    animation: shimmer 2s infinite linear;
}

.skeleton-loader.loaded {
    background: none;
    animation: none;
}

.launch-content {
    height: 60vh;
    width: 50vw;
}

@font-face {
    font-family: 'AltRektSolid';
    src: url('http://magicuniq.ru/static/altrektsolid.ttf') format('truetype');
}

/* Configuration management styles */
.config-manager {
    background-color: rgba(31, 41, 55, 0.3);
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-top: 1rem;
}

.config-manager select {
    min-width: 150px;
}

.config-manager button {
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

.config-manager button:hover {
    background-color: rgba(55, 65, 81, 0.5);
}

/* Modal styling */
.modal {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  transition: opacity 0.2s ease-in-out;
}

.modal.hidden {
  opacity: 0;
  pointer-events: none;
}

.modal-container {
  position: relative;
  width: 500px;
  max-width: 90vw;
  background-color: #1e293b;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transform: translateY(0);
  transition: transform 0.3s ease-in-out;
}

.modal.hidden .modal-container {
  transform: translateY(20px);
}

.modal-sm {
  width: 400px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(71, 85, 105, 0.3);
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #e2e8f0;
}

.modal-close {
  color: #94a3b8;
  transition: color 0.2s;
}

.modal-close:hover {
  color: #e2e8f0;
}

.modal-body {
  padding: 1.5rem;
}

.config-list-container {
  max-height: 300px;
  overflow-y: auto;
}

.config-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: rgba(30, 41, 59, 0.6);
  border-radius: 0.5rem;
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.config-item.active {
  border-color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
}

.config-item-name {
  font-weight: 500;
  color: #e2e8f0;
}

.config-item-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  margin-left: 0.5rem;
  background-color: rgba(59, 130, 246, 0.2);
  color: #93c5fd;
}

.config-item-actions {
  display: flex;
  gap: 0.5rem;
}

.input-field {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: rgba(30, 41, 59, 0.6);
  color: #e2e8f0;
  border: 1px solid rgba(71, 85, 105, 0.4);
  border-radius: 0.5rem;
  font-size: 0.95rem;
  transition: all 0.2s;
}

.input-field:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.primary-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
}

.primary-btn:hover {
  background-color: #2563eb;
}

.secondary-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background-color: rgba(51, 65, 85, 0.4);
  color: #e2e8f0;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
}

.secondary-btn:hover {
  background-color: rgba(71, 85, 105, 0.6);
}

/* Configuration styling */
.config-wrapper {
  background-color: rgba(30, 41, 59, 0.4);
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px solid rgba(71, 85, 105, 0.2);
}

.config-select-container {
  position: relative;
}

.config-select {
  appearance: none;
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: rgba(30, 41, 59, 0.6);
  color: #e2e8f0;
  border: 1px solid rgba(71, 85, 105, 0.4);
  border-radius: 0.5rem;
  font-size: 0.95rem;
  transition: all 0.2s;
}

.config-select:hover {
  background-color: rgba(51, 65, 85, 0.6);
  border-color: rgba(100, 116, 139, 0.5);
}

.config-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.config-select-arrow {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  pointer-events: none;
}

.icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  background-color: rgba(51, 65, 85, 0.4);
  color: #94a3b8;
  transition: all 0.2s;
}

.rename-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  color: #94a3b8;
  transition: all 0.2s;
}

.icon-btn:hover {
  background-color: rgba(71, 85, 105, 0.6);
  color: #e2e8f0;
}

/* Style for the config selector in main menu */
#mainConfigSelect {
  min-width: 200px;
  background-color: rgba(31, 41, 55, 0.5);  /* bg-gray-800/50 */
  color: white;
  border: 1px solid rgba(75, 85, 99, 0.5);  /* border-gray-600/50 */
  border-radius: 0.5rem;
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  appearance: none;
  font-size: 0.875rem;
}

/* Onboarding styles */
#onboardingModal {
    font-family: inherit;
}

.onboarding-content {
    transition: opacity 0.3s ease, transform 0.3s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    z-index: 100;
}

.onboarding-content.fade-out {
    opacity: 0;
    transform: scale(0.95);
}

.onboarding-highlight {
    position: relative;
    z-index: 60;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
    border-radius: inherit;
    animation: pulse 2s infinite;
    transition: box-shadow 0.3s ease;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 12px rgba(59, 130, 246, 0.1);
    }
    100% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
    }
}

.onboarding-connector {
    position: fixed;
    height: 2px;
    background: linear-gradient(to right, rgba(59, 130, 246, 0.7), rgba(59, 130, 246, 1));
    transform-origin: left center;
    z-index: 50;
    pointer-events: none;
}

.onboarding-connector::after {
    content: '';
    position: absolute;
    right: -1px;
    top: -4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 1);
    animation: pulsePoint 2s infinite;
}

@keyframes pulsePoint {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

/* Mascot image animation */
.mascot-image {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}


/* Onboarding styles */
#onboardingModal {
    font-family: inherit;
}

.onboarding-content {
    transition: opacity 0.3s ease, transform 0.3s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    z-index: 100;
    width: 450px;
    background-color: #1e293b; /* Darker blue gray */
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.onboarding-content.fade-out {
    opacity: 0;
    transform: scale(0.95);
}

.onboarding-highlight {
    position: relative;
    z-index: 60;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
    border-radius: inherit;
    animation: pulse 2s infinite;
    transition: box-shadow 0.3s ease;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 12px rgba(59, 130, 246, 0.1);
    }
    100% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
    }
}

.onboarding-connector {
    position: fixed;
    height: 2px;
    background-color: rgba(59, 130, 246, 0.8);
    transform-origin: left center;
    z-index: 50;
    pointer-events: none;
}

.onboarding-connector::after {
    content: '';
    position: absolute;
    right: -1px;
    top: -4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 1);
    animation: pulsePoint 2s infinite;
}

@keyframes pulsePoint {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

/* Onboarding styles */
#onboardingModal {
    font-family: inherit;
    z-index: 9999;
}

.onboarding-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.onboarding-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

.onboarding-content {
    transition: opacity 0.3s ease, transform 0.3s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    z-index: 9999;
    width: 450px;
    background-color: #1e293b; /* Darker blue gray */
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.onboarding-content.fade-out {
    opacity: 0;
    transform: scale(0.95);
}

.onboarding-highlight {
    position: relative;
    z-index: 9500;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
    border-radius: inherit;
    animation: pulse 2s infinite;
    transition: box-shadow 0.3s ease;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 12px rgba(59, 130, 246, 0.1);
    }
    100% {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
    }
}

.onboarding-connector {
    position: fixed;
    height: 2px;
    background-color: rgba(59, 130, 246, 0.8);
    transform-origin: left center;
    z-index: 9600;
    pointer-events: none;
}

.onboarding-connector::after {
    content: '';
    position: absolute;
    right: -1px;
    top: -4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 1);
    animation: pulsePoint 2s infinite;
}

@keyframes pulsePoint {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}



/* Onboarding styles */
#onboardingModal {
    font-family: inherit;
    z-index: 9999;
}

.onboarding-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5); /* Lighter overlay */
    z-index: 9000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.onboarding-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

.onboarding-content {
    transition: opacity 0.3s ease, transform 0.3s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    z-index: 9999;
    width: 450px;
    background-color: #1e293b; /* Darker blue gray */
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.onboarding-content.fade-out {
    opacity: 0;
    transform: scale(0.95);
}

.onboarding-highlight {
    position: relative;
    z-index: 9500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    border-radius: inherit;
    animation: pulse 2s infinite;
    transition: box-shadow 0.3s ease;
    background-color: rgba(30, 41, 59, 0.2) !important; /* Ensure highlighted element is visible */
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 8px rgba(59, 130, 246, 0.3);
    }
    100% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    }
}

.onboarding-connector {
    position: fixed;
    height: 2px;
    background-color: rgba(59, 130, 246, 1);
    transform-origin: left center;
    z-index: 9600;
    pointer-events: none;
    box-shadow: 0 0 5px 0 rgba(59, 130, 246, 0.7);
}

.onboarding-connector::after {
    content: '';
    position: absolute;
    right: -1px;
    top: -4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 1);
    animation: pulsePoint 2s infinite;
}

@keyframes pulsePoint {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

/* Onboarding styles */
#onboardingModal {
    font-family: inherit;
    z-index: 9999;
}

.onboarding-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3); /* Much lighter overlay */
    z-index: 9000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.onboarding-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

.onboarding-content {
    transition: opacity 0.3s ease, transform 0.3s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    z-index: 9999;
    width: 600px;
    background-color: #1e293b; /* Darker blue gray */
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.onboarding-content.fade-out {
    opacity: 0;
    transform: scale(0.95);
}

.onboarding-highlight {
    position: relative;
    z-index: 9500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    border-radius: inherit;
    animation: pulse 2s infinite;
    transition: box-shadow 0.3s ease;
    background-color: rgba(59, 130, 246, 0.05) !important; /* Very light blue instead of dark */
    filter: brightness(1.3) !important; /* Make the element brighter */
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 8px rgba(59, 130, 246, 0.3);
    }
    100% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    }
}

.onboarding-connector {
    position: fixed;
    height: 3px; /* Thicker line */
    background-color: rgba(59, 130, 246, 1);
    transform-origin: left center;
    z-index: 9600;
    pointer-events: none;
    box-shadow: 0 0 8px 2px rgba(59, 130, 246, 0.7); /* Stronger glow */
}

.onboarding-connector::after {
    content: '';
    position: absolute;
    right: -1px;
    top: -4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 1);
    animation: pulsePoint 2s infinite;
}

@keyframes pulsePoint {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

/* Onboarding styles */
#onboardingModal {
    font-family: inherit;
    z-index: 9999;
}

/* No dark overlay */
.onboarding-overlay {
    display: none !important;
}

.onboarding-content {
    transition: opacity 0.3s ease, transform 0.3s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    z-index: 9999;
    width: 600px;
    background-color: #1e293b; /* Darker blue gray */
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.onboarding-content.fade-out {
    opacity: 0;
    transform: scale(0.95);
}

.onboarding-highlight {
    position: relative;
    z-index: 9500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    border-radius: inherit;
    animation: pulse 2s infinite;
    transition: box-shadow 0.3s ease;
    background-color: rgba(59, 130, 246, 0.05) !important; /* Very light blue instead of dark */
    filter: brightness(1.3) !important; /* Make the element brighter */
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 8px rgba(59, 130, 246, 0.3);
    }
    100% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    }
}

.onboarding-connector {
    position: fixed;
    height: 3px; /* Thicker line */
    background-color: rgba(59, 130, 246, 1);
    transform-origin: left center;
    z-index: 9600;
    pointer-events: none;
    box-shadow: 0 0 8px 2px rgba(59, 130, 246, 0.7); /* Stronger glow */
}

.onboarding-connector::after {
    content: '';
    position: absolute;
    right: -1px;
    top: -4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 1);
    animation: pulsePoint 2s infinite;
}

@keyframes pulsePoint {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

/* Onboarding styles */
#onboardingModal {
    font-family: inherit;
    z-index: 9999;
}

/* Very light overlay, only used for first screen */
.onboarding-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.15); /* Very light overlay */
    z-index: 9000;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.onboarding-overlay.active {
    opacity: 1;
    pointer-events: auto;
}

.onboarding-content {
    transition: opacity 0.3s ease, transform 0.3s ease;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    z-index: 9999;
    width: 600px;
    background-color: #1e293b; /* Darker blue gray */
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.onboarding-content.fade-out {
    opacity: 0;
    transform: scale(0.95);
}

.onboarding-highlight {
    position: relative;
    z-index: 9500;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    border-radius: inherit;
    animation: pulse 2s infinite;
    transition: box-shadow 0.3s ease;
    background-color: rgba(59, 130, 246, 0.05) !important; /* Very light blue instead of dark */
    filter: brightness(1.3) !important; /* Make the element brighter */
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 8px rgba(59, 130, 246, 0.3);
    }
    100% {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 1), 0 0 15px 5px rgba(59, 130, 246, 0.5);
    }
}

.onboarding-connector {
    position: fixed;
    height: 3px; /* Thicker line */
    background-color: rgba(59, 130, 246, 1);
    transform-origin: left center;
    z-index: 9600;
    pointer-events: none;
    box-shadow: 0 0 8px 2px rgba(59, 130, 246, 0.7); /* Stronger glow */
}

.onboarding-connector::after {
    content: '';
    position: absolute;
    right: -1px;
    top: -4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(59, 130, 246, 1);
    animation: pulsePoint 2s infinite;
}

@keyframes pulsePoint {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 0 6px rgba(59, 130, 246, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
}

/* Mascot styling */
.mascot-container {
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    width: 150px;
    height: 150px;
    border-radius: 20px; /* Rounded corners for container */
}

.mascot-video {
    display: block;
    width: 150px;
    height: 150px;
    object-fit: contain;
    border-radius: 20px; /* Rounded corners for video */
}

/* Tooltip styling */
.onboarding-tooltip {
    position: absolute;
    background-color: #1e293b;
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(59, 130, 246, 0.5);
    z-index: 9700;
    max-width: 320px;
    border: 1px solid rgba(59, 130, 246, 0.5);
}

.onboarding-tooltip.faded {
    opacity: 0.8;
}

.tooltip-next {
    transition: background-color 0.2s;
}

.tooltip-skip,
.tooltip-close {
    transition: color 0.2s;
}

/* Make sure the tooltips are above modals in z-index */
.modal,
#settingsModal,
#launchModal {
    z-index: 9400 !important;
}

/* Fix for dropzone */
#dropzone.onboarding-highlight {
    border-color: rgba(59, 130, 246, 1) !important;
    filter: brightness(1.3) !important;
}

/* Fix for buttons */
#settingsToggle.onboarding-highlight,
#processingToggle.onboarding-highlight,
button.onboarding-highlight {
    filter: brightness(1.5) !important;
    background-color: rgba(59, 130, 246, 0.8) !important;
    color: white !important;
    border-color: rgba(59, 130, 246, 1) !important;
}

/* Make the "Привет!" text larger in the title */
#onboardingContent h3 {
    font-size: 1.75rem !important;
    font-weight: 600 !important;
}
