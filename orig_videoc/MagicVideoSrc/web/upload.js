class FileUploadManager {
    constructor() {
        this.initializeElements();
        if (this.elementsExist()) {
            this.init();
            this.loadExistingFiles();
        } else {
            console.error('Required elements not found in the DOM');
        }
    }

    initializeElements() {
        this.fileInput = document.getElementById('fileInput');
        this.dropzone = document.getElementById('dropzone');
        this.fileList = document.getElementById('fileList');
        this.fileItems = document.getElementById('fileItems');
        this.initialContent = document.getElementById('initialDropContent');
        this.selectFileButton = document.getElementById('selectFileButton');
        this.clearFilesButton = document.getElementById('clearFiles');
    }

    elementsExist() {
        return this.fileInput && this.dropzone && this.fileList &&
               this.fileItems && this.initialContent;
    }

    init() {
        // File selection button
        if (this.selectFileButton) {
            this.selectFileButton.addEventListener('click', (e) => {
                e.preventDefault();
                if (this.fileInput) {
                    this.fileInput.click();
                }
            });
        }

        // File input change handler
        if (this.fileInput) {
            this.fileInput.addEventListener('change', () => this.handleFileSelect());
        }

        // Clear files button
        if (this.clearFilesButton) {
            this.clearFilesButton.addEventListener('click', () => this.clearAllFiles());
        }

        // Global drag and drop
        if (this.dropzone) {
            document.body.addEventListener('dragover', (e) => this.handleDragOver(e));
            document.body.addEventListener('drop', (e) => this.handleDrop(e));
            document.body.addEventListener('dragleave', (e) => this.handleDragLeave(e));

            this.dropzone.addEventListener('dragover', (e) => this.handleDragOver(e));
            this.dropzone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            this.dropzone.addEventListener('drop', (e) => this.handleDrop(e));
        }
    }

    toggleInitialContent(hasFiles) {
        if (hasFiles) {
            this.initialContent.classList.add('hidden');
            this.fileList.classList.remove('hidden');
        } else {
            this.initialContent.classList.remove('hidden');
            this.fileList.classList.add('hidden');
        }
    }

    async clearAllFiles() {
        try {
            const response = await fetch('/clear_files', {
                method: 'POST'
            });

            const data = await response.json();
            if (data.status === 'files_cleared') {
                this.fileItems.innerHTML = '';
                this.toggleInitialContent(false);
                this.showNotification('Список файлов очищен', 'success');
            }
        } catch (error) {
            this.showNotification('Ошибка при очистке списка', 'error');
        }
    }

    async loadExistingFiles() {
        try {
            const response = await fetch('/get_existing_files');
            const data = await response.json();
            if (data.files && data.files.length > 0) {
                this.updateFileList(data.files);
            }
        } catch (error) {
            console.error('Error loading existing files:', error);
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        this.dropzone.classList.add('border-blue-500', 'bg-blue-500/10');
        document.body.style.opacity = '0.9';
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        if (!e.relatedTarget) {
            this.dropzone.classList.remove('border-blue-500', 'bg-blue-500/10');
            document.body.style.opacity = '1';
        }
    }

    handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        this.dropzone.classList.remove('border-blue-500', 'bg-blue-500/10');
        document.body.style.opacity = '1';
        const files = e.dataTransfer.files;
        if (files.length) {
            this.uploadFiles(files);
        }
    }

    handleFileSelect() {
        if (this.fileInput.files.length) {
            this.uploadFiles(this.fileInput.files);
        }
    }

    async uploadFiles(files) {
        const formData = new FormData();
        let hasVideoFiles = false;

        for (let file of files) {
            if (file.type.startsWith('video/')) {
                formData.append('files[]', file);
                hasVideoFiles = true;
            }
        }

        if (!hasVideoFiles) {
            this.showNotification('Пожалуйста, выберите видео файлы', 'error');
            return;
        }

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            if (data.success) {
                this.updateFileList(data.files);
                this.showNotification('Файлы успешно загружены', 'success');
            }
        } catch (error) {
            this.showNotification('Ошибка при загрузке файлов', 'error');
        }
    }

    updateFileList(files) {
        this.toggleInitialContent(files.length > 0);

        if (files.length > 0) {
            this.fileList.innerHTML = `
                <div class="file-list-wrapper">
                    <!-- Header section -->
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Выбранные файлы</h3>
                        <button id="clearFiles" class="text-red-400 hover:text-red-300 flex items-center gap-2">
                            <i data-lucide="trash-2"></i>
                            Очистить все
                        </button>
                    </div>

                    <!-- Top blur overlay -->
                    <div class="blur-overlay-top"></div>

                    <!-- Scrollable content -->
                    <div class="file-list-content">
                        <div id="fileItems" class="space-y-3">
                            ${files.map(file => `
                                <div class="p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <i data-lucide="film" class="w-6 h-6 text-gray-400"></i>
                                            <span class="text-lg">${file}</span>
                                        </div>
                                        <button class="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-gray-600 transition-colors"
                                                onclick="fileUploadManager.removeFile('${file}')">
                                            <i data-lucide="trash-2" class="w-5 h-5"></i>
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Bottom blur overlay -->
                    <div class="blur-overlay-bottom"></div>

                    <!-- Add button -->
                    <div class="add-file-button" onclick="document.getElementById('fileInput').click()">
                        <i data-lucide="plus-circle" class="w-8 h-8 text-gray-400 hover:text-gray-300"></i>
                    </div>
                </div>
            `;

            // Reinitialize the clear files button
            this.clearFilesButton = document.getElementById('clearFiles');
            if (this.clearFilesButton) {
                this.clearFilesButton.addEventListener('click', () => this.clearAllFiles());
            }

            // Update Lucide icons
            lucide.createIcons();
        } else {
            this.fileList.classList.add('hidden');
        }
    }

    async removeFile(filename) {
        try {
            const response = await fetch('/remove_file', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ filename })
            });

            const data = await response.json();
            if (data.success) {
                this.updateFileList(data.files);
                this.showNotification('Файл удален', 'success');
            }
        } catch (error) {
            this.showNotification('Ошибка при удалении файла', 'error');
        }
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg text-white
                                ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}
                                transform transition-all duration-300 translate-y-0 z-50`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('translate-y-full', 'opacity-0');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}


class LicenseManager {
    constructor() {
        this.updateLicenseInfo();
    }

    async updateLicenseInfo() {
        try {
            const response = await fetch('/get_license_info');
            const data = await response.json();

            // Обновляем тип лицензии
            const licenseType = document.getElementById('licenseType');
            licenseType.textContent = data.type;
            licenseType.classList.remove('skeleton-loader');

            // Обновляем статус
            const licenseStatus = document.getElementById('licenseStatus');
            const isActive = data.status.includes('Активна');
            licenseStatus.innerHTML = `
                <span class="${isActive ? 'text-green-400' : ''} flex items-center justify-end gap-1">
                    ${data.status}
                    ${isActive ? '<i data-lucide="check" class="w-4 h-4 mt-1"></i>' : ''}
                </span>
            `;
            licenseStatus.classList.remove('skeleton-loader');

            // Обновляем срок действия
            const licenseExpiry = document.getElementById('licenseExpiry');
            licenseExpiry.textContent = data.expiry_date;
            licenseExpiry.classList.remove('skeleton-loader');

            // Обновляем доступные функции
            const licenseFeatures = document.getElementById('licenseFeatures');
            licenseFeatures.textContent = data.features;
            licenseFeatures.classList.remove('skeleton-loader');

            // Обновляем иконки Lucide
            lucide.createIcons();
        } catch (error) {
            console.error('Error updating license info:', error);
        }
    }
}

// Измените инициализацию в конце файла на:
let fileUploadManager;
let licenseManager;
document.addEventListener('DOMContentLoaded', () => {
    fileUploadManager = new FileUploadManager();
    licenseManager = new LicenseManager();
});