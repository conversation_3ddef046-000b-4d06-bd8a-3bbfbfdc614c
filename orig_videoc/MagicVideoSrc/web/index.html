<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>MagicUniq</title>
    <link rel="stylesheet" href="{{config.base_url}}/tailwind.min.css">
    <link rel="stylesheet" href="{{config.base_url}}/styles.css">
    <link type="image/x-icon" href="{{config.base_url}}/favicon.ico" rel="shortcut icon">
</head>
<body class="bg-gradient-to-b from-gray-900 to-gray-800 text-white min-h-screen">
    <div class="container mx-auto max-w-6xl p-8">
    <!-- Header with adjusted layout -->
    <div class="flex justify-between items-center mb-8">
        <!-- Logo on the left -->
        <h1 class="text-3xl py-2 font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600">
            <div class="flex items-center">
                <img src="http://magicuniq.space/static/logo.png" alt="MagicUniq icon" class="w-11 h-11 mt-0.5 mr-1">
                MagicUniq
            </div>
        </h1>
        <button id="settingsToggle" class="flex items-center gap-2 px-4 py-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors">
            <i data-lucide="settings" class="w-5 h-5"></i>Настройки
        </button>
    </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- File Upload -->
            <div class="lg:col-span-2">
                <div id="dropzone" class="border-2 border-dashed rounded-xl p-8 text-center transition-colors border-gray-600 hover:border-gray-500">
                    <input type="file" id="fileInput" multiple accept="video/*" class="hidden">

                    <div id="initialDropContent" class="initial-content">
                        <i data-lucide="film" class="w-16 h-16 mx-auto mb-4 text-gray-400"></i>
                        <h3 class="text-xl font-semibold mb-2">Перетащите ваши видео сюда</h3>
                        <p class="text-gray-400 mb-4">или</p>
                        <button id="selectFileButton" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                            Выберите файл
                        </button>
                    </div>

                    <!-- File List -->
                    <div id="fileList" class="bg-gray-800/50 rounded-xl p-4 {% if not state.selected_files %}hidden{% endif %}">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold">Выбранные файлы</h3>
                            <button id="clearFiles"
                                    class="text-red-400 hover:text-red-300 flex items-center gap-2">
                                <i data-lucide="trash-2"></i>
                                Очистить все
                            </button>
                        </div>
                        <div id="fileItems" class="space-y-3">
                            {% for file in state.selected_files %}
                            <div class="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg">
                                <div class="flex items-center gap-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                    </svg>
                                    <span>{{ file }}</span>
                                </div>
                                <button class="text-red-400 hover:text-red-300">🗑️</button>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Processing Controls -->
            <div class="space-y-6">
                <div class="bg-gray-800/50 rounded-xl p-6">
                    <div class="flex items-center gap-2 mb-6">
                        <h3 class="text-xl font-bold">Лицензия</h3>
                        <span class="text-xl font-medium text-blue-400 skeleton-loader" id="licenseType"></span>
                    </div>
                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between items-center text-gray-300">
                            <span>Статус:</span>
                            <span class="skeleton-loader h-6 w-24 text-right" id="licenseStatus"></span>
                        </div>
                        <div class="flex justify-between items-center text-gray-300">
                            <span>Срок действия:</span>
                            <span class="skeleton-loader h-6 w-28 text-right" id="licenseExpiry"></span>
                        </div>
                        <div class="flex justify-between items-center text-gray-300">
                            <span>Доступные функции:</span>
                            <span class="skeleton-loader h-6 w-16 text-right" id="licenseFeatures"></span>
                        </div>
                    </div>
                    <button id="processingToggle" class="w-full flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                        <i data-lucide="play-circle" class="w-5 h-5"></i>
                        Запуск
                    </button>
                </div>
            </div>
        </div>

        <div id="settingsModal" class="fixed inset-0 items-center justify-center hidden z-50">
            <!-- Backdrop -->
            <div class="absolute inset-0 bg-black/70 transition-all duration-300 modal-backdrop"></div>

            <!-- Modal container -->
            <div class="relative bg-gray-800 rounded-xl w-[90vw] h-[90vh] m-auto flex flex-col overflow-hidden transition-all duration-300 modal-dialog">
                <!-- Modal Content -->
                <div class="flex h-full modal-content">
                    <!-- Sidebar with Parameters -->
                    <div class="w-72 flex flex-col bg-gray-900/30">
                        <!-- Fixed Header -->
                        <div class="p-6 pb-4">
                            <h2 class="text-2xl font-semibold">Параметры</h2>
                        </div>

                        <!-- ADD THE CONFIG WRAPPER HERE - right after the header div -->
                        <div class="config-wrapper mx-6 mb-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-200">Конфигурация</h3>
                                <div class="flex gap-2">
                                    <button id="newConfigBtn" class="icon-btn" title="Создать новую конфигурацию">
                                        <i data-lucide="plus" class="w-4 h-4"></i>
                                    </button>
                                    <button id="configSettingsBtn" class="icon-btn" title="Управление конфигурациями">
                                        <i data-lucide="settings" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mt-3 config-select-container">
                                <select id="configSelect" class="config-select">
                                    <option value="default">Default</option>
                                    <!-- Other configs will be added dynamically -->
                                </select>
                                <i data-lucide="chevron-down" class="config-select-arrow"></i>
                            </div>
                        </div>
                        <!-- Scrollable Parameters List -->
                        <div class="flex-1 overflow-y-auto no-scrollbar px-6 pt-2">
                            <div id="settingsTabs" class="space-y-2 pb-6">
                                <!-- Tabs will be inserted here -->
                            </div>
                        </div>
                    </div>

                    <!-- Main Content Area -->
                    <div class="flex-1 flex flex-col bg-gray-800">
                        <!-- Fixed Header -->
                        <div class="p-6 pb-4">
                            <h2 class="text-2xl font-semibold">Настройки</h2>
                        </div>
                        <!-- Scrollable Settings Content -->
                        <div id="settingsContent" class="flex-1 overflow-y-auto settings-scrollbar px-6">
                            <!-- Settings content will be inserted here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="launchModal" class="fixed inset-0 items-center justify-center hidden z-50">
            <div class="absolute inset-0 bg-black/70 transition-all duration-300 modal-backdrop"></div>

            <div class="relative bg-gray-800 rounded-xl launch-content m-auto flex flex-col overflow-hidden transition-all duration-300 modal-dialog">
                <!-- Header -->
                <div class="p-6 pb-4 flex justify-between items-center">
                    <h2 class="text-2xl font-semibold" id="launchModalTitle">
                        Параметры запуска
                    </h2>
                    <button class="text-gray-400 hover:text-gray-300 transition-colors" id="closeLaunchModal">
                        <i data-lucide="x" class="w-6 h-6"></i>
                    </button>
                </div>

                <!-- Launch Settings Content -->
                <div id="launchSettings" class="flex-1 px-6 py-4">
                    <div class="bg-gray-900/30 rounded-lg p-6">
                        <div class="space-y-6">
                            <!-- Videos count slider -->
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-lg text-gray-300">Количество итераций</span>
                                    <span class="text-sm font-mono text-gray-300" id="videosValue"></span>
                                </div>
                                <div class="range-slider relative pt-1 pb-4">
                                    <div class="range-track"></div>
                                    <div class="range-selected single-border"></div>
                                    <input type="range"
                                           id="videosSlider"
                                           min="1"
                                           max="1000"
                                           step="1"
                                           value="1"
                                           class="absolute z-20">
                                </div>
                            </div>

                            <!-- Threads count slider -->
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-lg text-gray-300">Количество потоков</span>
                                    <span class="text-sm font-mono text-gray-300" id="threadsValue"></span>
                                </div>
                                <div class="range-slider relative pt-1 pb-4">
                                    <div class="range-track"></div>
                                    <div class="range-selected single-border"></div>
                                    <input type="range"
                                           id="threadsSlider"
                                           min="1"
                                           max="16"
                                           step="1"
                                           value="1"
                                           class="absolute z-20">
                                </div>
                                <div id="threadsWarning" class="hidden text-red-500 text-sm mt-2 flex items-center gap-2">
                                    <i data-lucide="alert-triangle" class="w-4 h-4"></i>
                                    <span>Большое количество потоков может сильно нагрузить компьютер</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Content (initially hidden) -->
                <div id="progressContent" class="flex-1 px-6 py-4 hidden opacity-0 transition-opacity duration-300">
                    <div class="space-y-6">
                        <!-- Total Progress -->
<!--                        <div class="space-y-2">-->
<!--                            <div class="flex justify-between items-center">-->
<!--                                <span class="text-sm text-gray-300">Общий прогресс</span>-->
<!--                                <span class="text-sm font-mono text-gray-300" id="totalProgressValue">0 / 0</span>-->
<!--                            </div>-->
<!--                            <div class="h-2 bg-gray-700 rounded-full overflow-hidden">-->
<!--                                <div id="totalProgressBar" class="h-full bg-blue-500 transition-all duration-300" style="width: 0%"></div>-->
<!--                            </div>-->
<!--                        </div>-->

                        <!-- Thread Progress Bars Container -->
                        <div id="threadProgressBars" class="space-y-4"></div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="p-6">
                    <!-- Start button (visible initially) -->
                    <button id="startProcessing"
                            class="w-full flex items-center justify-center gap-2 px-6 py-4 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-lg font-semibold">
                        <i data-lucide="play-circle" class="w-6 h-6"></i>
                        Запуск
                    </button>

                    <!-- Stop button (initially hidden) -->
                    <button id="stopProcessing"
                            class="hidden w-full flex items-center justify-center gap-2 px-6 py-4 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-lg font-semibold">
                        <i data-lucide="stop-circle" class="w-6 h-6"></i>
                        Остановить
                    </button>
                    <!-- Complete button (initially hidden) -->
                    <button id="completeCloseButton"
                            class="hidden w-full flex items-center justify-center gap-2 px-6 py-4 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-lg font-semibold">
                        <i data-lucide="x-circle" class="w-6 h-6"></i>
                        Закрыть окно
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Management Modal -->
    <div id="configModal" class="modal hidden">
        <div class="modal-backdrop"></div>
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">Управление конфигурациями</h3>
                <button id="closeConfigModal" class="modal-close">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="config-list-container settings-scrollbar pr-2">
                    <div id="configsList" class="config-list">
                        <!-- Configs will be listed here dynamically -->
                    </div>
                </div>

                <div class="mt-4">
                    <button id="createConfigBtn" class="primary-btn w-full">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Создать новую конфигурацию
                    </button>

                    <div class="mt-2 gap-2">
                        <button id="importConfigBtn" class="secondary-btn w-full flex items-center justify-center">
                            <i data-lucide="upload" class="w-4 h-4 mr-2"></i>
                            Импорт
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rename Configuration Modal -->
    <div id="renameModal" class="modal hidden">
        <div class="modal-backdrop"></div>
        <div class="modal-container modal-sm">
            <div class="modal-header">
                <h3 class="modal-title">Переименовать конфигурацию</h3>
                <button id="closeRenameModal" class="modal-close">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>

            <div class="modal-body">
                <input type="hidden" id="configToRename">
                <div class="mb-4">
                    <label for="newConfigName" class="block text-gray-300 mb-2">Новое название:</label>
                    <input type="text" id="newConfigName" class="input-field w-full">
                </div>

                <div class="flex justify-end gap-3">
                    <button id="cancelRename" class="secondary-btn">Отмена</button>
                    <button id="confirmRename" class="primary-btn">Сохранить</button>
                </div>
            </div>
        </div>
    </div>

    <div id="createConfigModal" class="modal hidden">
        <div class="modal-backdrop"></div>
        <div class="modal-container modal-sm">
            <div class="modal-header">
                <h3 class="modal-title">Создать новую конфигурацию</h3>
                <button id="closeCreateConfigModal" class="modal-close">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="mb-4">
                    <label for="newConfigNameCreate" class="block text-gray-300 mb-2">Название конфигурации:</label>
                    <input type="text" id="newConfigNameCreate" class="input-field w-full">
                </div>

                <div class="flex justify-end gap-3">
                    <button id="cancelCreateConfig" class="secondary-btn">Отмена</button>
                    <button id="confirmCreateConfig" class="primary-btn">Сохранить</button>
                </div>
            </div>
        </div>
    </div>
    <script src="{{config.base_url}}/lucide.js"></script>
    <script src="{{config.base_url}}/settings.js"></script>
    <script src="{{config.base_url}}/upload.js"></script>
    <script src="{{config.base_url}}/launch.js"></script>
    <script src="{{config.base_url}}/config-manager.js"></script>
    <script src="{{config.base_url}}/onboarding.js"></script>
<input type="file" id="configFileInput" class="hidden" accept=".ini">
</body>
</html>
