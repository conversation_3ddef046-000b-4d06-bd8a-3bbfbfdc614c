class ConfigurationManager {
  constructor() {
    this.configurations = [];
    this.currentConfig = 'default';
    this.initElements();
    this.loadConfigurations();
    this.setupEventListeners();
  }

  initElements() {
    // Modal selector elements
    this.configSelect = document.getElementById('configSelect');
    this.newConfigBtn = document.getElementById('newConfigBtn');
    this.configSettingsBtn = document.getElementById('configSettingsBtn');

    // Main menu selector elements
    this.mainConfigSelect = document.getElementById('mainConfigSelect');
    this.mainNewConfigBtn = document.getElementById('mainNewConfigBtn');
    this.mainConfigSettingsBtn = document.getElementById('mainConfigSettingsBtn');

    // Modal elements
    this.configModal = document.getElementById('configModal');
    this.closeConfigModalBtn = document.getElementById('closeConfigModal');
    this.configsList = document.getElementById('configsList');
    this.createConfigBtn = document.getElementById('createConfigBtn');
    this.exportConfigBtn = document.getElementById('exportConfigBtn');
    this.importConfigBtn = document.getElementById('importConfigBtn');
    this.configFileInput = document.getElementById('configFileInput');

    // Rename modal elements
    this.renameModal = document.getElementById('renameModal');
    this.closeRenameModalBtn = document.getElementById('closeRenameModal');
    this.configToRename = document.getElementById('configToRename');
    this.newConfigName = document.getElementById('newConfigName');
    this.cancelRename = document.getElementById('cancelRename');
    this.confirmRename = document.getElementById('confirmRename');

    // Create config modal elements
    this.createConfigModal = document.getElementById('createConfigModal');
    this.closeCreateConfigModalBtn = document.getElementById('closeCreateConfigModal');
    this.newConfigNameCreate = document.getElementById('newConfigNameCreate');
    this.cancelCreateConfig = document.getElementById('cancelCreateConfig');
    this.confirmCreateConfig = document.getElementById('confirmCreateConfig');
  }

  async loadConfigurations() {
    try {
      const response = await fetch('/list_configs');
      const data = await response.json();

      if (response.ok) {
        this.configurations = data.configs || [];
        this.currentConfig = data.current || 'default';
        this.updateConfigSelects();
        this.renderConfigList();
      }
    } catch (error) {
      console.error('Error loading configurations:', error);
      this.showNotification('Ошибка при загрузке конфигураций', 'error');
    }
  }

  updateConfigSelects() {
    // Update both select elements (in settings and main menu)
    this.updateSelectElement(this.configSelect);
    this.updateSelectElement(this.mainConfigSelect);
  }

  updateSelectElement(selectElement) {
    if (!selectElement) return;

    selectElement.innerHTML = '';

    this.configurations.forEach(config => {
      const option = document.createElement('option');
      option.value = config.name;
      option.textContent = config.name;
      option.selected = config.name === this.currentConfig;
      selectElement.appendChild(option);
    });
  }

  renderConfigList() {
    if (!this.configsList) return;

    this.configsList.innerHTML = '';

    this.configurations.forEach(config => {
      const item = document.createElement('div');
      item.className = `config-item ${config.name === this.currentConfig ? 'active' : ''}`;
      item.setAttribute('data-config-name', config.name);

      // Make the entire item clickable to select the configuration
      item.addEventListener('click', (e) => {
        // Only switch if clicking on the item itself, not on buttons or input fields
        if (e.target === item ||
            e.target.classList.contains('config-item-name') ||
            e.target.classList.contains('name-container') ||
            e.target === item.querySelector('.name-container')) {
          this.switchConfig(config.name);
        }
      });

      // Name container with edit button
      const nameContainer = document.createElement('div');
      nameContainer.className = 'name-container flex items-center';

      const name = document.createElement('span');
      name.className = 'config-item-name';
      name.textContent = config.name;

      // Edit button next to name
      const editBtn = document.createElement('button');
      editBtn.className = 'rename-btn ml-1';
      editBtn.title = 'Переименовать';
      editBtn.innerHTML = '<i data-lucide="pencil" class="w-4 h-4"></i>';
      editBtn.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent item click
        this.startInlineRename(config.name, name);
      });

      nameContainer.appendChild(name);
      nameContainer.appendChild(editBtn);

      if (config.name === this.currentConfig) {
        const badge = document.createElement('span');
        badge.className = 'config-item-badge ml-2';
        badge.textContent = 'Активна';
        nameContainer.appendChild(badge);
      }

      const actions = document.createElement('div');
      actions.className = 'config-item-actions flex items-center gap-2';

      // Export button for each config
      const exportBtn = document.createElement('button');
      exportBtn.className = 'icon-btn';
      exportBtn.title = 'Экспорт';
      exportBtn.innerHTML = '<i data-lucide="download" class="w-4 h-4"></i>';
      exportBtn.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent item click
        this.exportConfig(config.name);
      });
      actions.appendChild(exportBtn);

      // Delete button (disabled for default)
      if (config.name !== 'default') {
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'icon-btn text-red-400 hover:text-red-300';
        deleteBtn.title = 'Удалить';
        deleteBtn.innerHTML = '<i data-lucide="trash-2" class="w-4 h-4"></i>';
        deleteBtn.addEventListener('click', (e) => {
          e.stopPropagation(); // Prevent item click
          this.deleteConfig(config.name);
        });
        actions.appendChild(deleteBtn);
      }

      item.appendChild(nameContainer);
      item.appendChild(actions);
      this.configsList.appendChild(item);
    });

    // Add CSS for the config items if not already in styles.css
    if (!document.getElementById('config-item-styles')) {
      const style = document.createElement('style');
      style.id = 'config-item-styles';
      style.textContent = `
        .config-item {
          cursor: pointer;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem;
          border-radius: 0.5rem;
          margin-bottom: 0.5rem;
          background-color: rgba(75, 85, 99, 0.2);
        }
        .config-item:hover {
          background-color: rgba(75, 85, 99, 0.3);
        }
        .config-item.active {
          background-color: rgba(59, 130, 246, 0.2);
          border: 1px solid rgba(59, 130, 246, 0.5);
        }
        .config-inline-edit {
          background-color: rgba(55, 65, 81, 0.8);
          border: 1px solid rgba(75, 85, 99, 0.5);
          border-radius: 0.25rem;
          padding: 0.25rem 0.5rem;
          color: white;
          font-size: 0.875rem;
        }
        .config-inline-edit:focus {
          outline: none;
          border-color: rgba(59, 130, 246, 0.8);
        }
      `;
      document.head.appendChild(style);
    }

    // Initialize Lucide icons
    lucide.createIcons();
  }

  startInlineRename(configName, nameElement) {
    // Create an input field for inline editing
    const input = document.createElement('input');
    input.type = 'text';
    input.value = configName;
    input.className = 'config-inline-edit';
    input.style.width = `${nameElement.offsetWidth + 30}px`;

    // Replace the name span with the input
    const parent = nameElement.parentNode;
    parent.replaceChild(input, nameElement);

    // Focus the input
    input.focus();
    input.select();

    // Handle enter key and blur events
    const handleRename = () => {
      const newName = input.value.trim();
      if (newName && newName !== configName) {
        this.renameConfig(configName, newName);
      } else {
        // Just restore the original element if no change
        parent.replaceChild(nameElement, input);
      }
    };

    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        handleRename();
      } else if (e.key === 'Escape') {
        parent.replaceChild(nameElement, input);
      }
    });

    input.addEventListener('blur', handleRename);
  }

  setupEventListeners() {
    // Settings modal selector events
    if (this.configSelect) {
      this.configSelect.addEventListener('change', () => {
        this.switchConfig(this.configSelect.value);
      });
    }

    if (this.newConfigBtn) {
      this.newConfigBtn.addEventListener('click', () => this.createNewConfig());
    }

    if (this.configSettingsBtn) {
      this.configSettingsBtn.addEventListener('click', () => this.openConfigModal());
    }

    // Main menu selector events
    if (this.mainConfigSelect) {
      this.mainConfigSelect.addEventListener('change', () => {
        this.switchConfig(this.mainConfigSelect.value);
      });
    }

    if (this.mainNewConfigBtn) {
      this.mainNewConfigBtn.addEventListener('click', () => this.createNewConfig());
    }

    if (this.mainConfigSettingsBtn) {
      this.mainConfigSettingsBtn.addEventListener('click', () => this.openConfigModal());
    }

    // Modal events
    if (this.closeConfigModalBtn) {
      this.closeConfigModalBtn.addEventListener('click', () => this.closeConfigModal());
    }

    if (this.createConfigBtn) {
      this.createConfigBtn.addEventListener('click', () => this.createNewConfig());
    }

    // Import/Export events
    if (this.exportConfigBtn) {
      this.exportConfigBtn.addEventListener('click', () => this.exportConfig(this.currentConfig));
    }

    if (this.importConfigBtn) {
      this.importConfigBtn.addEventListener('click', () => this.importConfig());
    }

    if (this.configFileInput) {
      this.configFileInput.addEventListener('change', (e) => this.handleFileImport(e));
    }

    // Rename modal events
    if (this.closeRenameModalBtn) {
      this.closeRenameModalBtn.addEventListener('click', () => this.closeRenameModal());
    }

    if (this.cancelRename) {
      this.cancelRename.addEventListener('click', () => this.closeRenameModal());
    }

    if (this.confirmRename) {
      this.confirmRename.addEventListener('click', () => this.renameConfigFromModal());
    }

    // Create config modal events
    if (this.closeCreateConfigModalBtn) {
      this.closeCreateConfigModalBtn.addEventListener('click', () => this.closeCreateConfigModal());
    }

    if (this.cancelCreateConfig) {
      this.cancelCreateConfig.addEventListener('click', () => this.closeCreateConfigModal());
    }

    if (this.confirmCreateConfig) {
      this.confirmCreateConfig.addEventListener('click', () => this.saveNewConfig());
    }

    // Modal backdrop clicks
    document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
      backdrop.addEventListener('click', (e) => {
        if (e.target === backdrop) {
          this.closeAllModals();
        }
      });
    });
  }

  openConfigModal() {
    this.renderConfigList();
    this.configModal.classList.remove('hidden');
  }

  closeConfigModal() {
    this.configModal.classList.add('hidden');
  }

  openRenameModal(configName) {
    this.configToRename.value = configName;
    this.newConfigName.value = configName;
    this.renameModal.classList.remove('hidden');
    this.newConfigName.focus();
    this.newConfigName.select();
  }

  closeRenameModal() {
    this.renameModal.classList.add('hidden');
  }

  openCreateConfigModal() {
    this.newConfigNameCreate.value = this.generateRandomCamelCaseName();
    this.createConfigModal.classList.remove('hidden');
    this.newConfigNameCreate.focus();
    this.newConfigNameCreate.select();
  }

  closeCreateConfigModal() {
    this.createConfigModal.classList.add('hidden');
  }

  closeAllModals() {
    this.configModal.classList.add('hidden');
    this.renameModal.classList.add('hidden');
    this.createConfigModal.classList.add('hidden');
  }

  generateRandomCamelCaseName() {
    const adjectives = [
      'happy', 'brave', 'calm', 'wise', 'swift', 'gentle', 'bright', 'smart', 'bold',
      'quick', 'eager', 'fair', 'kind', 'proud', 'warm', 'rich', 'deep', 'pure',
      'fresh', 'grand', 'clear', 'sweet', 'cool', 'wild', 'solid', 'smooth', 'soft'
    ];

    const nouns = [
      'tiger', 'river', 'ocean', 'flame', 'cloud', 'eagle', 'diamond', 'forest',
      'sunset', 'valley', 'garden', 'planet', 'falcon', 'castle', 'harbor', 'breeze',
      'summit', 'desert', 'meadow', 'lagoon', 'maple', 'panda', 'lotus', 'island',
      'canyon', 'zenith', 'nebula', 'quasar'
    ];

    // Select random words
    const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];

    // Capitalize first letter of each word
    const capitalizedAdjective = randomAdjective.charAt(0).toUpperCase() + randomAdjective.slice(1);
    const capitalizedNoun = randomNoun.charAt(0).toUpperCase() + randomNoun.slice(1);

    // Return combined CamelCase name
    return capitalizedAdjective + capitalizedNoun;
  }

  createNewConfig() {
    this.openCreateConfigModal();
  }

  async saveNewConfig() {
    const newName = this.newConfigNameCreate.value.trim();

    if (!newName) {
      this.showNotification('Название конфигурации не может быть пустым', 'error');
      return;
    }

    try {
      const response = await fetch('/create_config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newName })
      });

      const data = await response.json();

      if (data.success) {
        this.showNotification('Новая конфигурация создана', 'success');
        this.closeCreateConfigModal();

        // Switch to the new config
        this.switchConfig(newName);
      } else {
        throw new Error(data.error || 'Failed to create configuration');
      }
    } catch (error) {
      console.error('Error creating configuration:', error);
      this.showNotification('Ошибка при создании конфигурации', 'error');
    }
  }

  async switchConfig(configName, callback) {
    if (configName === this.currentConfig) {
      if (callback) callback();
      return;
    }

    try {
      const response = await fetch('/switch_config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: configName })
      });

      const data = await response.json();

      if (data.success) {
        this.currentConfig = configName;
        this.showNotification(`Переключено на конфигурацию: ${configName}`, 'success');

        // Reload the list and select
        await this.loadConfigurations();

        // Reload the current settings view
        if (window.settingsManager && window.settingsManager.currentTab) {
          window.settingsManager.showCategory(window.settingsManager.currentTab);
        }

        if (callback) callback();
      } else {
        throw new Error(data.error || 'Failed to switch configuration');
      }
    } catch (error) {
      console.error('Error switching configuration:', error);
      this.showNotification('Ошибка при переключении конфигурации', 'error');
    }
  }

  async renameConfig(oldName, newName) {
    if (!newName || oldName === newName) {
      return false;
    }

    try {
      const response = await fetch('/rename_config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ old_name: oldName, new_name: newName })
      });

      // First check if the HTTP request was successful
      if (!response.ok) {
        throw new Error(`HTTP error: ${response.status}`);
      }

      // Then parse the JSON
      const data = await response.json();
      console.log("Rename response:", data);

      // Check the success flag
      if (data && data.success === true) {
        // Success path
        this.showNotification(`Конфигурация переименована в ${newName}`, 'success');
        await this.loadConfigurations();
        return true;
      } else {
        // Error in the API response
        throw new Error(data.error || 'Server reported failure during rename operation');
      }
    } catch (error) {
      console.error('Error renaming configuration:', error);
      this.showNotification('Ошибка при переименовании конфигурации', 'error');

      // Refresh the list anyway since the backend rename might have succeeded
      await this.loadConfigurations();
      return false;
    }
  }

  renameConfigFromModal() {
    const oldName = this.configToRename.value;
    const newName = this.newConfigName.value.trim();

    this.closeRenameModal();
    if (newName && oldName !== newName) {
      this.renameConfig(oldName, newName);
    }
  }

  // Helper method to check if a config exists
  async checkIfConfigExists(configName) {
    try {
      const response = await fetch('/list_configs');
      const data = await response.json();

      if (response.ok && data.configs) {
        return data.configs.some(config => config.name === configName);
      }
      return false;
    } catch (error) {
      console.error("Error checking if config exists:", error);
      return false;
    }
  }

  async deleteConfig(configName) {
    if (!confirm(`Вы уверены, что хотите удалить конфигурацию "${configName}"?`)) {
      return;
    }

    try {
      const response = await fetch('/delete_config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: configName })
      });

      const data = await response.json();

      if (data.success) {
        this.showNotification(`Конфигурация ${configName} удалена`, 'success');
        await this.loadConfigurations();
      } else {
        throw new Error(data.error || 'Failed to delete configuration');
      }
    } catch (error) {
      console.error('Error deleting configuration:', error);
      this.showNotification('Ошибка при удалении конфигурации', 'error');
    }
  }

  async exportConfig(configName) {
    // Use the specific config name or default to current config
    const name = configName || this.currentConfig;

    try {
      // Get the config content from the server
      const response = await fetch(`/export_config?name=${encodeURIComponent(name)}`);
      if (!response.ok) {
        throw new Error(`Failed to export: ${response.status} ${response.statusText}`);
      }

      const content = await response.text();

      // Check if File System Access API is supported
      if (window.showSaveFilePicker) {
        try {
          // Configure options for the save dialog
          const options = {
            suggestedName: `${name}.ini`,
            types: [{
              description: 'Configuration Files',
              accept: {'text/plain': ['.ini']}
            }]
          };

          // Show the save dialog
          const fileHandle = await window.showSaveFilePicker(options);

          // Create a writable stream
          const writable = await fileHandle.createWritable();

          // Write the content
          await writable.write(content);

          // Close the file
          await writable.close();

          this.showNotification(`Конфигурация ${name} экспортирована`, 'success');
        } catch (fsError) {
          if (fsError.name === 'AbortError') {
            // User cancelled the save dialog
            return;
          }

          // If any other error occurred with the File System API, fall back to the traditional method
          console.warn('Error using File System Access API, falling back to traditional download:', fsError);
          this.fallbackExport(name, content);
        }
      } else {
        // File System Access API not supported, use traditional method
        this.fallbackExport(name, content);
      }
    } catch (error) {
      console.error('Error exporting config:', error);
      this.showNotification('Ошибка при экспорте конфигурации', 'error');
    }
  }

  // Traditional download method as fallback
  fallbackExport(name, content) {
    const blob = new Blob([content], {type: 'text/plain'});
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = `${name}.ini`;

    // Append, click, and remove the link to trigger browser's save dialog
    document.body.appendChild(a);
    a.click();

    // Clean up
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    this.showNotification(`Конфигурация ${name} экспортирована`, 'success');
  }

  importConfig() {
    // Ensure the file input exists before trying to click it
    if (!this.configFileInput) {
      // Create the file input if it doesn't exist
      this.configFileInput = document.createElement('input');
      this.configFileInput.type = 'file';
      this.configFileInput.accept = '.ini';
      this.configFileInput.style.display = 'none';
      this.configFileInput.addEventListener('change', (e) => this.handleFileImport(e));
      document.body.appendChild(this.configFileInput);
    }

    // Trigger the file input dialog by clicking the hidden input
    this.configFileInput.click();
  }

  handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Check if it's an INI file
    if (!file.name.toLowerCase().endsWith('.ini')) {
      this.showNotification('Пожалуйста, выберите файл .ini', 'error');
      return;
    }

    // Create a FormData object to send the file
    const formData = new FormData();
    formData.append('config_file', file);

    // Send to backend
    fetch('/import_config', {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        this.showNotification(`Конфигурация ${data.name} импортирована`, 'success');
        this.loadConfigurations();

        // Switch to the imported config
        if (data.name) {
          this.switchConfig(data.name);
        }
      } else {
        throw new Error(data.error || 'Failed to import configuration');
      }
    })
    .catch(error => {
      console.error('Error importing config:', error);
      this.showNotification('Ошибка при импорте конфигурации', 'error');
    });

    // Reset file input
    event.target.value = '';
  }

  showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg text-white z-50
                            ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}
                            transform transition-all duration-300`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.classList.add('translate-y-10', 'opacity-0');
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }
}

// Initialize the configuration manager
document.addEventListener('DOMContentLoaded', () => {
  window.configManager = new ConfigurationManager();
});
