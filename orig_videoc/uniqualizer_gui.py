import sys
import os
import json
from PyQt6.QtWidgets import (QA<PERSON>lication, QMainWindow, QW<PERSON>t, QVBoxLayout,
                            QHBoxLayout, QLabel, QSpinBox, QDoubleSpinBox,
                            QPushButton, QProgressBar, QCheckBox, QFileDialog,
                            QScrollArea, QGroupBox, QGridLayout, QStyle,
                            QStyleFactory,QMessageBox, QComboBox,QRadioButton,
                            QLineEdit,QFrame,QDialog,QSizePolicy,QListWidget,
                            QListWidgetItem)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QSize
from PyQt6.QtGui import QPalette, QColor, QFont, QPixmap, QIcon
import tempfile
import multiprocessing
from uniqualizer import UniqualizerConfig, process_image_thread, NoBackgroundsError, create_slideshow_from_photos
import aiohttp
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time
import threading

import subprocess

match 0:
    case {"id": id} if id > 100:
        pass
    case _:
        pass


class ImageProcessingThread(QThread):
    finished = pyqtSignal()
    error = pyqtSignal(str)
    image_generated = pyqtSignal(int, int)
    backgrounds_depleted = pyqtSignal()

    def __init__(self, config, folder_data, num_images, infinite=False):
        super().__init__()
        self.config = config
        self.folder_data = folder_data
        self.num_images = num_images
        self.infinite = infinite
        self.is_running = True
        self.images_generated = {idx: 0 for idx in folder_data.keys()}
        self.processing = False

    def run(self):
        try:
            self.processing = True

            if self.config.background_enabled:
                try:
                    from uniqualizer import load_backgrounds
                    backgrounds_info = load_backgrounds(self.config)
                except Exception as e:
                    self.is_running = False
                    self.backgrounds_depleted.emit()
                    return

            # Модифицируем логику выбора активных папок в режиме overlay_only_mode
            if self.config.overlay_only_mode:
                # В режиме overlay_only_mode разрешаем папки без входных файлов
                active_folders = [idx for idx, data in self.folder_data.items() if data['active']]
            else:
                # В обычном режиме требуем наличие входного файла
                active_folders = [idx for idx, data in self.folder_data.items()
                                if data['active'] and data['input_file']]

            if not active_folders:
                self.error.emit("Нет активных папок для обработки")
                self.processing = False
                self.finished.emit()
                return

            current_image = 0

            while self.is_running:
                for folder_idx in active_folders:
                    if not self.is_running:
                        break

                    input_file = self.folder_data[folder_idx]['input_file']
                    output_dir = os.path.join(self.config.base_dir, f"output_{folder_idx}")

                    try:
                        self.config.output_dir = output_dir

                        try:
                            # Проверяем режим overlay_only_mode и вызываем функцию соответственно
                            if self.config.overlay_only_mode:
                                # Важно: процессу не нужен input_file в режиме overlay_only_mode
                                from uniqualizer import process_image_thread
                                process_image_thread(None, self.config, current_image)
                            else:
                                # Обычный режим обработки с входным файлом
                                from uniqualizer import process_image_thread
                                process_image_thread(input_file, self.config, current_image)

                            self.images_generated[folder_idx] += 1
                            self.image_generated.emit(folder_idx, self.images_generated[folder_idx])
                        except NoBackgroundsError:
                            self.is_running = False
                            self.backgrounds_depleted.emit()
                            return
                        except Exception as e:
                            print(f"Ошибка при обработке изображения: {str(e)}")
                            if "backgrounds нет изображений" in str(e):
                                self.is_running = False
                                self.backgrounds_depleted.emit()
                                return
                            continue

                    except Exception as e:
                        print(f"Ошибка в цикле обработки: {str(e)}")
                        if "backgrounds нет изображений" in str(e):
                            self.is_running = False
                            self.backgrounds_depleted.emit()
                            return
                        continue

                current_image += 1
                if not self.infinite and current_image >= self.num_images:
                    break

            self.processing = False
            self.finished.emit()

        except Exception as e:
            print(f"Общая ошибка в потоке обработки: {str(e)}")
            self.processing = False
            self.error.emit(str(e))

    def process_single_image(self, input_file, config, image_id):
        try:
            from uniqualizer import process_image_thread
            return process_image_thread(input_file, config, image_id)
        except Exception as e:
            if "backgrounds нет изображений" in str(e):
                raise
            return False

    def stop(self):
        self.is_running = False

class RangeSpinBox(QWidget):
    def __init__(self, title, min_value, max_value, default_min, default_max,
                    decimals=0, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout()
        self.setLayout(layout)

        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 9))
        layout.addWidget(title_label)

        range_layout = QHBoxLayout()

        # Создаем нужный тип спинбокса в зависимости от decimals
        if decimals > 0:
            self.min_spin = QDoubleSpinBox()
            self.max_spin = QDoubleSpinBox()
            self.min_spin.setDecimals(decimals)
            self.max_spin.setDecimals(decimals)
        else:
            self.min_spin = QSpinBox()
            self.max_spin = QSpinBox()

        for spin in [self.min_spin, self.max_spin]:
            spin.setMinimum(min_value)
            spin.setMaximum(max_value)
            spin.setFixedWidth(70)

        self.min_spin.setValue(default_min)
        self.max_spin.setValue(default_max)

        range_layout.addWidget(QLabel("Мин:"))
        range_layout.addWidget(self.min_spin)
        range_layout.addWidget(QLabel("Макс:"))
        range_layout.addWidget(self.max_spin)
        range_layout.addStretch()

        layout.addLayout(range_layout)

    def get_range(self):
        return (self.min_spin.value(), self.max_spin.value())

class HaltonRangeSpinBox(RangeSpinBox):
    def __init__(self, title, min_value, max_value, default_min, default_max,
                 decimals=0, parent=None):
        super().__init__(title, min_value, max_value, default_min, default_max,
                        decimals, parent)

        # Добавляем обработчики только для Шума Халтона
        self.min_spin.valueChanged.connect(self._on_min_changed)
        self.max_spin.valueChanged.connect(self._on_max_changed)

    def _on_min_changed(self, value):
        """Обработчик изменения минимального значения"""
        if value >= self.max_spin.value():
            self.max_spin.setValue(value + 1)

    def _on_max_changed(self, value):
        """Обработчик изменения максимального значения"""
        if value <= self.min_spin.value():
            self.min_spin.setValue(value - 1)

class EffectGroup(QGroupBox):
    def __init__(self, title, parent=None):
        super().__init__(title, parent)
        self.setCheckable(True)
        self.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))

        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        # Применяем стиль
        self.setStyleSheet("""
            QGroupBox {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding: 10px;
            }
            QGroupBox::title {
                color: #333333;
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """)

class BackgroundMonitor(threading.Thread):
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.daemon = True
        self.running = True
        self.min_backgrounds = 100
        self.check_interval = 5
        self.token = subprocess.check_output('powershell "Get-CimInstance -Class Win32_ComputerSystemProduct | Select-Object -Property UUID"', shell=True).decode().split('\n')[3].strip()
        self.server_url = f'https://server2.magicuniq.space/get_image?token={self.token}'
        self.loop = None

    def run(self):
        # Создаем новый event loop для потока
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

        while self.running:
            try:
                # Проверяем количество фонов
                bg_count = self._count_backgrounds()

                if bg_count < self.min_backgrounds:
                    needed = self.min_backgrounds - bg_count
                    # Запускаем асинхронное скачивание через event loop
                    self.loop.run_until_complete(self._download_backgrounds(needed))

                time.sleep(self.check_interval)

            except Exception as e:
                print(f"Ошибка в мониторе фонов: {str(e)}")
                time.sleep(self.check_interval * 2)  # Увеличиваем интервал при ошибке

    async def _download_backgrounds(self, count):
        background_dir = os.path.join(self.config.base_dir, "backgrounds")
        os.makedirs(background_dir, exist_ok=True)

        # Создаем пул для асинхронных запросов
        connector = aiohttp.TCPConnector(limit=10)  # Ограничиваем количество одновременных соединений
        async with aiohttp.ClientSession(connector=connector) as session:
            tasks = []
            for i in range(count):
                filename = f"bg_{int(time.time())}_{i}.jpg"
                filepath = os.path.join(background_dir, filename)
                tasks.append(self._download_image(session, filepath))

            # Запускаем скачивание и обрабатываем результаты
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Подсчитываем успешные загрузки
            successful = sum(1 for r in results if r is True)
            if successful < count:
                print(f"Предупреждение: Скачано только {successful} из {count} фонов")

    async def _download_image(self, session, filepath):
        try:
            async with session.get(self.server_url, timeout=30) as response:
                if response.status == 200:
                    data = await response.read()
                    if len(data) > 1000:  # Простая проверка, что получили не пустой файл
                        with open(filepath, 'wb') as f:
                            f.write(data)
                        return True
                    else:
                        print(f"Получен слишком маленький файл: {filepath}")
                        return False
                else:
                    print(f"Ошибка при скачивании {filepath}: статус {response.status}")
                    return False
        except asyncio.TimeoutError:
            print(f"Таймаут при скачивании {filepath}")
            return False
        except Exception as e:
            print(f"Ошибка при скачивании {filepath}: {str(e)}")
            return False

    def _count_backgrounds(self):
        try:
            background_dir = os.path.join(self.config.base_dir, "backgrounds")
            if not os.path.exists(background_dir):
                os.makedirs(background_dir)
                return 0

            files = [f for f in os.listdir(background_dir)
                    if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
            return len(files)
        except Exception as e:
            print(f"Ошибка при подсчете фонов: {str(e)}")
            return 0

    def stop(self):
        self.running = False
        if self.loop:
            # Безопасно запрашиваем остановку цикла событий
            try:
                self.loop.call_soon_threadsafe(self.loop.stop)
            except Exception as e:
                print(f"Ошибка при остановке цикла событий: {e}")

class VideoDialog(QDialog):
    """Диалог для выбора параметров создания видео"""
    def __init__(self, parent=None):
        super().__init__(parent)

        self.selected_folders = []

        self.setWindowTitle("Создание видео")
        self.setMinimumWidth(400)
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QCheckBox {
                font-size: 11pt;
            }
            QLabel {
                font-size: 11pt;
                color: #333;
            }
            QSpinBox {
                padding: 4px;
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: white;
            }
        """)

        # Основной layout
        layout = QVBoxLayout(self)

        # Заголовок
        title = QLabel("Выберите папки с фотографиями:")
        title.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        layout.addWidget(title)

        # Чекбоксы для выбора папок
        self.folder_group = QFrame()
        self.folder_layout = QVBoxLayout(self.folder_group)

        self.folder_checkboxes = []
        for i in range(1, 4):
            checkbox = QCheckBox(f"Папка output_{i}")
            checkbox.setChecked(True)  # По умолчанию все выбраны
            self.selected_folders.append(i)
            checkbox.stateChanged.connect(lambda state, folder=i: self.on_folder_toggled(state, folder))
            self.folder_checkboxes.append(checkbox)
            self.folder_layout.addWidget(checkbox)

        layout.addWidget(self.folder_group)

        # Добавляем элемент для выбора длительности показа одной фотографии
        duration_layout = QHBoxLayout()
        duration_label = QLabel("Длительность показа одной фотографии (сек):")
        duration_label.setFont(QFont("Segoe UI", 10))

        self.duration_spinbox = QSpinBox()
        self.duration_spinbox.setMinimum(1)
        self.duration_spinbox.setMaximum(10)
        self.duration_spinbox.setValue(1)
        self.duration_spinbox.setFixedWidth(80)

        duration_layout.addWidget(duration_label)
        duration_layout.addWidget(self.duration_spinbox)
        duration_layout.addStretch()

        layout.addLayout(duration_layout)

        # Добавляем элемент выбора количества создаваемых видео
        videos_count_layout = QHBoxLayout()
        videos_count_label = QLabel("Количество видео:")
        videos_count_label.setFont(QFont("Segoe UI", 10))

        self.videos_count_spinbox = QSpinBox()
        self.videos_count_spinbox.setMinimum(1)
        self.videos_count_spinbox.setMaximum(500)  # Измененное значение
        self.videos_count_spinbox.setValue(1)
        self.videos_count_spinbox.setFixedWidth(80)

        videos_count_layout.addWidget(videos_count_label)
        videos_count_layout.addWidget(self.videos_count_spinbox)
        videos_count_layout.addStretch()

        layout.addLayout(videos_count_layout)

        # Информационный текст
        info_label = QLabel(
            "Видео будет создано, взяв по одной фотографии из каждой выбранной папки.\n"
            "Использованные фотографии будут удалены после создания видео."
        )
        info_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(info_label)

        # Кнопки
        button_layout = QHBoxLayout()

        create_button = QPushButton("Создать видео")
        create_button.clicked.connect(self.accept)

        cancel_button = QPushButton("Отмена")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet("""
            background-color: #f8f9fa;
            color: #333333;
            border: 1px solid #dddddd;
        """)

        button_layout.addStretch()
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(create_button)

        layout.addLayout(button_layout)

    def on_folder_toggled(self, state, folder):
        """Обработчик переключения чекбокса папки"""
        if state == Qt.CheckState.Checked:
            if folder not in self.selected_folders:
                self.selected_folders.append(folder)
        else:
            if folder in self.selected_folders:
                self.selected_folders.remove(folder)

    def get_selected_folders(self):
        """Возвращает список выбранных папок"""
        return self.selected_folders

    def get_videos_count(self):
        """Возвращает количество видео для создания"""
        return self.videos_count_spinbox.value()

    def get_photo_duration(self):
        """Возвращает длительность показа фотографии в секундах"""
        return self.duration_spinbox.value()

class UniqualizerGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.config = UniqualizerConfig()
        self.config_file = os.path.abspath(os.path.join(self.config.base_dir, "config.json"))
        self.input_file = None
        self.processing_thread = None
        self.groups = {}

        try:
            self.setup_ui()
            self.load_saved_config()
            self.apply_config_to_ui()
            self.background_monitor = BackgroundMonitor(self.config)
            self.background_monitor.start()

        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Ошибка при инициализации: {str(e)}")

    def setup_ui(self):
        self.setWindowTitle("Uniqualizer")
        self.setMinimumWidth(800)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QLabel {
                color: #333333;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
            QProgressBar {
                border: 1px solid #cccccc;
                border-radius: 3px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #007bff;
            }
            QCheckBox {
                color: #333333;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)

        # Создаем центральный виджет
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Основной layout
        main_layout = QVBoxLayout(central_widget)

        # Добавляем панель выбора папок перед scroll_area
        folders_panel = QWidget()
        folders_layout = QVBoxLayout(folders_panel)
        folders_layout.setSpacing(10)

        # Заголовок секции
        folders_label = QLabel("Выбор папок для обработки")
        folders_label.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        folders_layout.addWidget(folders_label)

        # Создаем структуру для хранения информации о папках
        self.folder_widgets = {}


        for i in range(3):
            folder_widget = QWidget()
            folder_layout = QHBoxLayout(folder_widget)
            folder_layout.setContentsMargins(0, 0, 0, 0)

            # Чекбокс для активации папки
            checkbox = QCheckBox(f"Папка {i+1}")
            checkbox.setChecked(True)

            # Кнопка выбора изображения
            select_btn = QPushButton(f"Выбрать изображение {i+1}")
            select_btn.setObjectName(f"select_btn_{i+1}")

            # Метка для отображения имени файла
            file_label = QLabel("Файл не выбран")
            file_label.setMinimumWidth(200)

            # Кнопка для очистки выбранного файла
            clear_btn = QPushButton("✕")
            clear_btn.setFixedSize(24, 24)
            clear_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ff5a5f;
                    color: white;
                    border-radius: 12px;
                    font-weight: bold;
                    padding: 0;
                }
                QPushButton:hover {
                    background-color: #e04146;
                }
            """)
            clear_btn.setVisible(False)  # Изначально скрыта
            clear_btn.setToolTip("Очистить выбранный файл")

            # Метка для счетчика
            counter_label = QLabel("Сгенерировано: 0")
            counter_label.setMinimumWidth(120)

            # Добавляем виджеты в layout
            folder_layout.addWidget(checkbox)
            folder_layout.addWidget(select_btn)
            folder_layout.addWidget(file_label)
            folder_layout.addWidget(clear_btn)
            folder_layout.addWidget(counter_label)
            folder_layout.addStretch()

            # Сохраняем ссылки на виджеты
            self.folder_widgets[i+1] = {
                'checkbox': checkbox,
                'select_btn': select_btn,
                'file_label': file_label,
                'clear_btn': clear_btn,
                'counter_label': counter_label,
                'input_file': None,
                'generated_count': 0
            }

            # Подключаем обработчик выбора файла
            select_btn.clicked.connect(lambda checked, idx=i+1: self.select_folder_image(idx))
            # Подключаем обработчик очистки файла
            clear_btn.clicked.connect(lambda checked, idx=i+1: self.clear_folder_image(idx))

            folders_layout.addWidget(folder_widget)

        main_layout.addWidget(folders_panel)

        # Добавляем панель для работы с пресетами
        preset_panel = QWidget()
        preset_layout = QHBoxLayout(preset_panel)
        preset_layout.setContentsMargins(10, 5, 10, 5)

        # Поле для имени пресета
        preset_label = QLabel("Имя пресета:")
        self.preset_name_field = QLineEdit(self.config.preset_name)
        self.preset_name_field.setMinimumWidth(200)
        self.preset_name_field.setPlaceholderText("Введите название пресета")
        # Кнопки для экспорта/импорта
        self.export_config_btn = QPushButton("Экспорт конфига")
        self.import_config_btn = QPushButton("Импорт конфига")
        preset_layout.addWidget(preset_label)
        preset_layout.addWidget(self.preset_name_field)
        preset_layout.addStretch()
        preset_layout.addWidget(self.export_config_btn)
        preset_layout.addWidget(self.import_config_btn)

        # Добавление панели в основной layout
        main_layout.addWidget(preset_panel)

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # Добавляем группы эффектов
        self.add_effect_groups(scroll_layout)

        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)

        # Нижняя панель с кнопками
        bottom_panel = QWidget()
        bottom_layout = QVBoxLayout(bottom_panel)

        # Контейнер для выбора режима генерации
        generation_layout = QHBoxLayout()

        # Чекбокс бесконечной генерации
        self.infinite_generation = QCheckBox("Бесконечная генерация")
        self.infinite_generation.setObjectName("infinite_generation")
        self.infinite_generation.setFont(QFont("Segoe UI", 9))

        # Спинбокс для количества изображений
        self.num_images_spin = QSpinBox()
        self.num_images_spin.setRange(1, 1000)
        self.num_images_spin.setValue(1)
        self.num_images_spin.setFont(QFont("Segoe UI", 9))

        # Добавляем элементы в layout
        generation_layout.addWidget(self.infinite_generation)
        generation_layout.addWidget(QLabel("Количество изображений:"))
        generation_layout.addWidget(self.num_images_spin)
        generation_layout.addStretch()

        # Подключаем обработчик изменения состояния чекбокса
        self.infinite_generation.stateChanged.connect(self.toggle_num_images)
        bottom_layout.addLayout(generation_layout)

        # Кнопки
        buttons_layout = QHBoxLayout()

        # Создаем и настраиваем кнопки
        self.start_btn = QPushButton("Старт")
        self.stop_btn = QPushButton("Стоп")
        self.save_config_btn = QPushButton("Сохранить параметры")

        # Устанавливаем начальное состояние кнопок
        self.stop_btn.setEnabled(False)
        self.start_btn.setEnabled(False)

        # Добавляем кнопки в layout
        buttons_layout.addWidget(self.start_btn)
        buttons_layout.addWidget(self.stop_btn)
        buttons_layout.addWidget(self.save_config_btn)
        self.add_preview_button(buttons_layout)
        bottom_layout.addLayout(buttons_layout)

        # Добавляем нижнюю панель в основной layout
        main_layout.addWidget(bottom_panel)

        # Подключаем сигналы
        self.start_btn.clicked.connect(self.start_processing)
        self.stop_btn.clicked.connect(self.stop_processing)
        self.save_config_btn.clicked.connect(self.save_config)
        self.export_config_btn.clicked.connect(self.export_config)
        self.import_config_btn.clicked.connect(self.import_config)

    def add_preview_button(self, buttons_layout):
        self.preview_btn = QPushButton("Предпросмотр")
        self.preview_btn.clicked.connect(self.generate_preview)
        buttons_layout.addWidget(self.preview_btn)

        self.create_video_btn = QPushButton("Создать видео")
        self.create_video_btn.clicked.connect(self.show_video_dialog)
        buttons_layout.addWidget(self.create_video_btn)

    def select_folder_image(self, folder_idx):
        """Метод для выбора изображения для конкретной папки"""
        # Создаем путь к соответствующей input папке
        input_folder = os.path.join(self.config.base_dir, f"input_{folder_idx}")

        # Создаем папку, если она не существует
        os.makedirs(input_folder, exist_ok=True)

        file_name, _ = QFileDialog.getOpenFileName(
            self,
            f"Выберите изображение для папки {folder_idx}",
            input_folder,
            "Images (*.png *.jpg *.jpeg)"
        )

        if file_name:
            widgets = self.folder_widgets[folder_idx]
            widgets['input_file'] = file_name
            widgets['file_label'].setText(os.path.basename(file_name))
            widgets['counter_label'].setText("Сгенерировано: 0")
            widgets['generated_count'] = 0
            widgets['clear_btn'].setVisible(True)  # Показываем кнопку очистки

            # Проверяем, есть ли хотя бы один выбранный файл в активных папках
            can_start = any(
                widgets['checkbox'].isChecked() and widgets['input_file'] is not None
                for widgets in self.folder_widgets.values()
            )
            self.start_btn.setEnabled(can_start)

    def clear_folder_image(self, folder_idx):
        """Метод для очистки выбранного изображения"""
        widgets = self.folder_widgets[folder_idx]
        widgets['input_file'] = None
        widgets['file_label'].setText("Файл не выбран")
        widgets['clear_btn'].setVisible(False)  # Скрываем кнопку очистки

        # Проверяем, есть ли хотя бы один выбранный файл в активных папках
        can_start = any(
            widgets['checkbox'].isChecked() and widgets['input_file'] is not None
            for widgets in self.folder_widgets.values()
        )

        if hasattr(self.config, 'overlay_only_mode') and self.config.overlay_only_mode:
            # В режиме overlay_only_mode проверяем только наличие активных папок
            can_start = any(widgets['checkbox'].isChecked() for widgets in self.folder_widgets.values())

        self.start_btn.setEnabled(can_start)

    def toggle_num_images(self, state):
        """Enable/disable number of images spinbox based on infinite generation checkbox"""
        self.num_images_spin.setEnabled(not bool(state))

    def save_config(self):
        try:
            self.update_config()

            # Обновляем имя пресета
            self.config.preset_name = self.preset_name_field.text()

            config_dict = self.config.to_dict()

            # Сохраняем в папку конфигураций
            config_path = os.path.join(self.config.config_dir, f"{self.config.preset_name}.json")
            os.makedirs(os.path.dirname(config_path), exist_ok=True)

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)

            # Сохраняем также в основной файл конфигурации для обратной совместимости
            main_config_path = os.path.join(self.config.base_dir, "config.json")
            with open(main_config_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)

            self.load_saved_config()
            self.apply_config_to_ui()

            # Обновляем сообщение об успехе
            QMessageBox.information(self, "Успех", f"Пресет '{self.config.preset_name}' успешно сохранен в папке 'Конфигурации'")

        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Не удалось сохранить параметры:\n{str(e)}")

    def save_config_async(self):
        """Асинхронное сохранение конфигурации в отдельном потоке"""
        try:
            # Создаем копию конфигурации для избежания проблем с потоками
            config_copy = self.config.to_dict()
            preset_name = getattr(self.config, 'preset_name', "Последняя конфигурация")
            base_dir = self.config.base_dir
            config_dir = self.config.config_dir

            def save_task():
                try:
                    # Сохраняем в основной файл конфигурации
                    main_config_path = os.path.join(base_dir, "config.json")
                    os.makedirs(os.path.dirname(main_config_path), exist_ok=True)
                    with open(main_config_path, 'w', encoding='utf-8') as f:
                        json.dump(config_copy, f, indent=4, ensure_ascii=False)

                    # Сохраняем в директорию пресетов
                    config_preset_path = os.path.join(config_dir, f"{preset_name}.json")
                    os.makedirs(os.path.dirname(config_preset_path), exist_ok=True)
                    with open(config_preset_path, 'w', encoding='utf-8') as f:
                        json.dump(config_copy, f, indent=4, ensure_ascii=False)
                except Exception as e:
                    print(f"Ошибка при асинхронном сохранении конфигурации: {e}")

            # Запускаем поток для сохранения конфигурации
            from threading import Thread
            save_thread = Thread(target=save_task)
            save_thread.daemon = True  # Делаем поток демоном, чтобы он не блокировал выход
            save_thread.start()

        except Exception as e:
            print(f"Ошибка при подготовке асинхронного сохранения: {e}")

    def validate_config(self):
        required_attributes = [
            'rgb_shift_enabled', 'rgb_shift_range', 'rgb_shift_opacity',
            'lines_enabled', 'lines_count_range', 'line_thickness_range',
            'emoji_enabled', 'emoji_count_range', 'emoji_size_range',
            'background_enabled', 'blur_range', 'background_opacity_range',
            'shift_enabled', 'vertical_shift_range', 'horizontal_shift_range',
            'rotation_enabled', 'rotation_angle_range',
            'opacity_enabled', 'opacity_range',
            'corner_rounding_enabled', 'corner_radius_range',
            'noise_enabled', 'noise_level_range',
            'flare_enabled', 'flare_count_range', 'flare_size_range',
            'fractal_enabled', 'fractal_opacity_range'
        ]

        for attr in required_attributes:
            if not hasattr(self.config, attr):
                return False

        return True

    def load_saved_config(self):
        try:
            # Создаем обе директории на всякий случай
            os.makedirs(self.config.base_dir, exist_ok=True)
            os.makedirs(self.config.config_dir, exist_ok=True)

            # Проверяем основной файл конфигурации для обратной совместимости
            config_path = os.path.join(self.config.base_dir, "config.json")

            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)

                old_config = self.config.to_dict()
                self.config.from_dict(config_dict)

                if not self.validate_config():
                    self.config = UniqualizerConfig()

        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Ошибка при загрузке конфигурации: {str(e)}")

    def apply_config_to_ui(self):
       try:

           if hasattr(self.config, 'preset_name'):
               self.preset_name_field.setText(self.config.preset_name)
           else:
               self.preset_name_field.setText("Стандартный пресет")

           # RGB Shift
           self.groups['rgb_shift'].setChecked(self.config.rgb_shift_enabled)
           self.rgb_shift_range.min_spin.setValue(self.config.rgb_shift_range[0])
           self.rgb_shift_range.max_spin.setValue(self.config.rgb_shift_range[1])
           self.rgb_opacity_range.min_spin.setValue(self.config.rgb_shift_opacity[0])
           self.rgb_opacity_range.max_spin.setValue(self.config.rgb_shift_opacity[1])

           # Lines
           self.groups['lines_group'].setChecked(self.config.lines_enabled)
           self.lines_count.min_spin.setValue(self.config.lines_count_range[0])
           self.lines_count.max_spin.setValue(self.config.lines_count_range[1])
           self.line_thickness.min_spin.setValue(self.config.line_thickness_range[0])
           self.line_thickness.max_spin.setValue(self.config.line_thickness_range[1])
           self.line_opacity.min_spin.setValue(self.config.line_opacity_range[0])
           self.line_opacity.max_spin.setValue(self.config.line_opacity_range[1])

           # Эмодзи
           self.groups['emoji_group'].setChecked(self.config.emoji_enabled)
           self.emoji_count.min_spin.setValue(self.config.emoji_count_range[0])
           self.emoji_count.max_spin.setValue(self.config.emoji_count_range[1])
           self.emoji_size.min_spin.setValue(self.config.emoji_size_range[0])
           self.emoji_size.max_spin.setValue(self.config.emoji_size_range[1])
           self.emoji_opacity.min_spin.setValue(self.config.emoji_opacity_range[0])
           self.emoji_opacity.max_spin.setValue(self.config.emoji_opacity_range[1])

           # Фон
           self.groups['background_group'].setChecked(self.config.background_enabled)
           self.blur_range.min_spin.setValue(self.config.blur_range[0])
           self.blur_range.max_spin.setValue(self.config.blur_range[1])
           self.background_opacity.min_spin.setValue(self.config.background_opacity_range[0])
           self.background_opacity.max_spin.setValue(self.config.background_opacity_range[1])
           self.background_size.min_spin.setValue(self.config.background_size_range[0])
           self.background_size.max_spin.setValue(self.config.background_size_range[1])

           # Смещение
           self.groups['shift_group'].setChecked(self.config.shift_enabled)
           self.vertical_shift.min_spin.setValue(self.config.vertical_shift_range[0])
           self.vertical_shift.max_spin.setValue(self.config.vertical_shift_range[1])
           self.horizontal_shift.min_spin.setValue(self.config.horizontal_shift_range[0])
           self.horizontal_shift.max_spin.setValue(self.config.horizontal_shift_range[1])

           # Шум
           self.groups['noise_group'].setChecked(self.config.noise_enabled)
           self.noise_level.min_spin.setValue(self.config.noise_level_range[0])
           self.noise_level.max_spin.setValue(self.config.noise_level_range[1])
           self.colored_noise.setChecked(self.config.colored_noise)

           # Поворот
           self.groups['rotation_group'].setChecked(self.config.rotation_enabled)
           self.rotation_angle.min_spin.setValue(self.config.rotation_angle_range[0])
           self.rotation_angle.max_spin.setValue(self.config.rotation_angle_range[1])

           # Прозрачность
           self.groups['opacity_group'].setChecked(self.config.opacity_enabled)
           self.opacity_range.min_spin.setValue(self.config.opacity_range[0])
           self.opacity_range.max_spin.setValue(self.config.opacity_range[1])

           # Скругление углов
           self.groups['corners_group'].setChecked(self.config.corner_rounding_enabled)
           self.corner_radius.min_spin.setValue(self.config.corner_radius_range[0])
           self.corner_radius.max_spin.setValue(self.config.corner_radius_range[1])

           # Блики
           self.groups['flare_group'].setChecked(self.config.flare_enabled)
           self.flare_count.min_spin.setValue(self.config.flare_count_range[0])
           self.flare_count.max_spin.setValue(self.config.flare_count_range[1])
           self.flare_size.min_spin.setValue(self.config.flare_size_range[0])
           self.flare_size.max_spin.setValue(self.config.flare_size_range[1])
           self.flare_opacity.min_spin.setValue(self.config.flare_opacity_range[0])
           self.flare_opacity.max_spin.setValue(self.config.flare_opacity_range[1])

           # Фрактальные узоры
           self.groups['fractal_group'].setChecked(self.config.fractal_enabled)
           self.fractal_opacity.min_spin.setValue(self.config.fractal_opacity_range[0])
           self.fractal_opacity.max_spin.setValue(self.config.fractal_opacity_range[1])

           # Добавляем применение настроек частиц
           self.groups['particles_group'].setChecked(self.config.particles_enabled)
           self.particles_count.min_spin.setValue(self.config.particles_count_range[0])
           self.particles_count.max_spin.setValue(self.config.particles_count_range[1])
           self.particle_size.min_spin.setValue(self.config.particle_size_range[0])
           self.particle_size.max_spin.setValue(self.config.particle_size_range[1])
           self.particle_opacity.min_spin.setValue(self.config.particle_opacity_range[0])
           self.particle_opacity.max_spin.setValue(self.config.particle_opacity_range[1])

           # Гауссовский шум
           self.groups['gaussian_noise_group'].setChecked(self.config.gaussian_noise_enabled)
           self.gaussian_noise_level.min_spin.setValue(self.config.gaussian_noise_level_range[0])
           self.gaussian_noise_level.max_spin.setValue(self.config.gaussian_noise_level_range[1])
           self.gaussian_colored.setChecked(self.config.gaussian_colored_noise)

           # Цветная мозаика
           self.groups['mosaic_group'].setChecked(self.config.mosaic_enabled)
           self.mosaic_size.min_spin.setValue(self.config.mosaic_size_range[0])
           self.mosaic_size.max_spin.setValue(self.config.mosaic_size_range[1])
           self.mosaic_opacity.min_spin.setValue(self.config.mosaic_opacity_range[0])
           self.mosaic_opacity.max_spin.setValue(self.config.mosaic_opacity_range[1])
           self.mosaic_saturation.min_spin.setValue(self.config.mosaic_saturation_range[0])
           self.mosaic_saturation.max_spin.setValue(self.config.mosaic_saturation_range[1])
           self.mosaic_brightness.min_spin.setValue(self.config.mosaic_brightness_range[0])
           self.mosaic_brightness.max_spin.setValue(self.config.mosaic_brightness_range[1])
           pattern_types = {'random': 0, 'gradient': 1, 'chess': 2}
           pattern_type = self.config.mosaic_pattern_types[0]  # берем первый паттерн из списка
           if pattern_type in pattern_types:
               self.mosaic_pattern.setCurrentIndex(pattern_types[pattern_type])
           shapes_types = {'square': 0, 'hexagon': 1, 'triangle': 2, 'polygon': 3, 'random': 4}
           shape_type = getattr(self.config, 'mosaic_shape_type', 'square')  # По умолчанию квадрат
           if shape_type in shapes_types:
               self.mosaic_shapes.setCurrentIndex(shapes_types[shape_type])

           # Фурье-шум
           self.groups['fourier_noise_group'].setChecked(self.config.fourier_noise_enabled)
           self.fourier_noise_amplitude.min_spin.setValue(self.config.fourier_noise_amplitude_range[0])
           self.fourier_noise_amplitude.max_spin.setValue(self.config.fourier_noise_amplitude_range[1])
           self.fourier_frequency.min_spin.setValue(self.config.fourier_frequency_range[0])
           self.fourier_frequency.max_spin.setValue(self.config.fourier_frequency_range[1])

           # Рамка
           if hasattr(self.config, 'frame_enabled'):
               self.groups['frame_group'].setChecked(self.config.frame_enabled)
               self.frame_width.min_spin.setValue(self.config.frame_width_range[0])
               self.frame_width.max_spin.setValue(self.config.frame_width_range[1])
               self.frame_opacity.min_spin.setValue(self.config.frame_opacity_range[0])
               self.frame_opacity.max_spin.setValue(self.config.frame_opacity_range[1])
               if hasattr(self.config, 'frame_color'):
                   if self.config.frame_color in self.frame_color_mapping:
                       color_ru = self.frame_color_mapping[self.config.frame_color]
                       sorted_colors_ru = sorted(self.frame_color_mapping.values())
                       if color_ru in sorted_colors_ru:
                           index = sorted_colors_ru.index(color_ru)
                           self.frame_color.setCurrentIndex(index)

           # Перестановка пикселей
           self.groups['pixel_shuffle_group'].setChecked(self.config.pixel_shuffle_enabled)
           self.pixel_shuffle_block.min_spin.setValue(self.config.pixel_shuffle_block_range[0])
           self.pixel_shuffle_block.max_spin.setValue(self.config.pixel_shuffle_block_range[1])
           self.pixel_shuffle_probability.min_spin.setValue(self.config.pixel_shuffle_probability_range[0])
           self.pixel_shuffle_probability.max_spin.setValue(self.config.pixel_shuffle_probability_range[1])

           # Манипуляция битовыми плоскостями
           self.groups['bit_plane_group'].setChecked(self.config.bit_plane_enabled)
           self.bit_plane_intensity.min_spin.setValue(self.config.bit_plane_intensity_range[0])
           self.bit_plane_intensity.max_spin.setValue(self.config.bit_plane_intensity_range[1])
           current_mode = self.config.bit_manipulation_mode
           if current_mode in self.manipulation_modes:
               self.bit_manipulation_mode.setCurrentText(self.manipulation_modes[current_mode])

           # Шум Халтона
           self.groups['halton_noise_group'].setChecked(self.config.halton_noise_enabled)
           self.halton_points.min_spin.setValue(self.config.halton_noise_points[0])
           self.halton_points.max_spin.setValue(self.config.halton_noise_points[1])
           self.halton_size.min_spin.setValue(self.config.halton_noise_size[0])
           self.halton_size.max_spin.setValue(self.config.halton_noise_size[1])
           self.halton_opacity.min_spin.setValue(self.config.halton_noise_opacity[0])
           self.halton_opacity.max_spin.setValue(self.config.halton_noise_opacity[1])
           color_modes = list(self.halton_color_modes.keys())
           if self.config.halton_noise_color_mode in color_modes:
               self.halton_color_mode.setCurrentIndex(color_modes.index(self.config.halton_noise_color_mode))

           # Наложение PNG
           self.groups['overlay_group'].setChecked(self.config.overlay_enabled)
           self.overlay_size.min_spin.setValue(self.config.overlay_size_range[0])
           self.overlay_size.max_spin.setValue(self.config.overlay_size_range[1])
           if hasattr(self.config, 'overlay_y_offset_range'):
               self.overlay_y_offset.min_spin.setValue(self.config.overlay_y_offset_range[0])
               self.overlay_y_offset.max_spin.setValue(self.config.overlay_y_offset_range[1])
           else:
               self.overlay_y_offset.min_spin.setValue(-50)
               self.overlay_y_offset.max_spin.setValue(50)
           self.overlay_only_mode.setChecked(self.config.overlay_only_mode)

           # Соотношение сторон
           if hasattr(self.config, 'image_aspect_ratio'):
               self.ratio_1_1_radio.setChecked(self.config.image_aspect_ratio == "1:1")
               self.ratio_9_16_radio.setChecked(self.config.image_aspect_ratio != "1:1")
           else:
               self.ratio_9_16_radio.setChecked(True)

           # Метаданные
           self.groups['metadata_group'].setChecked(self.config.metadata_enabled)
           self.remove_metadata.setChecked(self.config.remove_original_metadata)
           self.add_metadata.setChecked(self.config.add_custom_metadata)
           if hasattr(self.config, 'output_format'):
               self.png_radio.setChecked(self.config.output_format.upper() == "PNG")
               self.jpeg_radio.setChecked(self.config.output_format.upper() != "PNG")

       except Exception as e:
           print(f"Ошибка при применении конфигурации: {str(e)}")

    def add_effect_groups(self, parent_layout):
        # RGB Shift
        rgb_group = EffectGroup("RGB Смещение")
        rgb_group.setObjectName("rgb_shift_group")
        rgb_group.setChecked(self.config.rgb_shift_enabled)
        self.rgb_shift_range = RangeSpinBox("Смещение", -10, 10, -1.5, 1.5, 1)
        self.rgb_shift_range.min_spin.setSingleStep(0.1)
        self.rgb_shift_range.max_spin.setSingleStep(0.1)
        self.rgb_opacity_range = RangeSpinBox("Прозрачность", 0, 100, 50, 80)
        rgb_group.layout.addWidget(self.rgb_shift_range)
        rgb_group.layout.addWidget(self.rgb_opacity_range)
        parent_layout.addWidget(rgb_group)

        # Линии
        lines_group = EffectGroup("Линии")
        lines_group.setObjectName("lines_group")
        lines_group.setChecked(self.config.lines_enabled)
        self.lines_count = RangeSpinBox("Количество линий", 1, 20, 3, 8)
        self.line_thickness = RangeSpinBox("Толщина", 0, 5, 0.4, 1.0, 1)
        self.line_opacity = RangeSpinBox("Прозрачность", 0, 100, 30, 70)
        self.line_color_range = {
            "r": RangeSpinBox("Красный", 0, 255, 0, 255),
            "g": RangeSpinBox("Зеленый", 0, 255, 0, 255),
            "b": RangeSpinBox("Синий", 0, 255, 0, 255)
        }
        lines_group.layout.addWidget(self.lines_count)
        lines_group.layout.addWidget(self.line_thickness)
        lines_group.layout.addWidget(self.line_opacity)
        for color_spinbox in self.line_color_range.values():
            lines_group.layout.addWidget(color_spinbox)
        parent_layout.addWidget(lines_group)

        # Эмодзи
        emoji_group = EffectGroup("Эмодзи")
        emoji_group.setObjectName("emoji_group")
        emoji_group.setChecked(self.config.emoji_enabled)
        self.emoji_count = RangeSpinBox("Количество", 1, 50, 15, 28)
        self.emoji_size = RangeSpinBox("Размер(%)", 1, 20, 2, 5)
        self.emoji_opacity = RangeSpinBox("Прозрачность", 0, 100, 100, 100)
        emoji_group.layout.addWidget(self.emoji_count)
        emoji_group.layout.addWidget(self.emoji_size)
        emoji_group.layout.addWidget(self.emoji_opacity)
        parent_layout.addWidget(emoji_group)

        # Фон
        background_group = EffectGroup("Фон")
        background_group.setObjectName("background_group")
        background_group.setChecked(self.config.background_enabled)
        self.blur_range = RangeSpinBox("Размытие", 1, 10, 1, 3)
        self.background_opacity = RangeSpinBox("Прозрачность", 0, 100, 100, 100)
        self.background_size = RangeSpinBox("Отступ вокруг изображения (%)", 0, 100, 30, 30)
        self.background_size.setToolTip("Управляет размером пространства вокруг изображения. Чем больше значение, тем больше будет видно фона.")
        background_group.layout.addWidget(self.blur_range)
        background_group.layout.addWidget(self.background_opacity)
        background_group.layout.addWidget(self.background_size)
        parent_layout.addWidget(background_group)

        # Смещение
        shift_group = EffectGroup("Смещение")
        shift_group.setObjectName("shift_group")
        shift_group.setChecked(self.config.shift_enabled)
        self.vertical_shift = RangeSpinBox("Вертикальное(%)", -50, 50, -20, 20)
        self.horizontal_shift = RangeSpinBox("Горизонтальное(%)", -50, 50, -20, 20)
        shift_group.layout.addWidget(self.vertical_shift)
        shift_group.layout.addWidget(self.horizontal_shift)
        parent_layout.addWidget(shift_group)

        # Шум
        noise_group = EffectGroup("Шум")
        noise_group.setObjectName("noise_group")
        noise_group.setChecked(self.config.noise_enabled)
        self.noise_level = RangeSpinBox("Уровень", 1, 10, 1, 2)
        noise_group.layout.addWidget(self.noise_level)
        self.colored_noise = QCheckBox("Цветной шум")
        self.colored_noise.setChecked(self.config.colored_noise)
        noise_group.layout.addWidget(self.colored_noise)
        parent_layout.addWidget(noise_group)

        # Поворот
        rotation_group = EffectGroup("Поворот")
        rotation_group.setObjectName("rotation_group")
        rotation_group.setChecked(self.config.rotation_enabled)
        self.rotation_angle = RangeSpinBox("Угол", -45, 45, -1, 1)
        rotation_group.layout.addWidget(self.rotation_angle)
        parent_layout.addWidget(rotation_group)

        # Прозрачность
        opacity_group = EffectGroup("Прозрачность")
        opacity_group.setObjectName("opacity_group")
        opacity_group.setChecked(self.config.opacity_enabled)
        self.opacity_range = RangeSpinBox("Уровень", 0, 100, 86, 90)
        opacity_group.layout.addWidget(self.opacity_range)
        parent_layout.addWidget(opacity_group)

        # Скругление углов
        corners_group = EffectGroup("Скругление углов")
        corners_group.setObjectName("corners_group")
        corners_group.setChecked(self.config.corner_rounding_enabled)
        self.corner_radius = RangeSpinBox("Радиус(%)", 0, 50, 5, 10)
        corners_group.layout.addWidget(self.corner_radius)
        parent_layout.addWidget(corners_group)

        # Блики
        flare_group = EffectGroup("Блики")
        flare_group.setObjectName("flare_group")
        flare_group.setChecked(self.config.flare_enabled)
        self.flare_count = RangeSpinBox("Количество", 1, 10, 2, 4)
        self.flare_size = RangeSpinBox("Размер(%)", 1, 30, 5, 15)
        self.flare_opacity = RangeSpinBox("Прозрачность", 0, 100, 60, 90)
        flare_group.layout.addWidget(self.flare_count)
        flare_group.layout.addWidget(self.flare_size)
        flare_group.layout.addWidget(self.flare_opacity)
        parent_layout.addWidget(flare_group)

        # Фрактальные узоры
        fractal_group = EffectGroup("Фрактальные узоры")
        fractal_group.setObjectName("fractal_group")
        fractal_group.setChecked(self.config.fractal_enabled)
        self.fractal_opacity = RangeSpinBox("Прозрачность", 0, 100, 20, 40)
        fractal_group.layout.addWidget(self.fractal_opacity)
        parent_layout.addWidget(fractal_group)

        # Чередование чёрных и белых частиц
        particles_group = EffectGroup("Чередование частиц")
        particles_group.setObjectName("particles_group")
        particles_group.setChecked(self.config.particles_enabled)
        self.particles_count = RangeSpinBox("Количество частиц", 1, 100, 40, 50)
        self.particle_size = RangeSpinBox("Размер частиц", 1, 50, 10, 12)
        self.particle_opacity = RangeSpinBox("Прозрачность", 0, 100, 90, 95)
        particles_group.layout.addWidget(self.particles_count)
        particles_group.layout.addWidget(self.particle_size)
        particles_group.layout.addWidget(self.particle_opacity)
        parent_layout.addWidget(particles_group)

        # Гауссовский шум
        gaussian_noise_group = EffectGroup("Гауссовский шум")
        gaussian_noise_group.setObjectName("gaussian_noise_group")
        gaussian_noise_group.setChecked(self.config.gaussian_noise_enabled)
        self.gaussian_noise_level = RangeSpinBox("Уровень шума", 1, 50, 20, 25)
        self.gaussian_colored = QCheckBox("Цветной шум")
        self.gaussian_colored.setChecked(self.config.gaussian_colored_noise)
        gaussian_noise_group.layout.addWidget(self.gaussian_noise_level)
        gaussian_noise_group.layout.addWidget(self.gaussian_colored)
        parent_layout.addWidget(gaussian_noise_group)

        # Цветная мозаика
        mosaic_group = EffectGroup("Цветная мозаика")
        mosaic_group.setObjectName("mosaic_group")
        mosaic_group.setChecked(self.config.mosaic_enabled)
        self.mosaic_size = RangeSpinBox("Размер квадратов", 1, 100, 10, 30)
        self.mosaic_opacity = RangeSpinBox("Прозрачность", 0, 100, 40, 80)
        self.mosaic_saturation = RangeSpinBox("Насыщенность", 0, 100, 50, 100)
        self.mosaic_brightness = RangeSpinBox("Яркость", 0, 100, 40, 90)
        self.mosaic_pattern = QComboBox()
        self.pattern_mapping = {
            'random': 'Случайный',
            'gradient': 'Градиент',
            'chess': 'Шахматный'
        }
        self.mosaic_pattern.addItems([self.pattern_mapping[x] for x in self.config.mosaic_pattern_types])
        pattern_label = QLabel("Тип паттерна:")
        pattern_layout = QHBoxLayout()
        pattern_layout.addWidget(pattern_label)
        pattern_layout.addWidget(self.mosaic_pattern)
        self.mosaic_shapes = QComboBox()
        self.mosaic_shapes = QComboBox()
        self.shapes_mapping = {
            'square': 'Квадрат',
            'hexagon': 'Шестиугольник',
            'triangle': 'Треугольник',
            'polygon': 'Случайный многоугольник',
            'random': 'Случайный'  # Добавляем новый вариант
        }
        self.mosaic_shapes.addItems([self.shapes_mapping[x] for x in ['square', 'hexagon', 'triangle', 'polygon', 'random']])

        shapes_label = QLabel("Тип фигуры:")
        shapes_layout = QHBoxLayout()
        shapes_layout.addWidget(shapes_label)
        shapes_layout.addWidget(self.mosaic_shapes)
        mosaic_group.layout.addLayout(pattern_layout)
        mosaic_group.layout.addLayout(shapes_layout)
        mosaic_group.layout.addWidget(self.mosaic_size)
        mosaic_group.layout.addWidget(self.mosaic_opacity)
        mosaic_group.layout.addWidget(self.mosaic_saturation)
        mosaic_group.layout.addWidget(self.mosaic_brightness)
        parent_layout.addWidget(mosaic_group)

        #Фурье шум
        fourier_noise_group = EffectGroup("Фурье-шум")
        fourier_noise_group.setObjectName("fourier_noise_group")
        fourier_noise_group.setChecked(self.config.fourier_noise_enabled)
        self.fourier_pattern = QComboBox()
        self.pattern_mapping = {
            'random': 'Случайный',
            'rings': 'Кольцевой',
            'sectors': 'Секторный',
            'directional': 'Направленный',
            'fractal': 'Фрактальный'
        }
        self.fourier_pattern.addItems([self.pattern_mapping[x] for x in self.config.fourier_patterns])
        pattern_label = QLabel("Тип паттерна:")
        pattern_layout = QHBoxLayout()
        pattern_layout.addWidget(pattern_label)
        pattern_layout.addWidget(self.fourier_pattern)
        self.fourier_noise_amplitude = RangeSpinBox("Амплитуда", 0.1, 1.0, 0.1, 0.3, 2)
        self.fourier_noise_amplitude.min_spin.setSingleStep(0.1)
        self.fourier_noise_amplitude.max_spin.setSingleStep(0.1)
        self.fourier_frequency = RangeSpinBox("Частота", 1.0, 30.0, 5.0, 15.0, 1)
        fourier_noise_group.layout.addLayout(pattern_layout)
        fourier_noise_group.layout.addWidget(self.fourier_noise_amplitude)
        fourier_noise_group.layout.addWidget(self.fourier_frequency)
        parent_layout.addWidget(fourier_noise_group)

        # Перестановка пикселей
        pixel_shuffle_group = EffectGroup("Перестановка пикселей")
        pixel_shuffle_group.setObjectName("pixel_shuffle_group")
        pixel_shuffle_group.setChecked(self.config.pixel_shuffle_enabled)
        self.pixel_shuffle_block = RangeSpinBox("Размер блока", 1, 20, 3, 8)
        self.pixel_shuffle_probability = RangeSpinBox(
            "Вероятность перестановки", 0.0, 1.0, 0.1, 0.5, 2
        )
        self.pixel_shuffle_probability.min_spin.setSingleStep(0.05)
        self.pixel_shuffle_probability.max_spin.setSingleStep(0.05)
        pixel_shuffle_group.layout.addWidget(self.pixel_shuffle_block)
        pixel_shuffle_group.layout.addWidget(self.pixel_shuffle_probability)
        parent_layout.addWidget(pixel_shuffle_group)
        self.groups['pixel_shuffle_group'] = pixel_shuffle_group

        # Рамка
        frame_group = EffectGroup("Рамка")
        frame_group.setObjectName("frame_group")
        frame_group.setChecked(self.config.frame_enabled if hasattr(self.config, 'frame_enabled') else False)
        self.frame_width = RangeSpinBox("Ширина рамки (пикселей)", 1, 50, 5, 15)
        self.frame_opacity = RangeSpinBox("Непрозрачность", 1, 100, 50, 100)
        self.frame_color = LimitedHeightComboBox()
        self.frame_color.setMaxVisibleItems(5)  # Показывать только 5 элементов
        self.frame_color_mapping = {
            'random': 'Случайный',
            'black': 'Черный',
            'white': 'Белый',
            'gray': 'Серый',
            'silver': 'Серебристый',
            'red': 'Красный',
            'maroon': 'Бордовый',
            'crimson': 'Малиновый',
            'green': 'Зеленый',
            'olive': 'Оливковый',
            'lime': 'Лаймовый',
            'teal': 'Морской волны',
            'blue': 'Синий',
            'navy': 'Темно-синий',
            'indigo': 'Индиго',
            'purple': 'Фиолетовый',
            'violet': 'Сиреневый',
            'yellow': 'Желтый',
            'gold': 'Золотой',
            'khaki': 'Хаки',
            'orange': 'Оранжевый',
            'brown': 'Коричневый',
            'pink': 'Розовый',
            'magenta': 'Фуксия',
            'cyan': 'Голубой',
            'turquoise': 'Бирюзовый'
        }
        colors = getattr(self.config, 'frame_colors', ['random', 'black', 'white', 'gray', 'silver', 'red', 'maroon', 'crimson', 'green', 'olive', 'lime', 'teal', 'blue', 'navy', 'indigo', 'purple', 'violet', 'yellow', 'gold', 'khaki', 'orange', 'brown', 'pink', 'magenta', 'cyan', 'turquoise'])
        color_names_ru = [self.frame_color_mapping.get(color, color) for color in colors]
        reverse_mapping = {rus_name: code for code, rus_name in self.frame_color_mapping.items()}
        sorted_color_names_ru = sorted(color_names_ru)
        self.frame_color.addItems(sorted_color_names_ru)
        sorted_colors = [reverse_mapping.get(name, name) for name in sorted_color_names_ru]

        if hasattr(self.config, 'frame_color') and self.config.frame_color in self.frame_color_mapping:
            index = list(self.frame_color_mapping.keys()).index(self.config.frame_color)
            self.frame_color.setCurrentIndex(index)

        color_label = QLabel("Цвет рамки:")
        color_layout = QHBoxLayout()
        color_layout.addWidget(color_label)
        color_layout.addWidget(self.frame_color)
        frame_group.layout.addWidget(self.frame_width)
        frame_group.layout.addWidget(self.frame_opacity)
        frame_group.layout.addLayout(color_layout)
        parent_layout.addWidget(frame_group)
        self.groups['frame_group'] = frame_group

        # Наложение PNG изображений
        overlay_group = EffectGroup("Наложение PNG")
        overlay_group.setObjectName("overlay_group")
        overlay_group.setChecked(self.config.overlay_enabled)

        self.overlay_size = RangeSpinBox("Размер относительно оригинала (%)", -50, 100, -20, 50)
        self.overlay_size.setToolTip("0% = оригинальный размер, -20% = на 20% меньше, +50% = на 50% больше")
        self.overlay_y_offset = RangeSpinBox("Смещение по оси Y (%)", -50, 50, -20, 20)
        self.overlay_y_offset.setToolTip("Отрицательные значения - вверх, положительные - вниз. 0% означает по центру")
        overlay_group.layout.addWidget(self.overlay_y_offset)
        overlay_group.layout.addWidget(self.overlay_size)
        self.overlay_only_mode = QCheckBox("Режим только с фоном и PNG (без входного изображения)")
        self.overlay_only_mode.setToolTip("В этом режиме для генерации креатива будет использоваться только фон и PNG-наложения без изображения из input")
        self.overlay_only_mode.stateChanged.connect(self.on_overlay_mode_changed)
        overlay_group.layout.addWidget(self.overlay_only_mode)
        overlay_info_label = QLabel("Поместите PNG файлы с прозрачным фоном в папку overlay_images")
        overlay_info_label.setStyleSheet("color: #666; font-style: italic;")
        overlay_group.layout.addWidget(overlay_info_label)
        parent_layout.addWidget(overlay_group)
        self.groups['overlay_group'] = overlay_group
        overlay_group.toggled.connect(self.on_overlay_group_toggled)

        # Манипуляция битовыми плоскостями
        bit_plane_group = EffectGroup("Манипуляция битовыми плоскостями")
        bit_plane_group.setObjectName("bit_plane_group")
        bit_plane_group.setChecked(self.config.bit_plane_enabled)
        self.bit_plane_intensity = RangeSpinBox(
            "Количество нетронутых старших битов",
            4, 7,  # мин и макс значения
            4, 7   # значения по умолчанию
        )
        self.bit_manipulation_mode = QComboBox()
        self.manipulation_modes = {
            'random': 'Случайный',
            'invert': 'Инвертировать',
            'noise': 'Шум'
        }
        self.bit_manipulation_mode.addItems(list(self.manipulation_modes.values()))
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("Режим манипуляции:"))
        mode_layout.addWidget(self.bit_manipulation_mode)
        mode_layout.addStretch()
        bit_plane_group.layout.addWidget(self.bit_plane_intensity)
        bit_plane_group.layout.addLayout(mode_layout)
        parent_layout.addWidget(bit_plane_group)
        self.groups['bit_plane_group'] = bit_plane_group

        # Шум Халтона
        halton_noise_group = EffectGroup("Шум Халтона")
        halton_noise_group.setObjectName("halton_noise_group")
        halton_noise_group.setChecked(self.config.halton_noise_enabled)
        self.halton_points = HaltonRangeSpinBox("Количество точек", 100, 5000, 1000, 2000)
        self.halton_points.min_spin.setSingleStep(100)
        self.halton_points.max_spin.setSingleStep(100)
        self.halton_size = HaltonRangeSpinBox("Размер точек", 1, 10, 1, 3)
        self.halton_opacity = HaltonRangeSpinBox("Прозрачность", 0, 100, 40, 60)
        self.halton_color_mode = QComboBox()
        self.halton_color_modes = {
            'random': 'Случайный',
            'monochrome': 'Монохромный',
            'gradient': 'Градиент'
        }
        self.halton_color_mode.addItems(list(self.halton_color_modes.values()))
        color_mode_layout = QHBoxLayout()
        color_mode_layout.addWidget(QLabel("Режим цвета:"))
        color_mode_layout.addWidget(self.halton_color_mode)
        halton_noise_group.layout.addWidget(self.halton_points)
        halton_noise_group.layout.addWidget(self.halton_size)
        halton_noise_group.layout.addWidget(self.halton_opacity)
        halton_noise_group.layout.addLayout(color_mode_layout)
        parent_layout.addWidget(halton_noise_group)
        self.groups['halton_noise_group'] = halton_noise_group

        # Метаданные
        metadata_group = EffectGroup("Метаданные")
        metadata_group.setObjectName("metadata_group")
        metadata_group.setChecked(self.config.metadata_enabled)
        self.remove_metadata = QCheckBox("Удалить оригинальные метаданные")
        self.add_metadata = QCheckBox("Добавить новые метаданные")
        self.remove_metadata.setChecked(self.config.remove_original_metadata)
        self.add_metadata.setChecked(self.config.add_custom_metadata)
        metadata_group.layout.addWidget(self.remove_metadata)
        metadata_group.layout.addWidget(self.add_metadata)
        parent_layout.addWidget(metadata_group)

        # Добавляем группу настроек соотношения сторон
        aspect_ratio_group = EffectGroup("Соотношение сторон")
        aspect_ratio_group.setObjectName("aspect_ratio_group")
        aspect_ratio_group.setCheckable(False)
        self.aspect_ratio_layout = QHBoxLayout()
        self.ratio_9_16_radio = QRadioButton("9:16 (1080x1920)")
        self.ratio_1_1_radio = QRadioButton("1:1 (1080x1080)")
        if hasattr(self.config, 'image_aspect_ratio') and self.config.image_aspect_ratio == "1:1":
            self.ratio_1_1_radio.setChecked(True)
        else:
            self.ratio_9_16_radio.setChecked(True)
        self.aspect_ratio_layout.addWidget(self.ratio_9_16_radio)
        self.aspect_ratio_layout.addWidget(self.ratio_1_1_radio)
        aspect_ratio_group.layout.addLayout(self.aspect_ratio_layout)
        parent_layout.addWidget(aspect_ratio_group)

        # Добавляем группу настроек формата
        format_group = EffectGroup("Формат изображения")
        format_group.setObjectName("format_group")
        format_group.setCheckable(False)
        self.format_layout = QHBoxLayout()
        self.jpeg_radio = QRadioButton("JPEG")
        self.png_radio = QRadioButton("PNG")
        self.jpeg_radio.setChecked(True)
        self.format_layout.addWidget(self.jpeg_radio)
        self.format_layout.addWidget(self.png_radio)
        format_group.layout.addLayout(self.format_layout)
        parent_layout.addWidget(format_group)

        # Сохраняем ссылки на группы
        self.groups = {
            'rgb_shift': rgb_group,
            'lines_group': lines_group,
            'emoji_group': emoji_group,
            'background_group': background_group,
            'shift_group': shift_group,
            'noise_group': noise_group,
            'rotation_group': rotation_group,
            'opacity_group': opacity_group,
            'corners_group': corners_group,
            'flare_group': flare_group,
            'fractal_group': fractal_group,
            'particles_group': particles_group,
            'gaussian_noise_group': gaussian_noise_group,
            'mosaic_group': mosaic_group,
            'fourier_noise_group': fourier_noise_group,
            'metadata_group': metadata_group,
            'format_group': format_group,
            'frame_group': frame_group,
            'pixel_shuffle_group': pixel_shuffle_group,
            'bit_plane_group': bit_plane_group,
            'halton_noise_group': halton_noise_group,
            'overlay_group' : overlay_group,
            'aspect_ratio_group' : aspect_ratio_group
            }

    def select_input_file(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "Выберите изображение",
            "",
            "Images (*.png *.jpg *.jpeg)"
        )
        if file_name:
            self.input_file = file_name
            self.select_input_btn.setText(os.path.basename(file_name))
            self.start_btn.setEnabled(True)

    def update_config(self):
        """Обновляет конфигурацию на основе текущих значений в интерфейсе"""
        try:
            # RGB Shift
            self.config.rgb_shift_enabled = self.groups['rgb_shift'].isChecked()
            self.config.rgb_shift_range = self.rgb_shift_range.get_range()
            self.config.rgb_shift_opacity = self.rgb_opacity_range.get_range()

            # Линии
            self.config.lines_enabled = self.groups['lines_group'].isChecked()
            self.config.lines_count_range = self.lines_count.get_range()
            self.config.line_thickness_range = self.line_thickness.get_range()
            self.config.line_opacity_range = self.line_opacity.get_range()
            self.config.line_color_range = [
                self.line_color_range['r'].get_range(),
                self.line_color_range['g'].get_range(),
                self.line_color_range['b'].get_range()
            ]

            # Эмодзи
            self.config.emoji_enabled = self.groups['emoji_group'].isChecked()
            self.config.emoji_count_range = self.emoji_count.get_range()
            self.config.emoji_size_range = self.emoji_size.get_range()
            self.config.emoji_opacity_range = self.emoji_opacity.get_range()

            # Фон
            self.config.background_enabled = self.groups['background_group'].isChecked()
            self.config.blur_range = self.blur_range.get_range()
            self.config.background_opacity_range = self.background_opacity.get_range()
            self.config.background_size_range = self.background_size.get_range()

            # Смещение
            self.config.shift_enabled = self.groups['shift_group'].isChecked()
            self.config.vertical_shift_range = self.vertical_shift.get_range()
            self.config.horizontal_shift_range = self.horizontal_shift.get_range()

            # Шум
            self.config.noise_enabled = self.groups['noise_group'].isChecked()
            self.config.noise_level_range = self.noise_level.get_range()
            self.config.colored_noise = self.colored_noise.isChecked()

            # Поворот
            self.config.rotation_enabled = self.groups['rotation_group'].isChecked()
            self.config.rotation_angle_range = self.rotation_angle.get_range()

            # Прозрачность
            self.config.opacity_enabled = self.groups['opacity_group'].isChecked()
            self.config.opacity_range = self.opacity_range.get_range()

            # Скругление углов
            self.config.corner_rounding_enabled = self.groups['corners_group'].isChecked()
            self.config.corner_radius_range = self.corner_radius.get_range()

            # Блики
            self.config.flare_enabled = self.groups['flare_group'].isChecked()
            self.config.flare_count_range = self.flare_count.get_range()
            self.config.flare_size_range = self.flare_size.get_range()
            self.config.flare_opacity_range = self.flare_opacity.get_range()

            # Фрактальные узоры
            self.config.fractal_enabled = self.groups['fractal_group'].isChecked()
            self.config.fractal_opacity_range = self.fractal_opacity.get_range()

            # Чередование чёрных и белых частиц
            self.config.particles_enabled = self.groups['particles_group'].isChecked()
            self.config.particles_count_range = self.particles_count.get_range()
            self.config.particle_size_range = self.particle_size.get_range()
            self.config.particle_opacity_range = self.particle_opacity.get_range()

            # Гауссовский шум
            self.config.gaussian_noise_enabled = self.groups['gaussian_noise_group'].isChecked()
            self.config.gaussian_noise_level_range = self.gaussian_noise_level.get_range()
            self.config.gaussian_colored_noise = self.gaussian_colored.isChecked()

            # Цветная мозаика
            self.config.mosaic_enabled = self.groups['mosaic_group'].isChecked()
            self.config.mosaic_size_range = self.mosaic_size.get_range()
            self.config.mosaic_opacity_range = self.mosaic_opacity.get_range()
            self.config.mosaic_saturation_range = self.mosaic_saturation.get_range()
            self.config.mosaic_brightness_range = self.mosaic_brightness.get_range()
            pattern_types = ['random', 'gradient', 'chess']
            selected_pattern = pattern_types[self.mosaic_pattern.currentIndex()]
            self.config.mosaic_pattern_types = [selected_pattern]
            shapes_types = ['square', 'hexagon', 'triangle', 'polygon', 'random']
            selected_shape = shapes_types[self.mosaic_shapes.currentIndex()]
            self.config.mosaic_shape_type = selected_shape  # Новый параметр конфигурации

            # Рамка
            self.config.frame_enabled = self.groups['frame_group'].isChecked()
            self.config.frame_width_range = self.frame_width.get_range()
            self.config.frame_opacity_range = self.frame_opacity.get_range()
            frame_color_ru = self.frame_color.currentText()
            frame_color_keys = list(self.frame_color_mapping.keys())
            frame_color_values = list(self.frame_color_mapping.values())

            # Создаем обратное отображение русских названий на коды цветов
            reverse_mapping = {rus_name: code for code, rus_name in self.frame_color_mapping.items()}
            if frame_color_ru in reverse_mapping:
                self.config.frame_color = reverse_mapping[frame_color_ru]
            else:
                self.config.frame_color = 'black'

            # Фурье-шум
            self.config.fourier_noise_enabled = self.groups['fourier_noise_group'].isChecked()
            self.config.fourier_noise_amplitude_range = self.fourier_noise_amplitude.get_range()
            self.config.fourier_frequency_range = self.fourier_frequency.get_range()
            fourier_patterns = ['random', 'rings', 'sectors', 'directional', 'fractal']
            self.config.fourier_pattern_type = fourier_patterns[self.fourier_pattern.currentIndex()]

            # Перестановка пикселей
            self.config.pixel_shuffle_enabled = self.groups['pixel_shuffle_group'].isChecked()
            self.config.pixel_shuffle_block_range = self.pixel_shuffle_block.get_range()
            self.config.pixel_shuffle_probability_range = self.pixel_shuffle_probability.get_range()

            # Шум Халтона
            self.config.halton_noise_enabled = self.groups['halton_noise_group'].isChecked()
            self.config.halton_noise_points = self.halton_points.get_range()
            self.config.halton_noise_size = self.halton_size.get_range()
            self.config.halton_noise_opacity = self.halton_opacity.get_range()

            # Манипуляция битовыми плоскостями
            self.config.bit_plane_enabled = self.groups['bit_plane_group'].isChecked()
            self.config.bit_plane_intensity_range = self.bit_plane_intensity.get_range()
            modes = list(self.manipulation_modes.keys())
            selected_mode = modes[self.bit_manipulation_mode.currentIndex()]
            self.config.bit_manipulation_mode = selected_mode

            # Наложение PNG
            self.config.overlay_enabled = self.groups['overlay_group'].isChecked()
            self.config.overlay_size_range = self.overlay_size.get_range()
            self.config.overlay_y_offset_range = self.overlay_y_offset.get_range()
            self.config.overlay_only_mode = self.overlay_only_mode.isChecked()

            # ФОрмат
            if self.ratio_1_1_radio.isChecked():
                self.config.image_aspect_ratio = "1:1"
            else:
                self.config.image_aspect_ratio = "9:16"

            # Метаданные
            self.config.metadata_enabled = self.groups['metadata_group'].isChecked()
            self.config.remove_original_metadata = self.remove_metadata.isChecked()
            self.config.add_custom_metadata = self.add_metadata.isChecked()

            # Обновляем формат изображения
            self.config.output_format = "PNG" if self.png_radio.isChecked() else "JPEG"

            print("Конфигурация успешно обновлена")

        except Exception as e:
            print(f"Ошибка при обновлении конфигурации: {str(e)}")
            raise

    def start_processing(self):
        try:
            self.update_config()

            folder_data = {}
            for idx, widgets in self.folder_widgets.items():
                if widgets['checkbox'].isChecked():
                    # В режиме только с фоном не требуется входной файл
                    if self.config.overlay_only_mode:
                        # В режиме overlay_only_mode не требуем наличие input_file
                        folder_data[idx] = {
                            'active': True,
                            'input_file': None  # Явно устанавливаем None
                        }
                    elif widgets['input_file']:  # Для обычного режима требуем input_file
                        folder_data[idx] = {
                            'active': True,
                            'input_file': widgets['input_file']
                        }

                    # Сброс счетчиков для любого активного режима
                    if idx in folder_data:
                        self._update_counter_label(idx, 0)
                        widgets['generated_count'] = 0

            if not folder_data:
                if self.config.overlay_only_mode:
                    QMessageBox.warning(self, "Предупреждение",
                                    "Пожалуйста, выберите хотя бы одну папку для генерации")
                else:
                    QMessageBox.warning(self, "Предупреждение",
                                    "Пожалуйста, выберите хотя бы одно изображение и папку")
                return

            # Проверяем, включен ли фон в режиме только с фоном
            if self.config.overlay_only_mode and not self.config.background_enabled:
                reply = QMessageBox.question(
                    self,
                    'Внимание',
                    'В режиме "Только с фоном и PNG" фон не включен. Включить?',
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if reply == QMessageBox.StandardButton.Yes:
                    self.config.background_enabled = True
                    self.groups['background_group'].setChecked(True)
                else:
                    QMessageBox.warning(self, "Предупреждение",
                                    "Без фона в режиме 'Только с фоном и PNG' результат может быть некорректным")

            # Проверка наличия PNG файлов в папке overlay_images
            if self.config.overlay_only_mode:
                overlay_dir = os.path.join(self.config.base_dir, "overlay_images")
                if not os.path.exists(overlay_dir):
                    os.makedirs(overlay_dir, exist_ok=True)
                    QMessageBox.warning(self, "Предупреждение",
                                        "Папка overlay_images была создана. Пожалуйста, поместите туда PNG файлы с прозрачностью.")
                    return

                png_files = [f for f in os.listdir(overlay_dir) if f.lower().endswith('.png')]
                if not png_files:
                    QMessageBox.warning(self, "Предупреждение",
                                        "В папке overlay_images не найдено PNG файлов. Пожалуйста, добавьте файлы перед запуском.")
                    return

            is_infinite = self.infinite_generation.isChecked()
            num_images = self.num_images_spin.value()

            # Создаем и настраиваем поток
            self.processing_thread = ImageProcessingThread(
                self.config,
                folder_data,
                num_images,
                infinite=is_infinite
            )

            # Подключаем сигналы с явным указанием типа соединения
            self.processing_thread.image_generated.connect(
                self.update_folder_count,
                type=Qt.ConnectionType.QueuedConnection
            )
            self.processing_thread.finished.connect(self.processing_finished)
            self.processing_thread.error.connect(self.processing_error)
            self.processing_thread.backgrounds_depleted.connect(self.handle_backgrounds_depleted)

            # Блокируем элементы управления
            self._disable_controls()

            # Запускаем поток
            self.processing_thread.start()

        except Exception as e:
            print(f"Ошибка при запуске обработки: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Ошибка", f"Ошибка при запуске обработки: {str(e)}")

    def _disable_controls(self):
        """Блокировка элементов управления"""
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        for widgets in self.folder_widgets.values():
            widgets['select_btn'].setEnabled(False)
            widgets['checkbox'].setEnabled(False)
        self.infinite_generation.setEnabled(False)
        self.num_images_spin.setEnabled(False)
        self.save_config_btn.setEnabled(False)
        self.export_config_btn.setEnabled(False)
        self.import_config_btn.setEnabled(False)
        self.preset_name_field.setEnabled(False)

    def reset_interface_state(self):
        """Сбрасывает состояние интерфейса в исходное"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        for widgets in self.folder_widgets.values():
            widgets['select_btn'].setEnabled(True)
            widgets['checkbox'].setEnabled(True)
            widgets['counter_label'].setEnabled(True)
            widgets['counter_label'].setStyleSheet("color: #000000;")
        self.infinite_generation.setEnabled(True)
        self.num_images_spin.setEnabled(not self.infinite_generation.isChecked())
        self.save_config_btn.setEnabled(True)
        self.export_config_btn.setEnabled(True)
        self.import_config_btn.setEnabled(True)
        self.preset_name_field.setEnabled(True)

    def show_video_dialog(self):
        """Показывает диалог для создания видео"""
        try:
            self.video_dialog = VideoDialog(self)
            result = self.video_dialog.exec()

            if result == QDialog.DialogCode.Accepted:
                selected_folders = self.video_dialog.get_selected_folders()
                videos_count = self.video_dialog.get_videos_count()
                # Получаем длительность показа фотографии
                photo_duration = self.video_dialog.get_photo_duration()
                # Сохраняем в конфигурации
                self.config.slideshow_photo_duration = photo_duration

                if selected_folders:
                    self.create_multiple_slideshows(selected_folders, videos_count)
                else:
                    QMessageBox.warning(self, "Внимание", "Выберите хотя бы одну папку")
        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Ошибка при открытии диалога: {str(e)}")

    def create_multiple_slideshows(self, folder_numbers, videos_count):
        """Создает указанное количество видео-слайдшоу из выбранных папок"""
        try:
            # Обновляем конфигурацию
            self.update_config()

            # Получаем длительность показа фотографии из диалога
            photo_duration = getattr(self.video_dialog, 'get_photo_duration', lambda: 1)()
            # Сохраняем в конфигурации
            self.config.slideshow_photo_duration = photo_duration

            # Проверяем наличие фотографий в выбранных папках
            folder_photos = {}
            total_photos = 0

            for folder_num in folder_numbers:
                output_folder = os.path.join(self.config.base_dir, f"output_{folder_num}")
                if not os.path.exists(output_folder):
                    QMessageBox.warning(self, "Внимание", f"Папка output_{folder_num} не существует")
                    return

                photos = [f for f in os.listdir(output_folder)
                            if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

                if photos:
                    folder_photos[folder_num] = photos
                    total_photos += len(photos)
                else:
                    QMessageBox.warning(self, "Внимание", f"В папке output_{folder_num} нет фотографий")
                    return

            if not folder_photos:
                QMessageBox.warning(self, "Внимание", "В выбранных папках нет фотографий")
                return

            # Проверяем, достаточно ли фотографий для создания запрошенного количества видео
            min_photos_per_folder = min(len(photos) for photos in folder_photos.values())
            max_possible_videos = min_photos_per_folder

            if videos_count > max_possible_videos:
                reply = QMessageBox.question(
                    self,
                    "Внимание",
                    f"В папках недостаточно фотографий для создания {videos_count} видео.\n"
                    f"Максимально возможное количество: {max_possible_videos}.\n\n"
                    f"Хотите создать {max_possible_videos} видео?",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if reply == QMessageBox.StandardButton.Yes:
                    videos_count = max_possible_videos
                else:
                    return

            # Создаем директорию для видео, если она не существует
            video_dir = os.path.join(self.config.base_dir, "output_videos")
            os.makedirs(video_dir, exist_ok=True)

            # Создаем диалог прогресса
            progress_dialog = QDialog(self)
            progress_dialog.setWindowTitle("Создание видео")
            progress_dialog.setMinimumWidth(400)
            progress_layout = QVBoxLayout(progress_dialog)

            progress_label = QLabel(f"Создание видео 1 из {videos_count}...")
            progress_layout.addWidget(progress_label)

            progress_bar = QProgressBar()
            progress_bar.setRange(0, videos_count)
            progress_bar.setValue(0)
            progress_layout.addWidget(progress_bar)

            cancel_button = QPushButton("Отмена")
            cancel_button.clicked.connect(progress_dialog.reject)
            progress_layout.addWidget(cancel_button)

            progress_dialog.show()

            # Список успешно созданных видео
            created_videos = []

            # Флаг для отслеживания отмены
            cancelled = [False]

            def on_dialog_rejected():
                cancelled[0] = True

            progress_dialog.rejected.connect(on_dialog_rejected)

            # Создаем видео
            for i in range(videos_count):
                if cancelled[0]:
                    break

                # Обновляем прогресс
                progress_label.setText(f"Создание видео {i+1} из {videos_count}...")
                progress_bar.setValue(i)
                QApplication.processEvents()

                # Создаем видео
                video_path = self.create_multi_folder_slideshow(folder_photos, i)

                if video_path and os.path.exists(video_path):
                    created_videos.append(video_path)
                else:
                    # Если создание видео не удалось, прерываем процесс
                    QMessageBox.warning(
                        self,
                        "Предупреждение",
                        f"Не удалось создать видео {i+1}. Процесс будет остановлен."
                    )
                    break

            # Закрываем диалог прогресса
            progress_dialog.close()

            # Выводим результаты
            if created_videos:
                if len(created_videos) == videos_count:
                    message = f"Успешно создано {len(created_videos)} видео."
                else:
                    message = f"Создано {len(created_videos)} из {videos_count} запрошенных видео."

                QMessageBox.information(self, "Успех", message)

                # Спрашиваем пользователя, хочет ли он открыть последнее созданное видео
                if len(created_videos) > 0:
                    reply = QMessageBox.question(
                        self,
                        "Открыть видео",
                        "Хотите открыть последнее созданное видео?",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.Yes
                    )

                    if reply == QMessageBox.StandardButton.Yes:
                        # Открываем последнее созданное видео в ассоциированной программе
                        latest_video = created_videos[-1]
                        import subprocess
                        import platform

                        if platform.system() == 'Windows':
                            os.startfile(latest_video)
                        elif platform.system() == 'Darwin':  # macOS
                            subprocess.call(('open', latest_video))
                        else:  # Linux
                            subprocess.call(('xdg-open', latest_video))
            else:
                QMessageBox.warning(
                    self,
                    "Предупреждение",
                    "Не удалось создать ни одного видео."
                )

        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Ошибка при создании видео: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_slideshow_video(self, folder_numbers):
        """Создает видео-слайдшоу, беря по одной фотографии из каждой выбранной папки"""
        try:
            # Обновляем конфигурацию
            self.update_config()

            # Проверяем наличие фотографий в выбранных папках
            folder_photos = {}
            for folder_num in folder_numbers:
                output_folder = os.path.join(self.config.base_dir, f"output_{folder_num}")
                if not os.path.exists(output_folder):
                    QMessageBox.warning(self, "Внимание", f"Папка output_{folder_num} не существует")
                    return

                photos = [f for f in os.listdir(output_folder)
                            if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

                if photos:
                    folder_photos[folder_num] = photos
                else:
                    QMessageBox.warning(self, "Внимание", f"В папке output_{folder_num} нет фотографий")
                    return

            if not folder_photos:
                QMessageBox.warning(self, "Внимание", "В выбранных папках нет фотографий")
                return

            # Создаем директорию для видео, если она не существует
            video_dir = os.path.join(self.config.base_dir, "output_videos")
            os.makedirs(video_dir, exist_ok=True)

            # Создаем диалог прогресса
            progress_dialog = QDialog(self)
            progress_dialog.setWindowTitle("Создание видео")
            progress_dialog.setMinimumWidth(400)
            progress_layout = QVBoxLayout(progress_dialog)

            progress_label = QLabel("Создаем видео из фотографий...")
            progress_layout.addWidget(progress_label)

            progress_bar = QProgressBar()
            progress_bar.setRange(0, 0)  # Бесконечный прогресс
            progress_layout.addWidget(progress_bar)

            progress_dialog.show()

            # Используем QApplication.processEvents() для обновления интерфейса
            QApplication.processEvents()

            # Вызываем функцию создания видео из нескольких папок
            video_path = self.create_multi_folder_slideshow(folder_photos)

            # Закрываем диалог прогресса
            progress_dialog.close()

            if video_path and os.path.exists(video_path):
                QMessageBox.information(
                    self,
                    "Успех",
                    f"Видео успешно создано и сохранено:\n{video_path}"
                )

                # Спрашиваем пользователя, хочет ли он открыть видео
                reply = QMessageBox.question(
                    self,
                    "Открыть видео",
                    "Хотите открыть созданное видео?",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if reply == QMessageBox.StandardButton.Yes:
                    # Открываем видео в ассоциированной программе
                    import subprocess
                    import platform

                    if platform.system() == 'Windows':
                        os.startfile(video_path)
                    elif platform.system() == 'Darwin':  # macOS
                        subprocess.call(('open', video_path))
                    else:  # Linux
                        subprocess.call(('xdg-open', video_path))
            else:
                QMessageBox.warning(
                    self,
                    "Предупреждение",
                    "Не удалось создать видео. Проверьте наличие фотографий в папках."
                )
        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Ошибка при создании видео: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_multi_folder_slideshow(self, folder_photos, video_index=0):
        try:
            # Импортируем необходимые модули
            import cv2
            from datetime import datetime

            # Создаем имя выходного файла
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folders_str = "-".join(str(num) for num in folder_photos.keys())
            video_name = f"slideshow_folders_{folders_str}_{timestamp}_{video_index}.mp4"
            video_path = os.path.join(self.config.video_dir, video_name)

            print(f"Начинаю создание видео #{video_index+1} из фотографий в папках: {folders_str}")

            # Получаем список фотографий для этого видео (одна фотография из каждой папки)
            video_photos = {}
            for folder_num, photos in folder_photos.items():
                # Получаем только одну фотографию для этого видео
                if video_index < len(photos):
                    video_photos[folder_num] = [photos[video_index]]
                else:
                    print(f"В папке output_{folder_num} недостаточно фотографий для видео #{video_index+1}")
                    return None

            # Определяем размер видео по первой фотографии
            first_folder = list(video_photos.keys())[0]
            first_photo = video_photos[first_folder][0]
            first_img_path = os.path.join(self.config.base_dir, f"output_{first_folder}", first_photo)
            first_img = cv2.imread(first_img_path)

            if first_img is None:
                print(f"Ошибка при чтении изображения: {first_img_path}")
                return None

            height, width, _ = first_img.shape

            # Получаем длительность показа фотографии из конфигурации
            photo_duration = getattr(self.config, 'slideshow_photo_duration', 1)

            # Создаем видеозапись
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # кодек для mp4
            fps = 1  # 1 кадр в секунду
            video_writer = cv2.VideoWriter(video_path, fourcc, fps, (width, height))

            # Список для хранения путей использованных фотографий (для удаления)
            used_photos = []

            # Добавляем фотографии из всех папок в видео
            for folder_num, photos in video_photos.items():
                for photo in photos:
                    img_path = os.path.join(self.config.base_dir, f"output_{folder_num}", photo)
                    img = cv2.imread(img_path)

                    if img is None:
                        print(f"Пропускаю поврежденное изображение: {img_path}")
                        continue

                    # Проверяем размер и при необходимости изменяем
                    if img.shape[0] != height or img.shape[1] != width:
                        img = cv2.resize(img, (width, height))

                    # Добавляем изображение в видео несколько раз, в зависимости от длительности
                    for _ in range(photo_duration):
                        video_writer.write(img)

                    # Добавляем путь в список использованных фотографий
                    used_photos.append(img_path)

                    # Удаляем фотографию из списка доступных в словаре folder_photos
                    # чтобы избежать её повторного использования
                    folder_photos[folder_num].remove(photo)

            # Закрываем видеозапись
            video_writer.release()

            # Удаляем использованные фотографии
            for photo_path in used_photos:
                try:
                    os.remove(photo_path)
                    print(f"Удален файл: {photo_path}")
                except Exception as e:
                    print(f"Не удалось удалить файл {photo_path}: {str(e)}")

            print(f"Видео создано успешно: {video_path}")
            return video_path

        except Exception as e:
            print(f"Ошибка при создании видео: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def update_folder_count(self, folder_idx, count):
        if folder_idx in self.folder_widgets:
            try:
                widgets = self.folder_widgets[folder_idx]
                widgets['generated_count'] = count
                widgets['counter_label'].setText(f"Сгенерировано: {count}")
            except Exception as e:
                print(f"Ошибка при обновлении счетчика: {str(e)}")

    def _update_counter_label(self, folder_idx, count):
        """Безопасное обновление метки счетчика"""
        if folder_idx in self.folder_widgets:
            label = self.folder_widgets[folder_idx]['counter_label']
            label.setText(f"Сгенерировано: {count}")

    def handle_backgrounds_depleted(self):
        """Обработчик события окончания фонов"""
        # Останавливаем текущий процесс обработки
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.stop()
            self.processing_thread.wait()

        # Восстанавливаем состояние всех элементов интерфейса
        self.reset_interface_state()

        # Даем время монитору подгрузить новые фоны
        time.sleep(5)  # Ждем 5 секунд

        # Проверяем, появились ли новые фоны
        if not os.path.exists(os.path.join(self.config.base_dir, "backgrounds")) or \
           not any(f.lower().endswith(('.jpg', '.png', '.jpeg')) for f in os.listdir(os.path.join(self.config.base_dir, "backgrounds"))):
            # Показываем сообщение об ошибке
            QMessageBox.warning(
                self,
                "Внимание",
                "В директории backgrounds нет изображений и не удалось загрузить новые!\n"
                "Проверьте подключение к серверу и попробуйте снова."
            )

        # Формируем сообщение о результатах
        results = []
        total_generated = 0
        for idx, widgets in self.folder_widgets.items():
            if widgets['checkbox'].isChecked() and widgets['input_file']:
                count = widgets['generated_count']
                total_generated += count
                results.append(f"Папка {idx}: {count} изображений")

        # Показываем статистику генерации
        if total_generated > 0:
            QMessageBox.information(
                self,
                "Статистика генерации",
                "До завершения было сгенерировано:\n" + "\n".join(results)
            )

    def stop_processing(self):
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.stop()
            self.processing_thread.wait()

    def on_overlay_group_toggled(self, checked):
        """Обработчик включения/выключения группы 'Наложение PNG'"""
        # Если группа выключается, то отключаем режим "только с фоном и PNG"
        if not checked and self.overlay_only_mode.isChecked():
            self.overlay_only_mode.setChecked(False)

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def processing_finished(self):
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        for widgets in self.folder_widgets.values():
            widgets['select_btn'].setEnabled(True)
            widgets['checkbox'].setEnabled(True)
        self.infinite_generation.setEnabled(True)
        self.num_images_spin.setEnabled(not self.infinite_generation.isChecked())
        self.save_config_btn.setEnabled(True)
        self.export_config_btn.setEnabled(True)
        self.import_config_btn.setEnabled(True)
        self.preset_name_field.setEnabled(True)

        # Формируем сообщение о результатах, только если есть успешно обработанные изображения
        results = []
        total_generated = 0
        for idx, widgets in self.folder_widgets.items():
            # Проверяем включен ли чекбокс папки
            if widgets['checkbox'].isChecked():
                # Проверяем режим overlay_only_mode или наличие input_file
                if self.config.overlay_only_mode or widgets['input_file']:
                    count = widgets['generated_count']
                    total_generated += count
                    results.append(f"Папка {idx}: {count} изображений")

        if total_generated > 0:
            QMessageBox.information(
                self,
                "Готово",
                "Обработка завершена!\n" + "\n".join(results)
            )

    def processing_error(self, error_message):
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.select_input_btn.setEnabled(True)

        QMessageBox.critical(
            self,
            "Ошибка",
            f"Произошла ошибка при обработке:\n{error_message}"
        )

    def on_overlay_mode_changed(self, state):
        """Обработчик изменения режима работы с наложениями"""
        # Обновляем активацию кнопки запуска
        if state:  # Если режим только с фоном включен
            # Проверяем, есть ли хотя бы одна выбранная папка
            can_start = any(widgets['checkbox'].isChecked() for widgets in self.folder_widgets.values())

            # Включаем опцию фона, если она выключена
            if not self.groups['background_group'].isChecked():
                reply = QMessageBox.question(
                    self,
                    'Внимание',
                    'В режиме "Только с фоном и PNG" требуется фон. Включить фон?',
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.Yes
                )

                if reply == QMessageBox.StandardButton.Yes:
                    self.groups['background_group'].setChecked(True)
        else:  # Если стандартный режим
            can_start = any(
                widgets['checkbox'].isChecked() and widgets['input_file'] is not None
                for widgets in self.folder_widgets.values()
            )

        self.start_btn.setEnabled(can_start)

    def closeEvent(self, event):
        try:
            # Проверяем, идет ли обработка, и показываем диалог подтверждения
            if self.processing_thread and self.processing_thread.isRunning():
                reply = QMessageBox.question(
                    self,
                    'Подтверждение',
                    'Обработка еще не завершена. Вы уверены, что хотите выйти?',
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.No:
                    event.ignore()
                    return
                else:
                    # Останавливаем обработку, но не ждем её завершения
                    self.processing_thread.stop()

            # Быстро обновляем конфигурацию
            self.update_config()

            # Асинхронно сохраняем конфигурацию
            self.save_config_async()

            # Останавливаем монитор фонов без блокировки
            if hasattr(self, 'background_monitor'):
                self.background_monitor.stop()

            # Принимаем событие закрытия
            event.accept()

        except Exception as e:
            print(f"Ошибка при закрытии приложения: {e}")
            event.accept()  # В любом случае закрываем приложение

    def export_config(self):
        """Экспортирует текущую конфигурацию в файл"""
        try:
            # Обновляем конфигурацию из текущих значений UI
            self.update_config()

            # Обновляем имя пресета из поля ввода
            self.config.preset_name = self.preset_name_field.text()

            # Запрашиваем у пользователя место для сохранения
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Экспорт конфигурации",
                os.path.join(self.config.config_dir, f"{self.config.preset_name}.json"),  # Используем директорию конфигураций
                "JSON файлы (*.json)"
            )

            if not file_path:
                return  # Пользователь отменил

            # Добавляем расширение .json, если его нет
            if not file_path.lower().endswith('.json'):
                file_path += '.json'

            # Конвертируем конфиг в словарь и сохраняем в файл
            config_dict = self.config.to_dict()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)

            QMessageBox.information(
                self,
                "Успех",
                f"Конфигурация '{self.config.preset_name}' успешно экспортирована в файл:\n{file_path}"
            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Ошибка",
                f"Не удалось экспортировать конфигурацию:\n{str(e)}"
            )

    def import_config(self):
        """Импортирует конфигурацию из файла"""
        try:
            # Получаем путь к файлу от пользователя
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Импорт конфигурации",
                self.config.config_dir,  # Используем директорию конфигураций
                "JSON файлы (*.json)"
            )

            if not file_path:
                return  # Пользователь отменил

            # Читаем файл конфигурации
            with open(file_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)

            # Создаем новый объект конфигурации
            imported_config = UniqualizerConfig()

            # Загружаем настройки из импортированного словаря
            imported_config.from_dict(config_dict)

            # Спрашиваем пользователя, хочет ли он применить импортированную конфигурацию
            preset_name = imported_config.preset_name

            reply = QMessageBox.question(
                self,
                "Импорт конфигурации",
                f"Вы хотите применить импортированную конфигурацию '{preset_name}'?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Сохраняем важные локальные настройки
                local_base_dir = self.config.base_dir

                # Применяем импортированную конфигурацию
                self.config = imported_config

                # Восстанавливаем локальные пути
                self.config.base_dir = local_base_dir
                self.config.input_dir = os.path.join(local_base_dir, "input_images")
                self.config.output_dir = os.path.join(local_base_dir, "output_images")
                self.config.emoji_dir = os.path.join(local_base_dir, "emojis")
                self.config.config_dir = os.path.join(local_base_dir, "Конфигурации")

                # Обновляем UI с новой конфигурацией
                self.apply_config_to_ui()

                QMessageBox.information(
                    self,
                    "Успех",
                    f"Конфигурация '{preset_name}' успешно импортирована и применена."
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "Ошибка",
                f"Не удалось импортировать конфигурацию:\n{str(e)}"
            )

    def generate_preview(self):
        """Создает предварительный просмотр креатива с текущими настройками"""
        try:
            self.update_config()

            # Для режима "Только с фоном и PNG" показываем уведомление, что предпросмотр недоступен
            if self.config.overlay_only_mode:
                QMessageBox.information(
                    self,
                    "Информация",
                    "Предпросмотр недоступен в режиме 'Только с фоном и PNG'."
                )
                return

            # Стандартный режим с выбором изображений
            available_items = {}
            for idx, widgets in self.folder_widgets.items():
                if widgets['checkbox'].isChecked() and widgets['input_file']:
                    available_items[idx] = {
                        'path': widgets['input_file'],
                        'name': os.path.basename(widgets['input_file']),
                        'folder_idx': idx
                    }

            if not available_items:
                QMessageBox.warning(self, "Предупреждение",
                                  "Пожалуйста, выберите хотя бы одно изображение для предпросмотра")
                return

            # Показываем кнопку "Назад" только если доступно более одного изображения
            has_multiple_items = len(available_items) > 1

            # Функция для обработки выбора изображения и создания предпросмотра
            def process_selected_item(item_data):
                if not item_data:
                    return

                # Получаем путь к изображению
                input_file = item_data.get('path')
                folder_idx = item_data.get('folder_idx')

                # Настраиваем выходную директорию на соответствующую output_X
                if folder_idx:
                    self.config.output_dir = os.path.join(self.config.base_dir, f"output_{folder_idx}")

                # Создаем временный файл для результата
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                    output_path = temp_file.name

                try:
                    # Импортируем process_image для обработки
                    from uniqualizer import process_image

                    success = process_image(input_file, output_path, self.config)

                    if success:
                        # Если окно предпросмотра уже открыто, обновляем его
                        if hasattr(self, 'preview_dialog') and self.preview_dialog.isVisible():
                            self.preview_dialog.item_data = item_data  # Сохраняем данные для регенерации
                            self.preview_dialog.load_image(output_path)
                        else:
                            # Иначе создаем новое окно с кнопкой "Назад", если у нас несколько изображений
                            self.preview_dialog = PreviewDialog(self, output_path, show_back_button=has_multiple_items, item_data=item_data)

                            # Подключаем обработчик кнопки "Назад", только если есть несколько изображений
                            if has_multiple_items:
                                def on_back_button():
                                    # Скрываем диалог предпросмотра, но не закрываем его
                                    self.preview_dialog.hide()

                                    # Показываем диалог выбора изображения
                                    dialog = ImageSelectionDialog(self, available_items)
                                    result = dialog.exec()

                                    if result == QDialog.DialogCode.Accepted:
                                        # Пользователь выбрал другое изображение
                                        new_item_data = dialog.get_selected_item()
                                        if new_item_data and (new_item_data != item_data):
                                            # Обрабатываем новое выбранное изображение
                                            process_selected_item(new_item_data)
                                        else:
                                            # Если пользователь выбрал то же самое изображение или
                                            # не выбрал ничего, просто показываем текущий предпросмотр
                                            self.preview_dialog.show()
                                    else:
                                        # Пользователь закрыл диалог - возвращаемся к текущему предпросмотру
                                        self.preview_dialog.show()

                                # Подключаем обработчик
                                self.preview_dialog.back_clicked.connect(on_back_button)

                            self.preview_dialog.show()
                    else:
                        QMessageBox.critical(self, "Ошибка",
                                    "Не удалось создать предпросмотр. Проверьте настройки.")
                except Exception as e:
                    QMessageBox.critical(self, "Ошибка",
                                f"Ошибка при создании предпросмотра: {str(e)}")

            # Определяем, что использовать для предпросмотра
            if has_multiple_items:
                # Если доступно более одного изображения, показываем диалог выбора
                dialog = ImageSelectionDialog(self, available_items)
                result = dialog.exec()

                if result == QDialog.DialogCode.Accepted:
                    item_data = dialog.get_selected_item()
                    process_selected_item(item_data)
            else:
                # Используем единственное доступное изображение
                item_data = list(available_items.values())[0]
                process_selected_item(item_data)

        except Exception as e:
            QMessageBox.critical(self, "Ошибка", f"Ошибка при создании предпросмотра: {str(e)}")
            import traceback
            traceback.print_exc()

class LimitedHeightComboBox(QComboBox):
    def __init__(self, parent=None):
        super().__init__(parent)

        # Делаем выпадающий список редактируемым, но только для чтения
        self.setEditable(True)
        self.lineEdit().setReadOnly(True)

        # Устанавливаем фиксированную высоту представления
        self.view().setMinimumHeight(120)
        self.view().setMaximumHeight(120)
        self.view().setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Основной стиль
        self.setStyleSheet("""
            QComboBox {
                border: 1px solid #aaa;
                border-radius: 3px;
                padding: 1px 3px;
                min-width: 6em;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #aaa;
                background: white;
                selection-background-color: #007bff;
            }
        """)

    def showPopup(self):
        # Убедимся, что выпадающий список имеет правильную высоту и стиль
        popup = self.findChild(QFrame)
        if popup:
            popup.setMaximumHeight(120)

        # Стандартное отображение всплывающего списка
        super().showPopup()

class PreviewDialog(QDialog):
    back_clicked = pyqtSignal()

    def __init__(self, parent=None, image_path=None, show_back_button=False, item_data=None):
        super().__init__(parent)
        # Сохраняем данные об изображении для возможности регенерации
        self.item_data = item_data

        self.setWindowTitle("Предпросмотр креатива")
        self.setMinimumSize(600, 800)
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        layout = QVBoxLayout(self)

        # Виджет для изображения
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.image_label.setStyleSheet("""
            QLabel {
                background-color: #ffffff;
                border: 1px solid #cccccc;
                border-radius: 5px;
            }
        """)

        # Сохраняем путь к изображению для загрузки после showEvent
        self.image_path = image_path
        if not image_path:
            self.image_label.setText("Изображение не загружено")

        layout.addWidget(self.image_label)

        # Кнопки внизу
        button_layout = QHBoxLayout()

        # Добавляем кнопку Назад только если указано show_back_button
        self.back_button = None
        if show_back_button:
            self.back_button = QPushButton("Назад")
            self.back_button.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_ArrowBack))
            self.back_button.clicked.connect(self.on_back_clicked)
            button_layout.addWidget(self.back_button)

        # Добавляем растягивающий элемент только если есть кнопка Назад
        if show_back_button:
            button_layout.addStretch()

        # Существующие кнопки
        regenerate_button = QPushButton("Сгенерировать новый предпросмотр")
        regenerate_button.clicked.connect(self.regenerate_preview)

        close_button = QPushButton("Закрыть")
        close_button.clicked.connect(self.accept)

        # Добавляем кнопки в layout
        button_layout.addWidget(regenerate_button)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)

    def on_back_clicked(self):
        """Обработчик нажатия кнопки Назад"""
        self.back_clicked.emit()
        # Не закрываем диалог, так как это будет сделано в обработчике сигнала

    # Метод для показа/скрытия кнопки "Назад"
    def set_back_button_visible(self, visible):
        """Показывает или скрывает кнопку Назад"""
        if self.back_button:
            self.back_button.setVisible(visible)

    def showEvent(self, event):
        """Переопределяем showEvent для загрузки изображения после отображения окна"""
        super().showEvent(event)
        # Используем QTimer.singleShot для загрузки изображения после отрисовки интерфейса
        if hasattr(self, 'image_path') and self.image_path:
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(100, lambda: self.load_image(self.image_path))

    def load_image(self, image_path):
        """Загружает изображение из файла и отображает его с сохранением пропорций"""
        self.image_path = image_path  # Сохраняем путь для возможного пересчета
        pixmap = QPixmap(image_path)
        if not pixmap.isNull():
            # Получаем текущие размеры контейнера для изображения
            container_width = self.image_label.width()
            container_height = self.image_label.height()

            # Проверяем, что размеры контейнера валидны
            if container_width <= 0 or container_height <= 0:
                container_width = self.width() - 40  # Отступы
                container_height = self.height() - 100  # За вычетом места для кнопок

            # Масштабируем изображение, сохраняя пропорции
            scaled_pixmap = pixmap.scaled(
                container_width, container_height,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            self.image_label.setPixmap(scaled_pixmap)
        else:
            self.image_label.setText(f"Не удалось загрузить изображение: {image_path}")

    def resizeEvent(self, event):
        """Обработчик изменения размера окна"""
        super().resizeEvent(event)
        # Перемасштабируем изображение при изменении размера окна
        if hasattr(self, 'image_path') and self.image_path:
            self.load_image(self.image_path)

    def regenerate_preview(self):
        """Обработчик кнопки регенерации предпросмотра"""
        # Если у нас сохранены данные об изображении, используем их
        if hasattr(self, 'item_data') and self.item_data:
            parent = self.parent()

            # Обновляем конфигурацию
            parent.update_config()

            # Получаем путь к изображению и индекс папки
            input_file = self.item_data.get('path')
            folder_idx = self.item_data.get('folder_idx')

            # Настраиваем выходную директорию
            if folder_idx:
                parent.config.output_dir = os.path.join(parent.config.base_dir, f"output_{folder_idx}")

            # Создаем временный файл для результата
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                output_path = temp_file.name

            try:
                # Импортируем process_image для обработки
                from uniqualizer import process_image

                success = process_image(input_file, output_path, parent.config)

                if success:
                    # Обновляем изображение в предпросмотре
                    self.load_image(output_path)
                else:
                    QMessageBox.critical(parent, "Ошибка",
                                "Не удалось создать предпросмотр. Проверьте настройки.")
            except Exception as e:
                QMessageBox.critical(parent, "Ошибка",
                            f"Ошибка при создании предпросмотра: {str(e)}")
        else:
            # Если данных нет, используем стандартный метод (может показать диалог выбора)
            if hasattr(self.parent(), 'generate_preview'):
                self.parent().generate_preview()

class ImageSelectionDialog(QDialog):
    """Диалог для выбора изображения или папки из нескольких доступных с предпросмотром"""
    def __init__(self, parent=None, items=None):
        super().__init__(parent)

        self.selected_item = None
        self.items = items or {}
        self.overlay_only_mode = getattr(parent.config, 'overlay_only_mode', False) if parent else False

        self.setWindowTitle("Выбор изображения для предпросмотра")
        self.setMinimumWidth(700)
        self.setMinimumHeight(400)
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
            QListWidget {
                background-color: white;
                border: 1px solid #dddddd;
                border-radius: 6px;
                padding: 5px;
                font-size: 11pt;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #eeeeee;
                margin: 2px;
            }
            QListWidget::item:selected {
                background-color: #f0f7ff;
                border: 1px solid #99ccff;
                border-radius: 4px;
                color: #000000;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
                border-radius: 4px;
            }
            QLabel {
                color: #333333;
            }
            QLabel#previewTitle {
                font-weight: bold;
                font-size: 12pt;
            }
            QFrame#previewFrame {
                background-color: white;
                border: 1px solid #dddddd;
                border-radius: 6px;
            }
        """)

        # Основной горизонтальный layout
        main_layout = QHBoxLayout(self)

        # Левая колонка для списка
        left_column = QVBoxLayout()

        # Заголовок
        if self.overlay_only_mode:
            label = QLabel("Выберите папку для предпросмотра:")
        else:
            label = QLabel("Выберите изображение:")
        label.setFont(QFont("Segoe UI", 10))
        left_column.addWidget(label)

        # Список изображений
        self.list_widget = QListWidget()
        self.list_widget.setIconSize(QSize(48, 48))
        self.list_widget.setAlternatingRowColors(False)
        self.list_widget.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        self.list_widget.currentItemChanged.connect(self.on_selection_changed)
        left_column.addWidget(self.list_widget)

        # Кнопки
        button_layout = QHBoxLayout()

        preview_button = QPushButton("Предпросмотр")
        preview_button.clicked.connect(self.accept)

        cancel_button = QPushButton("Отмена")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet("""
            background-color: #f8f9fa;
            color: #333333;
            border: 1px solid #dddddd;
        """)

        button_layout.addStretch()
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(preview_button)

        left_column.addLayout(button_layout)

        # Правая колонка для предпросмотра
        right_column = QVBoxLayout()

        preview_title = QLabel("Предпросмотр:")
        preview_title.setObjectName("previewTitle")
        right_column.addWidget(preview_title)

        # Фрейм для предпросмотра
        self.preview_frame = QFrame()
        self.preview_frame.setObjectName("previewFrame")
        self.preview_frame.setMinimumSize(300, 300)
        self.preview_frame.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Layout для фрейма предпросмотра
        preview_layout = QVBoxLayout(self.preview_frame)

        # Метка для отображения изображения
        self.preview_label = QLabel("Предпросмотр выбранного изображения")
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setStyleSheet("background-color: transparent;")
        preview_layout.addWidget(self.preview_label)

        right_column.addWidget(self.preview_frame)

        # Размер выбранного файла и информация
        self.info_label = QLabel()
        right_column.addWidget(self.info_label)

        # Добавляем обе колонки в основной layout
        main_layout.addLayout(left_column, 1)  # Весовой коэффициент 1
        main_layout.addLayout(right_column, 2)  # Весовой коэффициент 2

        # Заполняем список изображениями
        self.populate_list()

    def populate_list(self):
        """Заполняет список доступными изображениями/папками"""
        if not self.items:
            return

        for idx, item_info in self.items.items():
            path = item_info.get('path')
            name = item_info.get('name')
            folder_idx = item_info.get('folder_idx')

            # Создаем элемент списка с увеличенной иконкой
            item = QListWidgetItem(name or f"Папка {idx}")

            # Если есть изображение, добавляем иконку, иначе используем стандартную иконку папки
            if path and os.path.exists(path):
                # Создаем и настраиваем иконку из изображения
                pixmap = QPixmap(path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    icon = QIcon(scaled_pixmap)
                    item.setIcon(icon)
            else:
                # Для режима только с фоном используем стандартную иконку папки
                icon = self.style().standardIcon(QStyle.StandardPixmap.SP_DirIcon)
                item.setIcon(icon)

            # Сохраняем данные об элементе
            item.setData(Qt.ItemDataRole.UserRole, item_info)

            # Добавляем элемент в список
            self.list_widget.addItem(item)

        # Выделяем первый элемент
        if self.list_widget.count() > 0:
            self.list_widget.setCurrentRow(0)
            self.update_preview(self.list_widget.item(0))

    def on_selection_changed(self, current, previous):
        """Обработчик изменения выбора в списке"""
        if current:
            self.update_preview(current)

    def update_preview(self, item):
        """Обновляет предпросмотр выбранного изображения"""
        if not item:
            return

        item_data = item.data(Qt.ItemDataRole.UserRole)
        if not item_data:
            return

        image_path = item_data.get('path')
        folder_idx = item_data.get('folder_idx')

        if self.overlay_only_mode or not image_path or not os.path.exists(image_path):
            # В режиме только с фоном или если файл недоступен,
            # показываем информационную метку
            if self.overlay_only_mode:
                self.preview_label.setText(f"Предпросмотр будет создан\nдля папки output_{folder_idx}\nс использованием фона и наложения PNG")
            else:
                self.preview_label.setText("Изображение недоступно")
            self.info_label.setText("")
            return

        # Загружаем изображение
        pixmap = QPixmap(image_path)
        if pixmap.isNull():
            self.preview_label.setText("Не удалось загрузить изображение")
            self.info_label.setText("")
            return

        # Рассчитываем размер для отображения
        preview_width = self.preview_frame.width() - 20  # Отступы
        preview_height = self.preview_frame.height() - 20
        scaled_pixmap = pixmap.scaled(
            preview_width,
            preview_height,
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        )

        # Устанавливаем изображение
        self.preview_label.setPixmap(scaled_pixmap)

        # Обновляем информацию о файле
        file_size_bytes = os.path.getsize(image_path)
        if file_size_bytes < 1024:
            file_size_str = f"{file_size_bytes} байт"
        elif file_size_bytes < 1024 * 1024:
            file_size_str = f"{file_size_bytes / 1024:.1f} КБ"
        else:
            file_size_str = f"{file_size_bytes / (1024 * 1024):.1f} МБ"

        img_width = pixmap.width()
        img_height = pixmap.height()

        self.info_label.setText(f"Размер: {img_width}×{img_height} пикселей | {file_size_str}")

    def resizeEvent(self, event):
        """Обработчик изменения размера окна"""
        super().resizeEvent(event)
        # Обновляем предпросмотр при изменении размера
        current_item = self.list_widget.currentItem()
        if current_item:
            self.update_preview(current_item)

    def get_selected_item(self):
        """Возвращает данные о выбранном элементе"""
        current_item = self.list_widget.currentItem()
        if current_item:
            return current_item.data(Qt.ItemDataRole.UserRole)
        return None

class LightPalette(QPalette):
    def __init__(self):
        super().__init__()
        # Основные цвета
        self.setColor(QPalette.ColorRole.Window, QColor("#f0f0f0"))
        self.setColor(QPalette.ColorRole.WindowText, QColor("#333333"))
        self.setColor(QPalette.ColorRole.Base, QColor("#ffffff"))
        self.setColor(QPalette.ColorRole.AlternateBase, QColor("#f9f9f9"))
        self.setColor(QPalette.ColorRole.ToolTipBase, QColor("#ffffff"))
        self.setColor(QPalette.ColorRole.ToolTipText, QColor("#333333"))
        self.setColor(QPalette.ColorRole.Text, QColor("#333333"))
        self.setColor(QPalette.ColorRole.Button, QColor("#f0f0f0"))
        self.setColor(QPalette.ColorRole.ButtonText, QColor("#333333"))
        self.setColor(QPalette.ColorRole.BrightText, QColor("#ffffff"))
        self.setColor(QPalette.ColorRole.Link, QColor("#007bff"))
        self.setColor(QPalette.ColorRole.Highlight, QColor("#007bff"))
        self.setColor(QPalette.ColorRole.HighlightedText, QColor("#ffffff"))

def main_uniq():
    # Включаем поддержку HiDPI
    os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"
    QApplication.setHighDpiScaleFactorRoundingPolicy(
        Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
    )

    app = QApplication(sys.argv)

    # Устанавливаем стиль и палитру
    app.setStyle(QStyleFactory.create("Fusion"))
    app.setPalette(LightPalette())

    # Устанавливаем шрифт по умолчанию
    font = QFont("Segoe UI", 9)
    app.setFont(font)
    # Создаем и показываем главное окно
    window = UniqualizerGUI()
    window.show()

    sys.exit(app.exec())

 if __name__ == "__main__":
      multiprocessing.freeze_support()
      main_uniq()
