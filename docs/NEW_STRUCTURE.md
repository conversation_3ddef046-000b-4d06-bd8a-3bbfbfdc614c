# Новая структура проекта MagicUniq

## 📁 Обзор структуры

Проект был реорганизован для улучшения модульности, поддерживаемости и расширяемости.

```
magic_uniq/
├── src/
│   ├── magic_uniq/                 # Основной пакет
│   │   ├── __init__.py
│   │   ├── core/                   # Основная бизнес-логика
│   │   │   ├── __init__.py
│   │   │   ├── image_processor.py  # Обработка изображений
│   │   │   ├── video_processor.py  # Обработка видео
│   │   │   ├── classes.py          # Вспомогательные классы
│   │   │   ├── effects/            # Эффекты
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base.py         # Базовые классы эффектов
│   │   │   │   ├── image_effects.py
│   │   │   │   ├── video_effects.py
│   │   │   │   └── snow_effect.py
│   │   │   └── metadata/           # Работа с метаданными
│   │   │       ├── __init__.py
│   │   │       ├── manager.py
│   │   │       ├── generators.py
│   │   │       └── metadata_config.py
│   │   ├── interfaces/             # Пользовательские интерфейсы
│   │   │   ├── __init__.py
│   │   │   ├── cli/                # CLI интерфейс
│   │   │   │   ├── __init__.py
│   │   │   │   └── main.py
│   │   │   ├── gui/                # Desktop GUI
│   │   │   │   ├── __init__.py
│   │   │   │   └── main.py
│   │   │   └── web/                # Web интерфейс
│   │   │       ├── __init__.py
│   │   │       ├── app.py
│   │   │       └── static/         # Веб-ресурсы
│   │   ├── services/               # Внешние сервисы
│   │   │   ├── __init__.py
│   │   │   ├── image_server.py
│   │   │   ├── content_generator.py
│   │   │   ├── image_server_stub.py      # Заглушки для тестирования
│   │   │   └── content_generator_stub.py
│   │   ├── config/                 # Конфигурация
│   │   │   ├── __init__.py
│   │   │   ├── settings.py         # Централизованные настройки
│   │   │   ├── validation.py       # Валидация конфигурации
│   │   │   └── video_settings.py   # Настройки видео
│   │   └── utils/                  # Утилиты
│   │       ├── __init__.py
│   │       ├── file_utils.py       # Работа с файлами
│   │       └── image_utils.py      # Работа с изображениями
│   └── resources/                  # Статические ресурсы
│       ├── backgrounds/
│       ├── overlays/
│       ├── emojis/
│       └── templates/
├── tests/                          # Тесты
│   ├── __init__.py
│   ├── unit/
│   ├── integration/
│   └── fixtures/
├── docs/                           # Документация
├── configs/                        # Конфигурационные файлы
├── data/                          # Рабочие данные
│   ├── input/
│   └── output/
├── main.py                        # Единая точка входа
├── pyproject.toml
└── README.md
```

## 🔧 Ключевые улучшения

### 1. Четкое разделение ответственности

- **`core/`** - Основная бизнес-логика обработки медиа
- **`interfaces/`** - Пользовательские интерфейсы (CLI, GUI, Web)
- **`services/`** - Внешние сервисы и API
- **`config/`** - Централизованное управление настройками
- **`utils/`** - Вспомогательные функции и утилиты

### 2. Условные импорты

Все модули поддерживают работу без тяжелых зависимостей:

```python
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    Image = None
```

### 3. Заглушки для тестирования

Созданы заглушки для компонентов с внешними зависимостями:
- `image_processor_stub.py` - для обработки изображений
- `image_server_stub.py` - для сервера изображений  
- `content_generator_stub.py` - для генератора контента

### 4. Единая точка входа

`main.py` предоставляет CLI интерфейс для всех компонентов:

```bash
python main.py gui                    # Графический интерфейс
python main.py web                    # Веб-интерфейс
python main.py server                 # Сервер изображений
python main.py process image.jpg      # Обработка файла
```

## 🚀 Использование

### Базовое использование без зависимостей

```python
from magic_uniq.config.settings import Settings
from magic_uniq.utils.file_utils import FileUtils

# Настройки
settings = Settings()
print(settings.get('server.host'))

# Файловые утилиты
FileUtils.ensure_directory('output')
is_image = FileUtils.is_image_file('test.jpg')
```

### С полными зависимостями

```python
from magic_uniq.core import ImageProcessor
from magic_uniq.core.effects.base import EffectChain

# Обработка изображений
processor = ImageProcessor()
result = processor.process_image('input.jpg', 'output.jpg')

# Цепочка эффектов
chain = EffectChain()
# chain.add_effect(some_effect)
```

## 🧪 Тестирование

Запустите тесты структуры:

```bash
python test_structure_simple.py
```

Все базовые компоненты должны импортироваться без внешних зависимостей.

## 📦 Установка зависимостей

Для полной функциональности установите зависимости:

```bash
# С Poetry
poetry install

# Или с pip
pip install -r requirements.txt
```

## 🔄 Миграция с старой структуры

### Импорты

Старые импорты:
```python
from uniqualizer import UniqualizerConfig
from MagicVideoSrc.video_uniquify import VideoUniquifier
```

Новые импорты:
```python
from magic_uniq.core.image_processor import UniqualizerConfig
from magic_uniq.core.video_processor import VideoUniquifier
```

### Запуск приложений

Старый способ:
```bash
python uniqualizer_gui.py
python MagicVideoSrc/web_gui.py
```

Новый способ:
```bash
python main.py gui
python main.py web
```

## 🛠️ Разработка

### Добавление новых эффектов

1. Создайте класс эффекта, наследующий от `BaseEffect`
2. Реализуйте метод `apply()`
3. Добавьте в соответствующий модуль эффектов

### Добавление новых интерфейсов

1. Создайте директорию в `interfaces/`
2. Реализуйте интерфейс
3. Добавьте команду в `main.py`

### Тестирование

Добавляйте тесты в соответствующие директории:
- `tests/unit/` - модульные тесты
- `tests/integration/` - интеграционные тесты

## 📋 TODO

- [ ] Полная миграция всех эффектов в новую структуру
- [ ] Создание полноценных тестов
- [ ] Документация API
- [ ] Примеры использования
- [ ] Оптимизация производительности
