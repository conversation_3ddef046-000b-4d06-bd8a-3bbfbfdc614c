#!/usr/bin/env python3
"""
MagicUniq - Единая точка входа

Главный скрипт для запуска всех компонентов системы MagicUniq.
Предоставляет CLI интерфейс для управления различными режимами работы.
"""

import sys
import argparse
from pathlib import Path

# Добавляем src в путь для импорта
sys.path.insert(0, str(Path(__file__).parent / "src"))

from magic_uniq.config.settings import Settings
from magic_uniq.config.validation import ConfigValidator


def create_parser() -> argparse.ArgumentParser:
    """Создать парсер аргументов командной строки."""
    parser = argparse.ArgumentParser(
        description="MagicUniq - Продвинутый инструмент уникализации видео и изображений",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  %(prog)s gui                    # Запустить графический интерфейс
  %(prog)s web                    # Запустить веб-интерфейс
  %(prog)s server                 # Запустить сервер изображений
  %(prog)s generator              # Запустить генератор контента
  %(prog)s process image.jpg      # Обработать изображение
  %(prog)s process video.mp4      # Обработать видео
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Доступные команды')
    
    # GUI команда
    gui_parser = subparsers.add_parser('gui', help='Запустить графический интерфейс')
    gui_parser.add_argument('--config', help='Путь к файлу конфигурации')
    
    # Web команда
    web_parser = subparsers.add_parser('web', help='Запустить веб-интерфейс')
    web_parser.add_argument('--host', default='localhost', help='Хост сервера')
    web_parser.add_argument('--port', type=int, default=5001, help='Порт сервера')
    web_parser.add_argument('--debug', action='store_true', help='Режим отладки')
    web_parser.add_argument('--config', help='Путь к файлу конфигурации')
    
    # Server команда
    server_parser = subparsers.add_parser('server', help='Запустить сервер изображений')
    server_parser.add_argument('--host', default='localhost', help='Хост сервера')
    server_parser.add_argument('--port', type=int, default=80, help='Порт сервера')
    server_parser.add_argument('--config', help='Путь к файлу конфигурации')
    
    # Generator команда
    generator_parser = subparsers.add_parser('generator', help='Запустить генератор контента')
    generator_parser.add_argument('--output', help='Директория для сохранения')
    generator_parser.add_argument('--count', type=int, default=100, help='Количество изображений')
    generator_parser.add_argument('--config', help='Путь к файлу конфигурации')
    
    # Process команда
    process_parser = subparsers.add_parser('process', help='Обработать файл')
    process_parser.add_argument('input', help='Входной файл для обработки')
    process_parser.add_argument('--output', help='Выходной файл')
    process_parser.add_argument('--config', help='Путь к файлу конфигурации')
    process_parser.add_argument('--preset', help='Имя пресета настроек')
    
    # Config команда
    config_parser = subparsers.add_parser('config', help='Управление конфигурацией')
    config_subparsers = config_parser.add_subparsers(dest='config_action')
    
    config_subparsers.add_parser('validate', help='Проверить конфигурацию')
    config_subparsers.add_parser('show', help='Показать текущую конфигурацию')
    config_subparsers.add_parser('reset', help='Сбросить к настройкам по умолчанию')
    
    # Version команда
    subparsers.add_parser('version', help='Показать версию')
    
    return parser


def run_gui(args):
    """Запустить графический интерфейс."""
    try:
        # Инициализируем настройки и создаем директории
        settings = Settings(args.config if hasattr(args, 'config') and args.config else None)
        settings.ensure_directories()

        from magic_uniq.interfaces.gui.main import main_uniq
        print("🖥️  Запуск графического интерфейса...")
        main_uniq()
    except ImportError as e:
        print(f"Ошибка импорта GUI: {e}")
        print("Убедитесь что установлены зависимости: pip install PyQt6")
        sys.exit(1)
    except Exception as e:
        print(f"Ошибка запуска GUI: {e}")
        sys.exit(1)


def run_web(args):
    """Запустить веб-интерфейс."""
    try:
        # Инициализируем настройки и создаем директории
        settings = Settings(args.config if hasattr(args, 'config') and args.config else None)
        settings.ensure_directories()

        from magic_uniq.interfaces.web.app import main_uniq
        print(f"🌐 Запуск веб-интерфейса на http://{args.host}:{args.port}")
        main_uniq(host=args.host, port=args.port)
    except ImportError as e:
        print(f"Ошибка импорта веб-интерфейса: {e}")
        print("Убедитесь что установлены зависимости: pip install flask aiohttp")
        sys.exit(1)
    except Exception as e:
        print(f"Ошибка запуска веб-интерфейса: {e}")
        sys.exit(1)


def run_server(args):
    """Запустить сервер изображений."""
    try:
        # Инициализируем настройки и создаем директории
        settings = Settings()
        settings.ensure_directories()

        from magic_uniq.services.image_server import ImageServer
        print(f"🖼️  Запуск сервера изображений на http://{args.host}:{args.port}")
        server = ImageServer()
        import asyncio
        asyncio.run(server.run_server())
    except ImportError as e:
        print(f"Ошибка импорта сервера: {e}")
        print("Убедитесь что установлены зависимости: pip install aiohttp")
        sys.exit(1)
    except Exception as e:
        print(f"Ошибка запуска сервера: {e}")
        sys.exit(1)


def run_generator(args):
    """Запустить генератор контента."""
    try:
        # Инициализируем настройки и создаем директории
        settings = Settings()
        settings.ensure_directories()

        from magic_uniq.services.content_generator import CombinedGenerator
        print(f"🎨 Запуск генератора контента...")
        print(f"Будет создано {args.count} изображений")
        if args.output:
            print(f"Сохранение в: {args.output}")

        # TODO: Загрузить аккаунты и промпты из конфигурации
        print("⚠️  Для работы генератора необходимо настроить аккаунты API")
        print("Генератор контента готов к запуску")
    except ImportError as e:
        print(f"Ошибка импорта генератора: {e}")
        print("Убедитесь что установлены зависимости: pip install requests")
        sys.exit(1)
    except Exception as e:
        print(f"Ошибка запуска генератора: {e}")
        sys.exit(1)


def run_process(args):
    """Обработать файл."""
    input_file = Path(args.input)
    if not input_file.exists():
        print(f"Ошибка: Файл {input_file} не найден")
        sys.exit(1)
    
    try:
        from magic_uniq.utils.file_utils import FileUtils
        
        if FileUtils.is_image_file(input_file):
            from magic_uniq.core.image_processor import ImageProcessor
            print(f"Обработка изображения: {input_file}")
            # TODO: Реализовать обработку изображения
        elif FileUtils.is_video_file(input_file):
            from magic_uniq.core.video_processor import VideoProcessor
            print(f"Обработка видео: {input_file}")
            # TODO: Реализовать обработку видео
        else:
            print(f"Ошибка: Неподдерживаемый тип файла: {input_file}")
            sys.exit(1)
            
    except Exception as e:
        print(f"Ошибка обработки файла: {e}")
        sys.exit(1)


def run_config(args):
    """Управление конфигурацией."""
    settings = Settings(args.config if hasattr(args, 'config') else None)
    
    if args.config_action == 'validate':
        validator = ConfigValidator()
        if validator.validate_config(settings.to_dict()):
            print("✅ Конфигурация валидна")
        else:
            print("❌ Найдены ошибки в конфигурации:")
            for error in validator.get_errors():
                print(f"  - {error}")
            
            if validator.has_warnings():
                print("\n⚠️  Предупреждения:")
                for warning in validator.get_warnings():
                    print(f"  - {warning}")
    
    elif args.config_action == 'show':
        import json
        print("Текущая конфигурация:")
        print(json.dumps(settings.to_dict(), indent=2, ensure_ascii=False))
    
    elif args.config_action == 'reset':
        settings.reset_to_defaults()
        settings.save_config()
        print("Конфигурация сброшена к настройкам по умолчанию")


def show_version():
    """Показать версию."""
    try:
        from magic_uniq import __version__
        print(f"MagicUniq версия {__version__}")
    except ImportError:
        print("MagicUniq версия неизвестна")


def main():
    """Главная функция."""
    parser = create_parser()
    
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    args = parser.parse_args()
    
    # Маршрутизация команд
    if args.command == 'gui':
        run_gui(args)
    elif args.command == 'web':
        run_web(args)
    elif args.command == 'server':
        run_server(args)
    elif args.command == 'generator':
        run_generator(args)
    elif args.command == 'process':
        run_process(args)
    elif args.command == 'config':
        run_config(args)
    elif args.command == 'version':
        show_version()
    else:
        parser.print_help()


if __name__ == '__main__':
    main()
